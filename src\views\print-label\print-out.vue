<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">出库单查询</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>

      <van-search v-model="searchVal" shape="round" background="#007aff" placeholder="出库单号" @search="getSearchList"
        left-icon="">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="getSearchList" style="color: #999999"></div>
        </template>
      </van-search>
      <van-field label="车牌号">
        <template #input>
          <input type="search" readonly placeholder="请查询车牌号" v-model="vehicleNo" />
        </template>
        <template #right-icon>
          <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE" @click="showVehicle = true"></span>
        </template>
      </van-field>
    </van-sticky>
    <div v-if="searchList && searchList.length != 0">
      <van-cell-group>
        <van-cell v-for="(item, index) in searchList" :key="index" @click="isChecked(index, item)">
          <template #title>
            <div class="load-number">{{ item.putoutId }}</div>
          </template>
          <template #label>
            <div class="load-name">{{ item.settleUserName }}</div>
          </template>
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <div v-else>
      <van-empty description="暂未查询到出库清单列表" />
    </div>
    <van-dialog v-model="showVehicle" :show-confirm-button="showBtn">
      <div>
        <van-search v-model="value" shape="round" background="#007aff" placeholder="请输入车牌号" @search="searchVehicleNo"
          left-icon="" :clearable="false">
          <template #right-icon>
            <div class="iconfont icon-sousuo" @click="searchVehicleNo" style="color: #999999"></div>
          </template>
        </van-search>
      </div>
      <div class="search-list">
        <div v-if="vehicleList && vehicleList.length > 0">
          <van-radio-group v-model="value">
            <van-cell-group>
              <van-cell v-for="(item, index) in vehicleList" :key="index" clickable @click="onCheck(index, item)">
                <template #title>
                  <div class="seg-title">{{ item }}</div>
                </template>
                <template #right-icon>
                  <van-radio :name="item">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-radio>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>

        <div class="tips-content" v-else>
          <div class="title">提示</div>
          <div class="msg">未查询到可选择的车牌！</div>
        </div>
      </div>
      <button class="search-btn" @click="onConfrimCarNumber">确认</button>
    </van-dialog>


    <!-- 选择出库单弹出框 -->
    <van-dialog v-model="showDelivery" :show-confirm-button="showDeliveryBtn">
      <div class="search-list">
        <van-checkbox-group v-model="deliveryChecked">
          <van-cell-group>
            <van-cell v-for="(item, index) in deliveryList" :key="index" @click="toggle(index)">
              <template #title>
                <div class="seg-title">{{ item }}</div>
              </template>
              <template #right-icon>
                <van-checkbox :name="item" :ref="(el) => (checkboxRefs[index] = el)" @click.stop />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>

      </div>
      <button class="search-btn btn-left"
      @click="checkAlertDelivery(deliveryChecked, deliveryChecked.join(','))">确认</button>
    </van-dialog>
  </div>
</template>

<script>
import HmInput from "@/components/input.vue";
import * as baseApi from "@/api/base-api";
import {
  Dialog,
} from "vant";
export default {
  name: "search-outstorage",
  data() {
    return {
      searchVal: "",
      vehicleNo: '',
      showVehicle: false,
      showBtn: false,
      currentDate: "",
      endDate: "",
      dateStart: "",
      dateEnd: "",
      searchList: [],
      vehicleList: [],
      currentIndex: -1,
      value: "",
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
      deliveryList: [],
      showDelivery: false,
      showDeliveryBtn: false,
      deliveryChecked: [],
      checkboxRefs: [],
    };
  },
  components: {
    HmInput,
  },
  created() {
    this.getSearchList();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.$router.togo("/printDetail?putoutId=" + item.putoutId);
    },
    onConfrimCarNumber() {
      this.value = "";
      this.showVehicle = false;
      this.getVehicleList();
    },
    onCheck(index, item) {
      this.value = item;
      this.currentIndex = index;
      this.vehicleNo = item;
    },
    /** 选择出库单 */
    toggle(index) {
      this.checkboxRefs[index].toggle();
    },
    // 确定打印
    checkAlertDelivery(list, result) {
      let that = this;
      if (window.plus) {
        // 在这里调用h5+ API
        plus.nativeUI.confirm(`出库单为${result}，是否打印?`, function (e) {
          if (e.index == 0) {
            that.printOut(list);
          } else {
            console.log("You clicked Cancel!");
          }
        }, "提示", ["确认", "取消"]);
      } else {
        Dialog.confirm({
          title: '提示',
          message: `出库单为${result}，是否打印?`,
        })
          .then(() => {
            // on confirm
            that.printOut(list);
          })
          .catch(() => {
          });
      }
      that.showDelivery = false;
    },
    //查询出库单列表
    async getSearchList() {
      const params = {
        serviceId: "S_UC_PR_230609",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        putoutId: this.searchVal,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.__sys__.status != -1) {
              this.searchList = dataObj.result;
              if (this.searchList.length == 0) {
                this.$toast("未查询到相应数据");
              }
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async searchVehicleNo() {
      const params = {
        serviceId: "S_UC_PR_200106",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        vehicleNo: this.value,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.__sys__.status != -1) {
              this.vehicleList = dataObj.result;
              if (this.vehicleList.length == 0) {
                this.$toast("未查询到相应数据");
              }
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async getVehicleList() {
      const params = {
        serviceId: "S_UC_PR_200107",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        vehicleNo: this.vehicleNo,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.__sys__.status != -1) {
              this.searchList = [];
              let arr = dataObj.result;
              this.deliveryList = arr;
              let result = arr.join(', ');
              if (this.deliveryList.length == 1) {
                this.checkAlertDelivery(arr, result);
              } else {
                this.showDelivery = true;
              }
            } else {
              this.searchList = [];
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //打印出库单
    async printOut(val) {
      const params = {
        serviceId: "S_UC_PR_230622",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putoutIdList: val,
      };
      await baseApi.baseService(params)
        .then((res) => {
          console.log(res);
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            this.$toast(res.data.__sys__.msg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.search-list {
  height: 180px;
  border: 1px solid #f2f2f2;
  background-color: #f2f2f2;
  overflow-y: auto;
  margin-bottom: 3.5em;

  // margin-bottom: 80px;
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 2px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}

.tips-content {
  height: 110px;
  margin: 15px;
  background-color: #fff;
  font-size: 14px;
  padding: 10px;
  border-radius: 10px;

  div {
    margin: 10px;
  }

  .title {
    text-align: center;
  }

  .msg {
    margin-top: 10px;
  }
}

.search-btn {
  position: absolute;
  bottom: 0px;
  width: 100%;
  height: 48px;
  background: #007aff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  color: #fff;
}

.activeColor {
  color: #007aff;
}

.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}

.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}

.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  margin-top: 2px;
}
</style>
