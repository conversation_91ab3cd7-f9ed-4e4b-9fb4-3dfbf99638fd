<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">入库单查询</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content">
       <van-field
        v-model="searchVal"
        label="入库单"
        class="all-font-size"
        clearable
        placeholder="入库单"
        @keyup.enter.native="onSearch"
      />
      <van-cell
        title="入库开始日期"
        class="all-font-size"
        :value="currentDate"
        @click="showCalendarStart"
      />
      <van-cell
      class="all-font-size"
        title="入库结束日期"
        :value="endDate"
        @click="showCalendarEnd"
      />
      <div class="search-list" v-if="searchList && searchList.length != 0">
        <!-- <div
          class="load-cell"
          v-for="(item, index) in searchList"
          :key="index"
          @click="goDetail(item)"
        >
          <div class="load-number">{{ item.putinId }}</div>
          <div class="load-name">总数量：{{ item.totalQty }}</div>
          <div class="load-name">客：{{ item.settleUserName }}</div>
        </div> -->
        <van-cell-group v-for="(item, index) in searchList" :key="index">
          <van-cell @click="goDetail(item)">
            <!-- 使用 title 插槽来自定义标题 -->
            <template #title>
              <span class="load-number">{{ item.putinId }}</span>
            </template>
            <template #label>
              <span class="load-name">{{ item.settleUserName }}</span>
            </template>
            <template #right-icon>
              <span
                class="iconfont icon-youjiantou"
                style="color: #9f9f9f"
              ></span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div v-else>
        <van-empty description="暂未查询到清单" />
      </div>
      <calendar
        @change="onChange"
        :show.sync="showStart"
        :weekNames="weekList"
      />
      <calendar
        @change="onChange2"
        :show.sync="showEnd"
        :weekNames="weekList"
      />
    </div>
    <div
      class="mui-input-row"
      style="margin: 0"
      @click="onSearch"
      v-show="isShowBottom"
    >
      <button type="button" class="mui-btn">
        查&nbsp; &nbsp; &nbsp;&nbsp;询
      </button>
    </div>
  </div>
</template>

<script>
import HmInput from "@/components/input.vue";
import * as baseApi from "@/api/base-api";
export default {
  name: "search-instorage",
  data() {
    return {
      searchVal: "",
      currentDate: "",
      endDate: "",
      dateStart: "",
      dateEnd: "",
      showStart: false,
      showEnd: false,
      weekList: ["一", "二", "三", "四", "五", "六", "日"],
      searchList: [],
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  components: {
    HmInput,
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onChange(date) {
      this.currentDate = date.format("YYYY-MM-DD");
      this.dateStart = date.format("YYYYMMDDHHmmss");
    },
    onChange2(date) {
      this.endDate = date.format("YYYY-MM-DD");
      this.dateEnd = date.format("YYYYMMDD") + "235959";
    },
    onClickLeft() {
      this.$router.goBack();
    },
    showCalendarStart() {
      //this.show = true;
      this.showStart = true;
    },
    showCalendarEnd() {
      this.showEnd = true;
    },
    goDetail(item) {
      this.$router.togo("/searchDetail?putinId=" + item.putinId);
    },
    //查询入库单列表
    async getSearchList() {
      const params = {
        serviceId: "S_UC_PR_230607",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        putinId: this.searchVal,
        createTimeStart: this.dateStart,
        createTimeEnd: this.dateEnd,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.searchList = dataObj.result;
              if (this.searchList.length == 0) {
                this.$toast("未查询到相应数据");
              }
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onSearch() {
      if (this.currentDate && this.endDate) {
        if (this.checkTime() < 0) {
          this.$toast("结束时间不能小于开始时间");
        } else {
          this.getSearchList();
        }
      } else {
        this.getSearchList();
      }
    },
    getDate(date) {
      var dates = date.split("-");
      var dateReturn = "";
      for (var i = 0; i < dates.length; i++) {
        dateReturn += dates[i];
      }
      return dateReturn;
    },
    checkTime() {
      return this.getDate(this.endDate) - this.getDate(this.currentDate);
    },
  },
};
</script>

<style lang="less" scoped>
.search-list {
  width: 100%;
  height: 300px;
  margin-top: 6px;
  // margin: 8px;
  border: 1px solid #dcdcdc;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}
.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  margin-top: 2px;
}
</style>
