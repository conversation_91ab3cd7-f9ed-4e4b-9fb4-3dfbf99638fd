<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">出库单列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content" v-if="carList && carList.length > 0">
      <van-cell title="捆包扫描校验" to="baleScan" class="cell-title">
        <template #right-icon>
          <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
        </template>
      </van-cell>
      <van-swipe-cell v-for="(item, index) in carList" :key="index">
        <van-cell>
          <template #title>
            <div class="load-number">{{ item.packId }}</div>
            <div class="load-name">
              数量：{{ item.quantity }}&nbsp; &nbsp; &nbsp;&nbsp; 毛重：{{
                item.grossWeight
              }}{{ item.measureId }}
            </div>
            <div class="load-name">{{ item.prodCname }}{{ item.specDesc }}</div>
          </template>
        </van-cell>
        <template #right>
          <van-button
            square
            type="danger"
            text="删除"
            class="delete-bale-btn"
            @click="deleteItem(index)"
          />
        </template>
      </van-swipe-cell>
      <!-- 扫描捆包列表 -->
    </div>
    <div v-else>
      <van-empty description="该车没有出库列表" />
    </div>
    <!-- <van-button @click="showtest">tess</van-button> -->
    <HmPopup
      v-if="show"
      @closetip="showtest()"
      @checkInfo="getCheckInfo"
      :arr="testList"
    ></HmPopup>
    <div
      class="mui-input-row"
      style="margin: 0"
      @click="leaveFactory"
      v-show="isShowBottom"
      v-preventReClick="3000"
    >
      <button id="block_button" type="button" class="mui-btn">出厂验货</button>
    </div>
  </div>
</template>

<script>
import HmInput from "@/components/input.vue";
import HmPopup from "@/components/HmPopup.vue";
import * as baseApi from "@/api/base-api";
let previousRouterName = "";
let scanInfo = [];
export default {
  data() {
    return {
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
      packId: "",
      show: false,
      fathermsg: "",
      timer: null,
      carList: [],
      vehicleNo: "",
    };
  },
  beforeRouteEnter(to, from, next) {
    previousRouterName = from.name;
    if (from.name === "bale-scan") {
      scanInfo = from.params.packList;
    } else {
      scanInfo = [];
    }
    next();
  },
  created() {
    this.vehicleNo = this.$route.query.vehicleNo;
    this.getOutList();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  components: {
    HmInput,
    HmPopup,
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goInList() {
      this.$router.togo("/inlist");
    },
    handerSearch() {
      this.getBaleByPackId();
    },
    getCheckInfo(val) {
      console.log("接收到了传过来的值", val);
	    this.show = !this.show;
    },
    showtest() {
      console.log("点击了关闭弹窗");
      this.show = !this.show;
    },
    deleteItem(index) {
      //删除数组中值
      this.$delete(this.carList, index); //没效果？
    },
    async getOutList() {
      const params = {
        serviceId: "S_UC_PR_230605",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        vehicleNo: this.vehicleNo,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.carList = dataObj.result;
              // console.log(dataObj);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async leaveFactory() {
      // const chekVal = this.carList.flat().map((detail2) => {
      //   return {
      //     packId: detail2.packId,
      //     measureId: detail2.measureId,
      //     netWeight: detail2.netWeight,
      //     grossWeight: detail2.grossWeight,
      //     specDesc: detail2.specDesc,
      //     prodCode: detail2.prodCode,
      //     prodCname: detail2.prodCname,
      //   };
      // });
      const params = {
        serviceId: "S_UC_PR_230509",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        vehicleNo: this.vehicleNo,
        rowList: this.carList,
        rowList2: scanInfo,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              //console.log("---", dataObj.result);
              this.$toast(dataObj.__sys__.msg);
               setTimeout(() => {
                  this.$router.toReplace("/leaveBale");
                }, 2000);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.span-count {
  margin-left: 10px;
  color: #0000ff;
}
.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  //margin-top: 8px;
}
.delete-bale-btn {
  height: 92px;
}
.cell-title {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333;
}
</style>
