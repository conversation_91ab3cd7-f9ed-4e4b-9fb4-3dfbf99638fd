<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">班组班次</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="">
      <div class="group-content">
        <div class="group-flex">
          <div class="group-line"></div>
          <div class="group-title">班组：</div>
        </div>
        <div class="btn-group">
          <button class="group-btn" v-for="(item, index) in groupVal" :key="index" @click="getGroup(item)"
            :class="{ groupActive: item.id == isActive1 }">
            {{ item.groupName }}
          </button>
        </div>
      </div>

      <div class="group-content">
        <div class="group-flex">
          <div class="group-line"></div>
          <div class="group-title">班次：</div>
        </div>
        <div class="btn-group2">
          <button class="class-btn" v-for="(item, index) in classVal" :key="index" @click="getClass(item)"
            :class="{ classActive: item.id == isActive2 }">
            {{ item.className }}
          </button>
        </div>
      </div>
      <div class="group-choose2">
        <span class="choose-text1">已选择:</span>
        <div class="span-div">
          <span class="choose-text2">班组</span>
          <span class="check-text">{{ groupName }}</span>
        </div>
        <div class="span-div">
          <span class="choose-text2">班次</span>
          <span class="check-text">{{ className }}</span>
        </div>
      </div>
    </div>
    <div class="mui-input-row" style="margin: 0" @click="onConfirm">
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;定
      </button>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty
  } from '@/utils/tools';
  export default {
    data() {
      return {
        groupVal: [{
            id: 10,
            groupName: "甲班"
          },
          {
            id: 20,
            groupName: "乙班"
          },
          {
            id: 30,
            groupName: "丙班"
          },
          {
            id: 40,
            groupName: "丁班"
          },
        ],
        classVal: [{
            id: 10,
            className: "早"
          },
          {
            id: 20,
            className: "中"
          },
          {
            id: 30,
            className: "晚"
          },
        ],
        groupName: "",
        className: "",
        isActive1: 0,
        isActive2: 0,
      };
    },
    methods: {
      async onConfirm() {
        if (isEmpty(this.groupName)) {
          this.$toast("请选择班组！");
          return;
        }
        if (isEmpty(this.className)) {
          this.$toast("请选择班组次！");
          return;
        }
        const params = {
          serviceId: "S_UC_PR_200003",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          teamId: this.isActive1,
          workingShift: this.isActive2,
        };
        await baseApi.baseService(params)
          .then((res) => {
            console.log("res==", res.data);
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                localStorage.setItem("teamName", this.groupName);
                localStorage.setItem("shiftName", this.className);
                localStorage.setItem("groupVal", this.isActive1);
                localStorage.setItem("classVal", this.isActive2);
                this.$toast(dataObj.__sys__.msg);
                setTimeout(() => {
                  this.$router.toReplace("/");
                }, 2000);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onClickLeft() {
        this.$router.goBack();
      },
      getGroup(item) {
        this.isActive1 = item.id;
        this.groupName = item.groupName;
      },
      getClass(item) {
        this.isActive2 = item.id;
        this.className = item.className;
      },
    },
  };
</script>

<style lang="less" scoped>
  .btn-group {
    margin-top: 14px;
    display: flex;
    justify-content: space-between;
  }

  .btn-group2 {
    margin-top: 14px;
  }

  .btn-span {
    letter-spacing: 10px;
  }

  .group-btn {
    width: 20%;
    height: 32px;
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #666666;
    line-height: 20px;
    background: #ebebeb;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid #999999;
  }

  .groupActive {
    width: 20%;
    height: 32px;
    background: #007aff;
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #e7f3ff;
    line-height: 20px;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid #007aff;
  }

  .class-btn {
    width: 98%;
    height: 40px;
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #666666;
    line-height: 22px;
    margin-bottom: 4px;
    background: #ebebeb;
    border-radius: 4px 4px 0px 0px;
    opacity: 1;
  }

  .classActive {
    width: 98%;
    height: 40px;
    font-size: 18px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #e7f3ff;
    line-height: 22px;
    background: #007aff;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
  }

  .group-choose {
    margin-left: 16px;
  }
</style>
