<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">盘库单</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div>
      <van-cell title="无单盘库" to="withoutStorage" class="all-font-size">
        <template #right-icon>
          <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
        </template>
      </van-cell>
    <van-cell title="选择盘库单" to="checkList"  class="all-font-size">
        <template #right-icon>
          <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
        </template>
      </van-cell>
  <!--  <van-cell title="测试map" to="checkMapDemo"  class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell> -->
      <!-- <van-button @click="testUrl">测试按钮</van-button> -->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  created(){
   // window.android.postMessage('test');
  },
  // mounted() {
  //   // 原生app调用h5方法，iosToH5是methods中定义的方法名。
  //   const vm = this;
  //   this.$bridge.registerhandler("iosToH5", (data, responseCallback) => {
  //     // data是原生app传递给h5的参数
  //     console.log('data,',data);
  //   });
  // },
  methods: {
    onClickLeft() {
      // this.$router.goBack();
      this.$router.replace('/');
    },
    testUrl() {
       //this.$router.togo('/check')
        window.location.href = 'http://localhost:8081/#/check';

    },

  },
};
</script>

<style lang="less" scoped>

</style>
