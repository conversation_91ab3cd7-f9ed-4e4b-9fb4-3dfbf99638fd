<template>
  <div>
  <!--  <van-nav-bar>
      <template #title>
        <div class="global-hfont">出库扫描</div>
      </template>
    </van-nav-bar>
 -->
    <div class="">
      <van-form>
        <van-field v-model="storeNumber" label="捆包" ref="baleRef" class="all-font-size" clearable placeholder="请输入或扫描捆包号"
          @keyup.enter.native="getBaleByPackId" :required="true" />
        <van-field label="车牌号" placeholder="车牌号"  class="all-font-size" v-model="carNumber" readonly />

        <div class="detail_row">
          <div class="fourtext">已/未扫</div>
          <div style="width: 80%">

            <input class="weight_input" type="text" v-model="scanned" readonly />
            <input class="weight_input" type="text" v-model="notScanned" readonly />
          </div>
        </div>
         <van-field label="规格" placeholder="规格"  class="all-font-size" v-model="baleList.specsDesc" readonly />
        <div class="detail_row">
          <div class="fourtext">重/件</div>
          <div style="width: 80%">

            <input class="weight_input" type="text" v-model="baleListNetWeight" readonly />
            <input class="weight_input" type="text" v-model="baleListPieceNum" readonly />
          </div>
        </div>
        <div class="detail_row">
          <div class="fourtext">总重/件</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" v-model="allNetWeight" readonly />
            <input class="weight_input" type="text" v-model="allPieceNum" readonly />
          </div>
        </div>
        <div class="detail_row">
          <div class="fourtext">总捆包</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" v-model="allPackQty" readonly />
          </div>
        </div>
        <van-field v-model="storeName" class="all-font-size" label="仓库" readonly />
        <van-field  v-show="remark" label="备注"  type="textarea" placeholder="备注"  class="all-font-size" v-model="remark"  autosize readonly />
      </van-form>
    </div>
    <!-- <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" @click="onSave">
      <button type="button" class="mui-btn">
        保&nbsp; &nbsp; &nbsp;&nbsp;存
      </button>
    </div> -->
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  </div>
</template>

<script>
  import HmInput from "@/components/input.vue";
  import HmPopup from "@/components/HmPopup.vue";
  import * as baseApi from "@/api/base-api";
  export default {
    props: [
      "carNumber",
      "showInfo",
      "ladingBillIdList",
      "allNetWeight",
      "allPieceNum",
      "baleListNetWeight",
      "baleListPieceNum",
      "allPackQty",
      'scanned',
      'notScanned',
      "remark",
      "packIdListString",
      "packScanList"
    ],
    data() {
      return {
        storeNumber: "",
        value: "",
        carNumber2: "",
        storeName: "",
        searchValue: "",
        show: false,
        showBtn: false,
        chooseList: [],
        baleList: {},
        testList: [],
        showMsg: false,
        currentIndex: -1,
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    components: {
      HmInput,
      HmPopup,
    },
    created() {
      this.storeName = localStorage.getItem("warehouseName");
      this.baleList = {};
      this.$nextTick(() => {
        this.$refs.baleRef.focus();
      });
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    watch: {
      showInfo(oldValue, newValue) {
        if (oldValue) {
          this.baleList = {};
          this.chooseList = [];
          this.storeNumber = "";
          this.testList = [];
        }
      },
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      clearList(){
        this.baleList = {};
        this.chooseList = [];
        this.storeNumber = "";
        this.testList = [];
      },
      showtest() {
        console.log("点击了关闭弹窗");
        this.show = !this.show;
      },
      getCheckInfo(val) {
        console.log("接收到了传过来的值", val);
		 this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.storeNumber = this.baleList.packId;
        this.onSave();
      },
      handerSearch() {
        this.getBaleByPackId();
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_230602",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          packIdListString:this.packIdListString,
          packId: this.storeNumber,
          ladingBillIdList: this.ladingBillIdList,
          packList:this.packScanList,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                 this.storeNumber =  dataObj.packId;
                if (dataObj.result.length > 1) {
                  this.testList = dataObj.result;
                  this.show = !this.show;
                } else {
                  this.chooseList = dataObj.result;
                  this.baleList = {
                    ...dataObj.result[0]
                  };
                  this.onSave();
                }
               
              } else {
                this.storeNumber = res.data.packId;
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onSave() {
        console.log(this.chooseList);
        if (this.chooseList.length > 0) {
          this.$emit("scanInfo", this.chooseList, this.baleList.putoutType);
          this.clearList();
        } else {
          this.$toast("未查到捆包信息！");
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .van-nav-bar {
    background-color: #007aff;
    width: 100%;
    height: 42px;

    /deep/ .van-nav-bar__title {
      color: white;
    }

    /deep/ .van-icon {
      color: white;
    }
  }

  .search-list {
    height: 200px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
  }

  .search-btn {
    position: absolute;
    bottom: 0px;
    width: 320px;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    color: #fff;
  }

  .car-list {
    height: 30px;
    margin: 5px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
  }

  .store-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .tips-content {
    height: 110px;
    margin: 15px;
    background-color: #fff;
    font-size: 14px;
    padding: 10px;
    border-radius: 10px;

    div {
      margin: 10px;
    }

    .title {
      text-align: center;
    }

    .msg {
      margin-top: 10px;
    }
  }
</style>
