<template>
    <div class="div-main">
        <van-nav-bar>
            <template #title>
                <div class="global-hfont">实绩捆包打印</div>
            </template>
            <template #left>
                <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
            </template>
        </van-nav-bar>

        <!-- 进入页面光标停留输入框，扫描后自动跳转到捆包 -->
        <div class="">
            <van-cell is-link size="large" title="机组" :required="true" :value="machineTeamId" @click="showUnitClick();" />
            <van-action-sheet v-model="showUnit" :actions="actions" cancel-text="取消" description="选择机组"
                @select="selectMachine($event);" close-on-click-action />

            <van-field v-model="processOrderCode" label="工单" right-icon="search" placeholder="选择工单" input-align="right"
                onClick="this.select();" :required="true" @click-right-icon="showSelectOrder" />
            <van-action-sheet v-model:show="showPicker" :actions="orderActions" :close-on-click-action="true"
                @select="selectOrder" />


            <h2 class="van-doc-demo-block__title">
                捆包信息
                <div class="icon-sort">
                    <van-icon name="checked" @click="checkAll();"
                        :class="allPackChecked ? 'van-icon-checked' : 'van-icon-check'" />
                </div>
            </h2>

            <!-- <van-cell-group inset>
                <van-cell v-for="(item, index) in packList" :key="index">
                    <template #title>
                        <div>
                            <van-tag :type="item.processResultStatus == '10' ? 'success' : 'primary'">
                                {{ item.statusName }}
                            </van-tag>
                        </div>
                        <span class="custom-title">{{ `${item.packId} ${item.specsDesc}` }}</span>
                    </template>
                    <template #label>
                        <span class="custom-title">
                            {{ `${item.quantity}张, 净重: ${item.netWeight}, 毛重: ${item.grossWeight}` }}
                        </span>
                    </template>
                </van-cell>
            </van-cell-group> -->

            <van-checkbox-group v-model="checked" ref="packCheckedRef">
                <van-cell-group inset>
                    <van-cell v-for="(item, index) in packList" clickable :key="index" @click="toggle(index)">
                        <template #title>
                            <div>
                                <van-tag :type="item.processResultStatus == '10' ? 'success' : 'primary'">
                                    {{ item.statusName }}
                                </van-tag>
                            </div>
                            <span>{{ `${item.packId} ${item.specsDesc}` }}</span>
                        </template>
                        <template #label>
                            <span>
                                {{ `${item.quantity}张, 净重: ${item.netWeight}, 毛重: ${item.grossWeight}` }}
                            </span>
                        </template>
                        <template #right-icon>
                            <van-checkbox :name="item" :ref="(el) => (checkboxRefs[index] = el)" @click.stop />
                        </template>
                    </van-cell>

                </van-cell-group>
            </van-checkbox-group>

        </div>
        <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
            <button id="block_button" type="button" class="mui-btn" @click="saveBale" v-preventReClick="3000">远程打印</button>
        </div>
    </div>
</template>
  
<script>
import * as baseApi from "@/api/base-api";
import * as TransferService from "../../api/transfer-receipt-api";
import {
    Dialog, Notify
} from "vant";
export default {
    data() {
        return {
            machineTeamId: '',
            machineTeamCode: '',
            machineList: [],
            showUnit: false,
            actions: [],
            processOrderCode: '',
            showPicker: false,
            orderActions: [],
            processOrderList: [],
            isShowBottom: true, //显示或者隐藏footer
            documentHeight: document.documentElement.clientHeight,
            contractSubNum: '',
            order: {},
            segNo: localStorage.getItem('segNo'),
            segName: localStorage.getItem('segName'),
            processCategory: '',
            packList: [],
            oldMachineCode: '',
            oldOrderId: '',
            allPackChecked: false,
            checkboxRefs: [],
            checked: [],
        };
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                if (this.documentHeight > document.documentElement.clientHeight) {
                    this.isShowBottom = false;
                } else {
                    this.isShowBottom = true;
                }
            })();
        };
    },
    methods: {
        onClickLeft() {
            this.$router.goBack();
        },
        // 选中机组
        selectMachine(data) {
            if (this.oldMachineCode != data.name) {
                this.processOrderCode = '';
                this.packList = [];
            }
            this.oldMachineCode = data.name;
            this.machineTeamId = `${data.name}-${data.subname}`;
            this.machineTeamCode = data.name;
            this.processCategory = this.machineList.find(m => m.machineCode == data.name).processCategory;
        },

        /** 选中捆包 */
        toggle(index) {
            this.checkboxRefs[index].toggle();
            setTimeout(() => {
                this.allPackChecked = this.checked.length == this.packList.length;
            }, 0);
        },

        /** 捆包全选 */
        checkAll() {
            this.allPackChecked = !this.allPackChecked;
            this.$refs.packCheckedRef.toggleAll(this.allPackChecked);
        },
        // 查询机组
        async showUnitClick() {
            this.showUnit = true;
            this.actions = [{ name: "", loading: true }];
            const map = new Map();
            map.set("segNo", this.segNo);
            map.set("unitCode", this.segNo);
            map.set("segName", this.segName);

            const result = await TransferService.getMachineTeamList(map);
            if (!result || !result.__sys__ || !result.__sys__.status == "-1") {
                this.actions = [];
                Notify({
                    type: "warning",
                    message: result.data.__sys__.msg,
                });
                return;
            }

            if (!result.__blocks__.result2 || !result.__blocks__.result2.rows || result.__blocks__.result2.rows.length == 0) {
                Notify({
                    type: "danger",
                    message: "未查询到机组数据",
                });
                this.actions = [];
                return;
            }
            this.machineList = TransferService.arrayToArrayObj(
                result.__blocks__.result2.meta.columns,
                result.__blocks__.result2.rows
            );
            this.actions = this.machineList.map((data) => {
                return {
                    name: data.machineCode,
                    subname: data.machineName,
                };
            });
        },

        /** 显示下拉框, 选择工单 */
        async showSelectOrder() {
            if (!this.machineTeamCode) {
                Dialog.alert({ message: '请先选择机组' })
                return;
            }

            this.showPicker = true;
            this.actions = [
                {
                    name: "",
                    loading: true,
                },
            ];
            const map = new Map();
            map.set("segNo", this.segNo);
            map.set("unitCode", this.segNo);
            map.set("processOrderId", this.processOrderCode);
            map.set("machineCode", this.machineTeamCode);
            map.set("processDemandProcessStatus", '30'); // 启动
            map.set("processCategory", this.processCategory);
            const result = await TransferService.getList(map);
            this.processOrderList = result;
            this.orderActions = this.processOrderList.map((po) => {
                return {
                    name: po.processOrderId,
                };
            });
            if (this.orderActions.length == 0) {
                this.showPicker = false;
            }
            const currentPOC = this.processOrderCode;
            this.orderActions = this.orderActions.map((action) =>
                action.name == currentPOC
                    ? { ...action, color: "#007aff" }
                    : { name: action.name }
            );
        },

        /** 选中工单 */
        async selectOrder(value) {
            if (!value || !value.name) {
                return;
            }
            if (this.oldOrderId != value.name) {
                this.packList = [];
            }
            this.oldOrderId = value.name;
            this.processOrderCode = value.name;
            this.order = this.processOrderList.find(
                (po) => po.processOrderId == value.name
            );
            // 查询工单下的产出捆包信息
            await this.getOutputPackList();
        },

        /** 查询产出捆包 */
        async getOutputPackList() {

            let result = await TransferService.getOutputPackListToOrderId(this.order);
            if (!result || result.length == 0) {
                return;
            }
            result = result.map(r => {
                let statusName = '';
                switch (r.processResultStatus) {
                    case '10':
                        statusName = '实绩确认'
                        break;

                    default:
                        statusName = '新增';
                        break;
                }
                return {
                    ...r,
                    netWeight: Number(r.netWeight).toFixed(0),
                    grossWeight: Number(r.grossWeight).toFixed(0),
                    statusName,
                }
            });
            this.packList = result;
        },

        async saveBale() {
            if (!this.checked || this.checked.length == 0) {
                Dialog.alert({ message: '请选择捆包' });
                return;
            }

            const notStatusList = this.checked.filter(p => p.processResultStatus != '10');
            if (notStatusList.length > 0) {
                Dialog.alert({ message: `捆包号: ${notStatusList.map(n => n.packId).join(',')}请实绩确认。` });
                return;
            }

            if (this.checked.length < 11) {
                const params = {
                    serviceId: "S_UC_PR_230655",
                    userId: localStorage.getItem("userId"),
                    accessToken: localStorage.getItem("accessToken"),
                    segNo: localStorage.getItem("segNo"),
                    packIdList: this.checked.map(p => p.packId),
                    machineCode: this.processCategory,
                };
                const result = await baseApi.baseService(params);
                let dataObj = {
                    __sys__: {
                        msg: "调用失败",
                        status: -1,
                    },
                };
                if (!result || !result.data) {
                    this.$toast(res.data.__sys__.msg);
                    return;
                }
                dataObj = result.data;
                this.$toast(res.data.__sys__.msg);
                return;
            }
            // 超过10条数据，循环去调用防止超时
            let params = {
                serviceId: "S_UC_PR_230655",
                userId: localStorage.getItem("userId"),
                accessToken: localStorage.getItem("accessToken"),
                segNo: localStorage.getItem("segNo"),
                machineCode: this.processCategory,
            };

            let successPackList = [];
            for (const pack of this.checked) {
                let packId = pack.packId;
                params['packIdList'] = [packId];
                let result = await baseApi.baseService(params);
                let dataObj = {
                    __sys__: {
                        msg: "调用失败",
                        status: -1,
                    },
                };
                if (!result || !result.data) {
                    this.$toast(dataObj);
                    return;
                }
                successPackList.push(packId);
            }
            const successNum = successPackList.length;
            const packNum = this.checked.length;
            this.$toast(`${successPackList.length}条数据处理成功。${successNum == packNum ? '' : `还剩余${packNum - successNum}条数据。`}`);
        },
    },
};
</script>
  
<style lang="less" scoped>
.div-main {
    height: unset;
    background-color: rgba(243, 243, 243, 1);
}

.store-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
}

.cell-group {
    margin-top: 8px;
}

.van-cell-group {
    margin-top: 1em;
}

.van-doc-demo-block__title {
    margin: 0;
    padding: 16px 16px 5px 16px;
    color: rgba(69, 90, 100, .6);
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
}

h2 {
    display: flex;
    justify-content: space-between;

    div.icon-sort {
        font-size: 25px;
    }
}

.van-icon-checked {
    color: #1296db;
}

.van-icon-check {
    color: unset;
}
</style>
  