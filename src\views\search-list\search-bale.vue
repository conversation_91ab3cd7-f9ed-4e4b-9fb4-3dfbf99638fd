<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">捆包查询</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuoji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>

      <van-field
        v-model="searchVal"
        @keyup.enter.native="getBaleByPackId"
        center
        clearable
        class="all-font-size"
        label="捆包号"
        placeholder="请输入捆包号"
      >

        <template #button>
          <van-button size="small" type="info" class="all-font-size" @click="getBaleByPackId"
            >查询</van-button
          >
        </template>
      </van-field>
    </van-sticky>
    <div class="in-content" v-if="searchList && searchList.length != 0">
      <!-- 扫描捆包列表 -->
      <div style="text-align: left">
        <div
          class="detail_textarea"
          v-for="(item, index) in searchList"
          :key="index"
        >
          <div class="border_top">
            <div class="content_div">
              <div class="check-spec">标签号：{{ item.labelId }}</div>
            </div>
            <div class="content_div">
              <div class="check-val">捆包：{{ item.packId }}</div>
            </div>
            <div class="content_div">
              <div class="check-val">提单号：{{ item.entityBillId }}</div>
            </div>
            <div class="content_div">
              <div class="check-spec">规格：</div>
              <div class="check-val">{{ item.specDesc }}</div>
            </div>
            <div class="content_div">
              <div class="check-spec">数量/件数：</div>
              <div class="check-val">
                {{ item.quantity }}/{{ item.pieceNum }}
              </div>
            </div>
            <div class="content_div" >
              <div class="check-spec">库位编码：</div>
              <div class="check-val" >
                {{ item.locationId }}
              </div>
            </div>
            <div class="content_div" >
              <div class="check-spec">库存状态：</div>
              <div class="check-val status-color" >
                {{ item.instockFlagName }}
              </div>
            </div>
            <div class="content_div" >
              <div class="check-spec">锁定状态：</div>
              <div class="check-val status-color" >
                {{ item.blockadeTypeName }}
              </div>
            </div>
            <div class="content_div">
              <div class="check-spec">库位名称：</div>
              <div class="check-val">
                {{ item.locationName }}
              </div>
            </div>
            <div class="content_div">
              <div class="check-spec">毛重/净重：</div>
              <div class="check-val">
                {{ item.grossWeight }}/{{ item.netWeight }}
              </div>
            </div>
            <div class="content_div">
              <div class="check-spec">仓库：</div>
              <div class="check-val">{{ item.warehouseName }}</div>
            </div>
            <div class="content_div">
              <div class="check-spec">客户：</div>
              <div class="check-val">{{ item.customerName }}</div>
            </div>
            <div class="content_div">
              <div class="check-spec">车辆：</div>
              <div class="check-val">{{ item.vehicleCode }}</div>
            </div>
             <van-divider></van-divider>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无捆包清单" />
    </div>
  </div>
</template>

<script>
import { Dialog } from "vant";
import * as baseApi from "@/api/base-api";
import HmInput from "@/components/input.vue";
export default {
  data() {
    return {
      searchVal: "",
      searchList: [],
    };
  },
  components: {
    HmInput,
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goInList() {
      this.$router.togo("/inlist");
    },
    async getBaleByPackId() {
      const params = {
        serviceId: "S_UC_PR_200004",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        packId: this.searchVal,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj) {
              this.searchList = dataObj.result;
              this.searchVal = dataObj.packId;
              if (this.searchList.length == 0) {
                 this.searchVal = dataObj.packId;
                this.$toast("未查询到相应捆包数据");
              }
            } else {
               this.searchVal = dataObj.packId;
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
 /deep/ .van-field__label {
      -webkit-box-flex: 0;
      -webkit-flex: none;
      flex: none;
      box-sizing: border-box;
      width: 3.1em;
      margin-right: 12px;
      color: #646566;
      text-align: left;
      word-wrap: break-word;
  }
.span-count {
  margin-left: 10px;
  color: #0000ff;
}
.search-out-btn {
  width: 18%;
  padding: 5px;
  background: #007aff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;
}
.status-color{
  color: #0000ff;
}
</style>
