<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">并包生成捆包清单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
        <!-- <template #right>
         <span @click="onClickRight" class="title-add">添加</span>
       </template> -->
      </van-nav-bar>
    </van-sticky>

    <!-- 进入页面光标停留输入框，扫描后自动跳转到捆包 -->
    <div class="">
      <van-form>
        <van-field label="捆包" @keyup.enter.native="getBaleByPackId" class="all-font-size">
          <template #input>
            <input type="search" class="new-field" placeholder="请输入或扫描捆包号" ref="baleref" v-model="bale" />
          </template>
        </van-field>
    <!--    <van-field label="并包号" placeholder="并包号" v-model="baleList.unitedPackId" class="all-font-size" readonly /> -->
        <van-field label="规格" placeholder="规格" v-model="baleList.specsDesc" class="all-font-size" readonly />
      <!--  <van-field label="毛重" placeholder="毛重" v-model="baleList.grossWeight" class="all-font-size" readonly />
        <div class="detail_row">
          <div class="fourtext">重/件</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" v-model="baleList.netWeight" readonly />
            <input class="weight_input" type="text" v-model="baleList.pieceNum" readonly />
          </div>
        </div> -->
        <van-field v-model="storeName" label="仓库" class="all-font-size" readonly />
      </van-form>
    </div>

    <van-tabs v-model="active" color="#007aff" style="width: 100%" line-width="60px" offset-top="44px"
      title-active-color="#007aff" sticky>
      <van-tab title="捆包列表" :title-style="fontSize">
        <div v-if="packList && packList.length > 0">
          <div class="inlist-content">
            <div class="detail_textarea">

              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  已扫捆包合计 :
                  <span class="span-count">{{ packList.length }}</span>
                </div>
              </div>
              <div style="display: flex; justify-content: flex-end;">
                <van-button size="small" type="default" @click="checkAll">
                  <span class="">全选</span>
                </van-button>
                <van-button size="small" type="info" @click="toggleAll">
                  <span>反选</span>
                </van-button>
              </div>
              <!-- <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  总件数 :
                  <span class="span-count">{{ totalPieceNum }}</span>
                  总吨数 :
                  <span class="span-count">{{
                    formatNumber(totalNetWeight)
                  }}</span>
                </div>
              </div> -->
            </div>

            <!--  <van-cell ref="checkAll" title="全选" class="all-font-size" clickable >
              <template #right-icon>
                <van-checkbox v-model="checkedAll" ref="checkboxes" @click="checkAll">
                  <template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-checkbox>
              </template>
            </van-cell> -->
            <div class="content-cell">
              <van-checkbox-group v-model="result" ref="checkboxGroup">
                <van-cell-group class="cell-group" v-for="(item, index) in packList" clickable :key="index">
                  <van-swipe-cell>
                    <van-cell @click="toggle(index)">
                      <template #title>

                        <div class="div-flex">
                          <div>
                            <van-checkbox :name="item" ref="checkboxes">
                              <template #icon="props">
                                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                              </template>
                            </van-checkbox>
                          </div>
                          <div class="flex-right">
                            <div class="title-name">
                              <span class="check-spec">捆包号:</span>
                              <span class="check-val"> {{item.packId}}</span>
                            </div>
                            <div class="title-name">
                              <span class="check-spec">并包号：</span>
                              <span class="check-val">{{ item.unitedPackId }}</span>
                            </div>
                            <div class="title-name">
                              <span class="check-spec">净/毛重：</span>
                              <span class="check-val">{{ item.netWeight }}/{{ item.grossWeight }}</span>
                            </div>
                            <div>
                              <span class="check-spec">规格：</span>
                              <span class="check-val">{{ item.specsDesc }}</span>
                            </div>
                          </div>
                        </div>
                      </template>

                      <!-- <template #right-icon>
                  <van-checkbox :name="index" ref="checkboxes">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-checkbox>
                </template> -->
                    </van-cell>
                    <template #right>
                      <!-- <button class="swiper-btn-update" @click="updateListItem">
                      <span class="swiper-text">修改</span>
                    </button> -->
                      <button class="swiper-btn-delete" @click="deleteListItem(index)">
                        <span class="swiper-text">删除</span>
                      </button>
                    </template>
                  </van-swipe-cell>
                </van-cell-group>
              </van-checkbox-group>
            </div>
            <div v-if="packList && packList.length > 0">
              <!-- <div class="mui-input-row3" v-show="isShowBottom">
                <button id="block_button" type="button" class="mui-btn3" @click="onConfirm">
                  确&nbsp; &nbsp; &nbsp;&nbsp; 认
                </button>
                <button type="button" class="mui-btn3" v-preventReClick="3000" @click="onGenerateStorage">
                  并&nbsp; &nbsp; &nbsp;&nbsp;包
                </button>
              </div> -->
              <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
                <button type="button" class="mui-btn" v-preventReClick="3000" @click="onGenerateStorage">
                  并&nbsp; &nbsp; &nbsp;&nbsp; 包
                </button>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <van-empty description="暂无捆包清单，请添加扫描" class="all-font-size" />
          <!-- <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
            <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfirm">
              确&nbsp; &nbsp; &nbsp;&nbsp; 认
            </button>
          </div> -->
        </div>
      </van-tab>
    </van-tabs>
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  </div>
</template>

<script>
  import uploadIcon from '@/assets/imgs/upload-icon.png'
  import * as baseApi from "@/api/base-api";
  import HmPopup from "@/components/HmPopup.vue";
  import {
    isEmpty
  } from '@/utils/tools';
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        checkedAll: false,
        active: 2,
        packList: [],
        result: [],
        bale: "",
        radio: "00",
        storeName: "",
        show: "",
        matInnerId: "",
        baleList: {},
        location: "", //库位
        locationName: "", //库位名称
        chooseList: [],
        testList: [],
        radio2: -1,
        fontSize: " font-size: 16px; ",
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    components: {
      HmPopup,
    },
    created() {
      this.storeName = localStorage.getItem("warehouseName");
      // this.searchCargoDamage();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      toggle(index) {
        this.$refs.checkboxes[index].toggle();
      },
      onConfirm() {
        //添加到列表对库位不进行判断
      },
      onClickLeft() {
        this.$router.goBack();
      },
      deleteListItem(index) {
        //删除数组中值
        this.$delete(this.packList, index);
      },
      checkAll() {
        //   this.checkedAll = !this.checkedAll;
        // this.$refs.checkboxGroup.toggleAll(this.checkedAll);
        this.$refs.checkboxGroup.toggleAll(true);
        console.log("result", this.result);
      },
      toggleAll() {
        this.$refs.checkboxGroup.toggleAll();
      },
      saveBale(val) {
        let flag = this.packList.some(item => {
          return (
            item.matInnerId === val[0].matInnerId &&
            item.tradeCode === val[0].tradeCode
          );
        });
        if (flag) {
          this.$toast("此捆包号已添加，请重新扫描其他捆包号");
        } else {
          this.packList = [...val, ...this.packList];
          this.clearList();
        }
      },
      clearList() {
        this.baleList = {};
        this.chooseList = [];
        this.bale = "";
        // this.location = "";
        this.testList = [];
        // this.locationName = "";
      },
      clearLoction() {
        this.location = "";
        this.locationName = "";
      },
      getCheckInfo(val) {
        this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.matInnerId = this.baleList.matInnerId;
        this.bale = this.baleList.packId;
        this.location = this.baleList.locationId;
        this.locationName = this.baleList.locationName;
        console.log("val  =>", val);
        this.saveBale(val);
        // if (isEmpty(this.location)) {
        //   this.changeFocus();
        // } else {
        //   this.saveBale();
        // }
        // this.getWarehouseLocation();
      },
      showtest() {
        this.show = !this.show;
      },
      changeFocus() {
        this.$nextTick(() => {
          this.$refs.labelsecond.focus();
        });
      },
      async onGenerateStorage() {
        if (this.result.length == 0) {
          this.$toast("请选中要并包生成的捆包数据！");
          return false;
        }
        let ids = this.result.map((item) => {
          return {
            packId: item.packId,
            matInnerId: item.matInnerId,
            unitedPackId: item.unitedPackId,
          };
        })
        const params = {
          serviceId: "S_UC_PR_230640",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          detail: ids
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                this.packList = [];
                this.result = [];
              } else {
                Dialog.alert({
                  title: '提示',
                  message: res.data.__sys__.msg,
                }).then(() => {
                  // on close
                });
                //this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_200006",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          packId: this.bale
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj) {
                this.packId =  dataObj.packId;
                if (dataObj.result.length > 1) {
                  this.show = !this.show;
                  this.testList = res.data.result;
                } else {
                  if (dataObj.result.length == 0) {
                    this.$toast("没有查询到相应捆包信息！");
                  } else {
                    this.chooseList = dataObj.result;
                    this.baleList = {
                      ...dataObj.result[0]
                    };
                    this.saveBale(this.chooseList);
                  }
                }
              } else {
                this.packId =  dataObj.packId;
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByPackId2() {
        const params = {
          serviceId: "S_UC_PR_230601",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          packId: this.bale,
          locationId: this.location,
          locationName: this.locationName
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.__sys__.status != -1) {
              if (res.data.result.length > 1) {
                this.show = !this.show;
                this.testList = res.data.result;
              } else {
                this.chooseList = res.data.result;
                this.baleList = {
                  ...res.data.result[0]
                };
                this.location = this.baleList.locationId;
                this.locationName = this.baleList.locationName;
                if (
                  this.location == "" ||
                  this.location == null ||
                  this.location == undefined
                ) {
                  this.changeFocus();
                } else {
                  this.saveBale();
                }

              }
            } else {
              this.chooseList = [];
              this.baleList = {};
              this.bale = res.data.packId || this.bale;
              if (
                this.location == "" ||
                this.location == null ||
                this.location == undefined
              ) {
                this.changeFocus();
              } else {
                this.saveBale();
              }
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  .store-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }


  .dialog-content {
    width: 100%;
    height: 200px;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .activeColor {
    color: #007aff;
  }

  .content-cell {
    margin-bottom: 100px;
  }

  .check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
  }

  .content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .div-flex {
    display: flex;
    align-items: center;
    // justify-content: space-between;
  }

  .flex-right {
    margin-left: 25px;
  }

  .swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
  }

  .inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }
</style>
