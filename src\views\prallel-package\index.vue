<template>
  <div>
    <van-nav-bar>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
      <template #title>
        <div class="global-hfont">并包管理</div>
      </template>
    </van-nav-bar>
    <div class="load-content">
      <button class="mui-btn" @click="goGenerate">
        <div class="search icon"></div>
        <span class="load-span">并包生成</span>
      </button>
      <button class="mui-btn" @click="goRevoke">
        <div class="search icon"></div>
        <span class="load-span">并包撤销</span>
      </button>


    </div>
  </div>
</template>

<script>
import { Dialog } from "vant";
export default {
  data() {
    return {};
  },
  created() {
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goGenerate() {
      this.$router.togo("/generatePackage");
    },
    goRevoke() {
      this.$router.togo("/revokePackage");
    },
  },
};
</script>

<style lang="less" scoped>
// .van-nav-bar {
//   background-color: #007aff;
//   width: 100%;
//   height: 42px;
//   /deep/ .van-nav-bar__title {
//     font-size: 18px;
//     font-weight: 400;
//     color: #ffffff;
//     line-height: 25px;
//   }

//   /deep/ .van-icon {
//     color: ffffff;
//   }
// }
.load-content {
  margin: 20px 20px 8px 20px;
}
.load-span {
  margin-left: 25px;
}
</style>
