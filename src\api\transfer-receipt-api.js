/**
 * 引入fetch
 * @param params
 * @returns {*}
 */
import { fetch5 } from 'config/service';
import { Notify } from 'vant';

/** 蓝牙打印查询资材合同子项号数据 */
export async function queryBlueLabelSub(stuffContractSubid) {
    const query = {
        serviceId: 'S_VF_PM_20666',
        segNo: localStorage.getItem('segNo'),
        stuffContractSubid,
    };

    const result = (await fetch5(query)).data;
    if (!result || !result.__sys__ || !result.__sys__.msg || !result.__sys__.msg.includes('成功')) {
        Notify({ type: 'danger', message: result.__sys__.msg });
        return;
    }
    Notify({ type: 'success', message: result.__sys__.msg });
    return result.dataList;
}


/** 生产领料查询工单 */
export async function getList(query) {
    const { columns, values } = mapToColumnsAndValues(query);
    const param = {
        serviceId: 'S_VI_PM_0015',
        __blocks__: {
            inqu_status: {
                "attr": {},
                "meta": {
                    "desc": "",
                    "attr": {},
                    columns,
                },
                rows: [values],
            },
        },
    };
    const res = await fetch5(param);
    console.log(res);
    if (!res || !res.data) {
        Notify({ type: 'danger', message: '网络异常, 请联系管理员' });
        return [];
    }

    const data = res.data;
    if (data.__sys__.status != '0') {
        let message = data.__sys__.msg;
        Notify({ type: 'danger', message: message.length > 500 ? message.substring(0, 500) : message });
        return [];
    }

    if (!data.__blocks__.result || !data.__blocks__.result.rows || data.__blocks__.result.rows.length == 0) {
        Notify({ type: 'danger', message: '未查询到工单信息' });
        return [];
    }

    const arrList = arrayToArrayObj(data.__blocks__.result.meta.columns, data.__blocks__.result.rows);
    return arrList;
}

/** 查询该工单下所有的产出捆包 */
export async function getOutputPackListToOrderId(query) {
    const { columns, values } = mapToColumnsAndValues(new Map(Object.entries(query)));
    const userId = localStorage.getItem("userId");
    const userName = localStorage.getItem("userName");
    query = {
        flag: '0',
        isCopyWin: true,
        serviceId: 'S_VI_PM_0022',
        information: query,
        __blocks__: {
            result: {
                "attr": {},
                "meta": {
                    "desc": "",
                    "attr": {},
                    columns,
                },
                rows: [values],
            },
            loginUser: {
                "attr": {},
                "meta": {
                    "desc": "",
                    "attr": {},
                    "columns": [
                        {
                            "pos": 0,
                            "name": "loginName",
                        }, {
                            "pos": 1,
                            "name": "userName",
                        }, {
                            "pos": 2,
                            "name": "userId",
                        },
                    ],
                },
                rows: [
                    [userId, userName, userId],
                ],
            },
        },
    };

    const result = await fetch5(query);
    if (!result || !result.data) {
        Notify({ type: 'danger', message: '网络异常, 请联系管理员' });
        return [];
    }

    if (result.data.__sys__.status != '0') {
        Notify({ type: 'danger', message: result.data.__sys__.msg });
        return [];
    }

    if (!result.data.__blocks__.copyPackWin || !result.data.__blocks__.copyPackWin.rows || result.data.__blocks__.copyPackWin.rows.length == 0) {
        Notify({ type: 'danger', message: '未查询到捆包信息' });
        return [];
    }


    return arrayToArrayObj(result.data.__blocks__.copyPackWin.meta.columns, result.data.__blocks__.copyPackWin.rows);
}

export async function getMachineTeamList(map) {
    const { columns, values } = mapToColumnsAndValues(map);

    const query = {
        "serviceId": 'S_VI_PM_0000',
        __blocks__: {
            inqu2_status: {
                attr: {},
                meta: {
                    attr: {},
                    columns,
                    desc: "",
                },
                rows: [values],
            },
            result2: {
                attr: {
                    count: 0,
                    limit: 1000,
                    offset: 0,
                    orderBy: "",
                    showCount: "false",
                },
            },
        },
    }

    return (await fetch5(query)).data;
}


export function mapToColumnsAndValues(map) {
    return {
        columns: Array.from(map.keys()).map((name, pos) => {
            return { pos, name }
        }),
        values: Array.from(map.values())
    }
}

export function arrayToArrayObj(keys, values) {
    const arrObj = values.map(row => {
        const obj = {};
        row.forEach((value, index) => {
            obj[keys[index].name] = value;
        });
        return obj;
    });
    return arrObj;
}
