<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">司机出厂确认</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuo<PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>

      <van-field label="车牌号">
        <template #input>
          <input type="search" readonly placeholder="请查询车牌号" v-model="vehicleNo" />
        </template>
        <template #right-icon>
          <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE" @click="searchVehicleNo"></span>
        </template>
      </van-field>
    </van-sticky>

    <div class="load-content">
     <div style="margin-left: 10px; margin-top: 14px;">
        <div class="span-count">{{ tipStr }}</div>
      </div>

     <!-- <div  style="margin-left: 10px; margin-top: 14px;">
        <div v-for="(line, index) in processedText" :key="index" class="span-count">
          {{ line }}
        </div>
      </div> -->

      <div class="mui-input-row" style="margin: 0" @click="onConfim" v-if="vehicleNo">
        <button type="button" class="mui-btn">
          确&nbsp; &nbsp; &nbsp;&nbsp;定
        </button>
      </div>
    </div>
    <van-dialog v-model="showVehicle" :show-confirm-button="showBtn">
      <div>
        <van-search v-model="value" shape="round" background="#007aff" placeholder="请输入车牌号" @search="getVehicleList"
          left-icon="" :clearable="false">
          <template #right-icon>
            <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
          </template>
        </van-search>
      </div>
      <div class="search-list">
        <div v-if="vehicleList && vehicleList.length > 0">
          <van-radio-group v-model="value">
            <van-cell-group>
              <van-cell v-for="(item, index) in vehicleList" :key="index" clickable @click="onCheck(index, item)">
                <template #title>
                  <div class="seg-title">{{ item }}</div>
                </template>
                <template #right-icon>
                  <van-radio :name="item">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-radio>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>

        <div class="tips-content" v-else>
          <div class="title">提示</div>
          <div class="msg">未查询到可选择的车牌！</div>
        </div>
      </div>
      <van-popup :style="{ height: '100%' }" v-model="signShow" get-container="body" position="bottom">
        <signCanvas @close="closeDialog" :d_h="h" :d_w="w" :title="title" ref="signChild"></signCanvas>
        <!--  <div class="canvans-tip">提示模块</div> -->
      </van-popup>
      <button class="search-btn" @click="onConfrimCarNumber">确认</button>
    </van-dialog>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import signCanvas from '@/components/signature.vue'
  import {
    Dialog,
    ImagePreview,
  } from "vant";
  import {
    isEmpty
  } from "@/utils/tools";
  export default {
    components: {
      signCanvas
    },
    // computed: {
    //   processedText() {
    //     // 使用分号作为分割符，分割字符串
    //     return this.tipStr.split(';');
    //   }
    // },
    data() {
      return {
        title: '门卫签名',
        tipStr: '',
        signShow: false,
        resultImg: "",
        w: document.documentElement.clientWidth, //实时屏幕宽度
        h: document.documentElement.clientHeight, //实时屏幕高度
        showVehicle: false,
        showBtn: false,
        vehiclePackList: [],
        searchList: [],
        carList: [],
        result: [],
        vehicleNo: "",
        phoneNumber: "",
        vehicleList: [],
        ladingBillId: "",
        value: "",
        check: false,
        carNumber: "",
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        textCode: '',
        textCodeIndex: -1,
      };
    },

    created() {
      this.getVehicleList();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
        //  this.$router.replace('/selectOption');
      },
      showOpen() {
        this.show = true;
      },
      onCheck(index, item) {
        this.value = item;
        this.vehicleNo = item;
      },
      closeDialog(e) {
        // 签名赋值 这里的e就是base64了，直接使用
        // 关闭签名框,这里要调用上传图片接口不要直接确认

        if (!isEmpty(e)) {
          this.resultImg = e;
          this.onConfimSign();
        }
        this.signShow = false;

      },
      onConfim() {
        this.signShow = true;
      },
      async onConfimSign() {
        const params = {
          serviceId: "S_UC_PR_230511",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNo: this.vehicleNo,
          picture: this.resultImg,

        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                this.tipStr = '';
                this.vehicleNo ='';
                this.$refs.signChild.handleReset();
               // this.getVehicleList();
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      deleteItem(index) {
        //删除数组中值
        this.$delete(this.vehiclePackList, index);
        this.$delete(this.result, index);
      },
      onConfrimCarNumber() {
        this.value = "";
        this.showVehicle = false;
        this.getVehiclePackList();
      },

      //查询车牌号列表
      searchVehicleNo() {
        this.getVehicleList();
        this.showVehicle = true;
      },
      async getVehiclePackList() {
        const params = {
          serviceId: "S_UC_PR_200105",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          vehicleNo: this.vehicleNo,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.tipStr = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },

      async getVehicleList() {
        const params = {
          serviceId: "S_UC_PR_200103",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNo: this.value,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      // 判断验证码是否正确


    },
  }
</script>

<style lang="less" scoped>
  .load-content {
    padding-bottom: 120px;
  }

  .activeColor {
    color: #007aff;
  }

  .nav-bar-scan {
    color: #fff;
  }


  .info-btn {
    overflow: hidden;
    position: fixed;
    bottom: 0;
  }

  .load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
  }

  .empty-des {
    text-align: center;
    padding-top: 30px;
    color: #BEBEBE;
  }

  .load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
  }

  .search-list {
    height: 180px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
    overflow-y: auto;

    // margin-bottom: 80px;
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .tips-content {
    height: 110px;
    margin: 15px;
    background-color: #fff;
    font-size: 14px;
    padding: 10px;
    border-radius: 10px;

    div {
      margin: 10px;
    }

    .title {
      text-align: center;
    }

    .msg {
      margin-top: 10px;
    }
  }


  .span-count {
    word-wrap: break-word;
    margin-left: 10px;
    color: #0000ff;
    font-size: 16px;
    width: 350px;
  }

  .search-btn {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    color: #fff;
  }

  /** 遮挡罩 加载动画定位 */
  div.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    div.block {
      width: 100px;
      height: 100px;
      background-color: #fff;
      text-align: center;
      line-height: 100px;
      border-radius: 10px;
    }

  }
</style>
