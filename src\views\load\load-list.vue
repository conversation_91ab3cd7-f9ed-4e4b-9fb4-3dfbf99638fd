<template>
  <div>
    <van-nav-bar>
      <template #left>
        <span class="iconfont icon-zuo<PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
      </template>
      <template #title>
        <div class="global-hfont">列表</div>
      </template>
    </van-nav-bar>
    <div class="load-content">
      <button class="mui-btn" @click="onEndLoad">
        下个作业点
      </button>
      <button class="mui-btn" @click="onCallBack(1)">
        召回车辆
      </button>
      <button class="mui-btn" @click="onCallBack(2)">
        未装离厂
      </button>
    </div>
  </div>
</template>

<script>
  import {
    Dialog
  } from "vant";
  import {
    isEmpty
  } from "@/utils/tools";
  let previousCarNum = "";
  export default {
    data() {
      return {
        vehicleNo: "",
      };
    },
    created() {
      this.vehicleNo = this.$route.params.carNumber;
      if (!isEmpty(previousCarNum)) {
        this.vehicleNo = previousCarNum;
      }
    },
    beforeRouteEnter(to, from, next) {
      //if (from.name != "car-load") {
      previousCarNum = from.params.carNumber;
      //  }
      next();
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      onEndLoad() {
        this.$router.togo({
          name: "endNext",
          params: {
            carNumber: this.vehicleNo
          },
        });
        //this.$router.togo("/endNext");
      },
      onCallBack(val) {
        this.$router.togo({
          name: "callbackCar",
          params: {
            typeCar: val,
            carNumber: this.vehicleNo
          },
        });
        //  this.$router.togo("/callbackCar?typeCar=" + val);
      }
    },
  };
</script>

<style lang="less" scoped>
  .load-content {
    margin: 20px 20px 8px 20px;
  }

  .load-span {
    margin-left: 25px;
  }
</style>
