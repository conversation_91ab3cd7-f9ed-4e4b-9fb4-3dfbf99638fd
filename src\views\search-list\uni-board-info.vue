<template>
    <div class="beijin">
        <van-sticky>
            <van-nav-bar>
                <template #title>
                    <div class="global-hfont">优尼看板详情</div>
                </template>
                <template #left>
                    <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
                </template>
            </van-nav-bar>
        </van-sticky>
        <div class="in-content beijin">
            <h2 class="van-doc-demo-block__title">主项信息</h2>
            <van-cell-group class="detail_textarea">
                <van-cell title="客户订单号" :value="uniData.custPurOrderNum" />
                <van-cell title="客户零件号" :value="uniData.custPartId" />
                <van-cell title="规格" :value="uniData.specDesc" />
                <van-cell title="牌号" :value="uniData.shopsign" />
                <van-cell title="台份" :value="uniData.demandPlanQty" />
                <van-cell title="看板接收时间" :value="uniData.demandPlanDate" />
                <van-cell title="上传张数" :value="uniData.demandDeliveryQty" />
                <van-cell title="上传重量" :value="uniData.sumNetWeight" />
            </van-cell-group>
            <h2 class="van-doc-demo-block__title">子项信息</h2>
            <van-cell-group class="detail_textarea" inset v-for="(item, index) in uniData.subList" :key="index" >
                <van-cell title="捆包号" :value="item.packId" />
                <van-cell title="客户名称" :value="item.customerName" />
                <van-cell title="规格" :value="item.specDesc" />
                <van-cell title="钢厂订单号" :value="item.factoryOrderNum" />
                <van-cell title="分户号简称" :value="item.d_userNum" />
                <van-cell title="件数" :value="item.pieceNum" />
                <van-cell title="仓库代码" :value="item.warehouseCode" />
                <van-cell title="品种附属码" :value="item.prodTypeId" />
                <van-cell title="品种附属码描述" :value="item.prodTypeDesc" />
            </van-cell-group>
        </div>
    </div>
</template>
    
<script>
import { Dialog } from "vant";
import * as baseApi from "@/api/base-api";
import HmInput from "@/components/input.vue";
export default {
    created() {
        this.uniData = this.$route.params.data;
    },
    data() {
        return {
            uniData: '',
        };
    },
    methods: {
        onClickLeft() {
            this.$router.goBack();
        },
    },
};
</script>
    
<style lang="less" scoped>
.span-count {
    margin-left: 10px;
    color: #0000ff;
}

.search-out-btn {
    width: 18%;
    padding: 5px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
}

.status-color {
    color: #0000ff;
}

.detail_textarea {
    margin-bottom: 1em;
}

.beijin {
    background-color: rgba(243, 243, 243, 1);
}

.van-doc-demo-block__title {
    margin: 0;
    padding: 16px 16px 5px 16px;
    color: rgba(69, 90, 100, .6);
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
}
</style>
    