<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择配车单号</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content">
      <van-sticky :offset-top="42">
        <van-search
          v-model="value"
          shape="round"
          background="#007aff"
          placeholder="配车单"
          @search="onSearchInput"
          left-icon=""
          :clearable="false"
        >
          <template #right-icon>
            <div
              class="iconfont icon-sousuo"
              @click="onSearchInput"
              style="color: #999999"
            ></div>
          </template>
        </van-search>
      </van-sticky>
      <div class="load-content">
        <div
          class="load-cell"
          v-for="(item, index) in 12"
          :key="index"
          @click="isChecked(index)"
        >
          <div>
            <div class="load-number">PDA000000000000000</div>
            <div class="load-name">上海宝钢新事业发展总公司</div>
          </div>
          <div v-if="currentIndex == index">
            <div class="check-style"></div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="mui-input-row"
      style="margin: 0"
      @click="onConfirm"
      v-show="isShowBottom"
    >
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;定
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentIndex: -1,
      value: "",
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index) {
      this.currentIndex = index;
    },
    onSearchInput() {},
    onConfirm() {
      this.$route.params.loadList = [
        { number: "PDA000000000000000", loadName: "上海宝钢新事业发展总公司" },
      ];
      this.$router.goBack();
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  margin-bottom: 60px;
}
.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  margin-top: 8px;
}
</style>