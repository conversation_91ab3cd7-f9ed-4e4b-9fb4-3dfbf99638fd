<template>
  <div>
    <baidu-map
      class="map"
      :center="{ lng: 116.404, lat: 39.915 }"
      :zoom="15"
      @ready="onMapReady"
    >
      <bm-geolocation
        :anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
        :offset="{ width: 10, height: 20 }"
        :showAddressBar="true"
        :enableAutoLocation="true"
        @locationSuccess="onLocationSuccess"
        @locationError="onLocationError"
      ></bm-geolocation>
    </baidu-map>
    <div v-if="locationInfo">
      <p>纬度: {{ locationInfo.lat }}</p>
      <p>经度: {{ locationInfo.lng }}</p>
    </div>
    <div v-else-if="locationError">
      <p>定位失败: {{ locationError }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      locationInfo: null,
      locationError: null
    };
  },
  methods: {
    onMapReady({ BMap, map }) {
      // 地图加载完成回调
      console.log('地图加载完成');
    },
    onLocationSuccess(position) {
      const { point } = position;
      this.locationInfo = {
        lat: point.lat,
        lng: point.lng
      };
      console.log('定位成功:', this.locationInfo);
    },
    onLocationError(error) {
      this.locationError = error.message;
      console.error('定位失败:', this.locationError);
    }
  }
};
</script>

<style scoped>
.map {
  width: 100%;
  height: 500px;
}
</style>