<template>
  <div>
    <van-nav-bar>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
      <template #title>
        <div class="global-hfont">废次材</div>
      </template>
    </van-nav-bar>

    <div class="waste-content">
      <!-- 车牌号选择 -->
      <van-field v-model="vehicleNo" label="车牌号" placeholder="请选择或输入车牌号" readonly @click="openCarPicker"
        right-icon="arrow-down" class="all-font-size" />

      <!-- 车牌号选择弹窗 -->
      <van-dialog v-model="showCarPicker" title="选择车牌号" :show-confirm-button="false">
        <div>
          <van-search v-model="searchCarValue" shape="round" placeholder="请输入车牌号" @search="searchVehicleList"
            left-icon="" :clearable="false">
            <template #right-icon>
              <div class="iconfont icon-sousuo" @click="searchVehicleList" style="color: #999999"></div>
            </template>
          </van-search>
        </div>
        <div class="search-list">
          <div v-if="carList && carList.length > 0">
            <van-radio-group v-model="selectedCarValue">
              <van-cell-group>
                <van-cell v-for="(item, index) in carList" :key="index" clickable @click="onCarCheck(index, item)">
                  <template #title>
                    <div class="seg-title">{{ item.vehicleNo }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item.vehicleNo">
                      <template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </div>

                  <div class="tips-content" v-else>
          <div class="title">提示</div>
          <div class="msg">未查询到可选择的车牌！</div>
        </div>
      </div>
      <div class="dialog-buttons">
        <button class="cancel-btn" @click="onCancelCarPicker">取消</button>
        <button class="search-btn" @click="onConfirmCarNumber">确认</button>
      </div>
      </van-dialog>

      <!-- 手动输入车牌号 -->
      <van-field v-model="manualVehicleNo" label="手输车牌" placeholder="手动输入车牌号" clearable @blur="onManualVehicleInput"
        class="all-font-size" />

      <!-- 捆包扫描 -->
      <van-field v-model="packId" label="捆包号" placeholder="请输入或扫描捆包号" clearable @keyup.enter.native="searchPack"
        class="all-font-size" />

      <!-- 捆包列表 -->
      <div class="pack-list-section" v-if="packList.length > 0">
        <div class="list-header">
          <!-- <div class="fourline-blue"></div> -->
          <div class="baletext2">
            已扫捆包合计: <span class="span-count">{{ packList.length }}</span>
          </div>
          <div class="baletext2">
            总净重: <span class="span-count">{{ totalNetWeight }}吨</span>
          </div>
        </div>

        <van-swipe-cell v-for="(item, index) in packList" :key="index">
          <van-cell>
            <template #title>
              <div class="pack-number">{{ item.packId }}</div>
              <div class="pack-info">规格: {{ item.specDesc }}</div>
              <div class="pack-info">净重: {{ item.netWeight }}吨 | 毛重: {{ item.grossWeight }}吨</div>
              <div class="pack-info">客户: {{ item.settleUserName }}</div>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" text="删除" class="delete-pack-btn" @click="deletePackItem(index)" />
          </template>
        </van-swipe-cell>
      </div>

      <!-- 空状态 -->
      <van-empty v-else description="暂无捆包数据，请扫描捆包" image="https://img.yzcdn.cn/vant/custom-empty-image.png" />
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section" v-show="packList.length > 0">
      <van-button type="primary" block @click="onSubmit" :loading="submitting" class="submit-btn">
        提交 ({{ packList.length }}个捆包)
      </van-button>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import * as vehicleApi from "@/api/vehicle-api";
import { isEmpty } from "@/utils/tools.js";
import { Dialog } from "vant";

export default {
  data() {
    return {
      vehicleNo: "",
      manualVehicleNo: "",
      packId: "",
      packList: [],
      showCarPicker: false,
      searchCarValue: "",
      selectedCarValue: "",
      currentCarIndex: -1,
      carList: [],
      submitting: false,
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
    };
  },
  computed: {
    totalNetWeight() {
      return this.packList.reduce((sum, item) => sum + parseFloat(item.netWeight || 0), 0).toFixed(2);
    },
  },
  created() {
    // 页面初始化，不自动查询车辆列表
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },

    // 打开车牌号选择器
    openCarPicker() {
      this.showCarPicker = true;
      this.getVehicleList();
    },

    // 获取车辆列表
    async getVehicleList() {
      // 模拟API调用
      const params = {
        serviceId: "S_UC_PR_200101",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        vehicleNo: this.searchCarValue,
      };

      // 模拟数据，实际应该调用真实API
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              if (dataObj.result.length > 0) {
                this.carList = dataObj.result;
              } else {
                this.$toast("未查询到车牌号列表！");
              }
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    // 搜索车牌号
    searchVehicleList() {
      // 重新查询车辆列表，包含搜索条件
      this.getVehicleList();
    },

    // 选择车牌号
    onCarCheck(index, item) {
      this.selectedCarValue = item.vehicleNo;
      this.currentCarIndex = index;
    },

    // 确认选择车牌号
    onConfirmCarNumber() {
      if (this.selectedCarValue) {
        this.vehicleNo = this.selectedCarValue;
        this.manualVehicleNo = "";
        this.showCarPicker = false;
      } else {
        this.$toast("请选择车牌号!");
      }
    },

    // 取消选择车牌号
    onCancelCarPicker() {
      this.showCarPicker = false;
      this.searchCarValue = "";
      this.selectedCarValue = "";
      this.currentCarIndex = -1;
      this.carList = []; // 清空车辆列表
    },

    // 手动输入车牌号
    onManualVehicleInput() {
      if (this.manualVehicleNo) {
        this.vehicleNo = this.manualVehicleNo;
      }
    },

    // 检查捆包是否重复
    isDuplicatePack(packId, packData = null) {
      return this.packList.some(item => {
        // 检查捆包号是否重复
        if (item.packId === packId) return true;

        // 如果有标签号，检查标签号是否重复
        if (item.labelId && item.labelId === packId) return true;

        // 如果有材料内部编号，检查是否重复
        if (item.matInnerId && item.matInnerId === packId) return true;

        // 如果提供了捆包数据，进行更详细的检查
        if (packData) {
          // 检查标签号重复
          if (packData.labelId && item.labelId && item.labelId === packData.labelId) return true;

          // 检查材料内部编号重复
          if (packData.matInnerId && item.matInnerId && item.matInnerId === packData.matInnerId) return true;
        }

        return false;
      });
    },

    // 扫描捆包
    async searchPack() {
      if (isEmpty(this.packId)) {
        this.$toast("请输入捆包号!");
        return;
      }

      if (isEmpty(this.vehicleNo)) {
        this.$toast("请先选择车牌号!");
        return;
      }

      // 去重处理：检查是否已经扫描过
      if (this.isDuplicatePack(this.packId)) {
        this.$toast("此捆包已扫描，请重新扫描其他捆包号");
        this.packId = "";
        return;
      }

      // 调用捆包查询接口
      const params = {
        serviceId: "S_UC_PR_200006", // 使用现有的捆包查询接口
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        packId: this.packId
      };

      try {
        const res = await baseApi.baseService(params);
        if (res.data && res.data.result && res.data.result.length > 0) {
          const packData = res.data.result[0];

          // 构建新捆包数据
          const newPack = {
            packId: packData.packId,
            specDesc: packData.specDesc || packData.specsDesc,
            netWeight: packData.netWeight,
            grossWeight: packData.grossWeight,
            pieceNum: packData.pieceNum,
            settleUserName: packData.settleUserName,
            matInnerId: packData.matInnerId,
            tradeCode: packData.tradeCode,
            labelId: packData.labelId,
          };

          // 进行详细的去重检查
          if (this.isDuplicatePack(this.packId, newPack)) {
            this.$toast("此捆包已扫描，请重新扫描其他捆包号");
            this.packId = "";
            return;
          }

          this.packList.unshift(newPack);
          this.$toast("扫描成功");
          this.packId = "";
        } else {
          this.$toast("没有查询到相应捆包信息！");
        }
      } catch (err) {
        console.error("捆包查询失败:", err);
        this.$toast("捆包查询失败，请重试");
      }
    },

    // 删除捆包
    deletePackItem(index) {
      this.$delete(this.packList, index);
      this.$toast("删除成功");
    },

    // 提交废次材数据
    async onSubmit() {
      if (this.packList.length === 0) {
        this.$toast("请先扫描捆包!");
        return;
      }

      if (isEmpty(this.vehicleNo)) {
        this.$toast("请先选择车牌号!");
        return;
      }

      this.submitting = true;

      // 格式化提交时间为 YYYYMMDDHHmmss
      const now = new Date();
      const submitTime = now.getFullYear().toString() +
        (now.getMonth() + 1).toString().padStart(2, '0') +
        now.getDate().toString().padStart(2, '0') +
        now.getHours().toString().padStart(2, '0') +
        now.getMinutes().toString().padStart(2, '0') +
        now.getSeconds().toString().padStart(2, '0');

      // 为每个捆包添加提交信息
      const packListWithSubmitInfo = this.packList.map(pack => ({
        ...pack,
        submitTime: submitTime,
        segNo: localStorage.getItem("segNo"),
        submitId: localStorage.getItem("userId"),
        submitName: localStorage.getItem("userName"),
        vehicleNo: this.vehicleNo
      }));

      // 模拟提交API
      const params = {
        serviceId: "S_UC_EP_0152 ",
        accessToken: localStorage.getItem("accessToken"),
        packList: packListWithSubmitInfo,
      };

      const res = await baseApi.baseService(params);
      this.submitting = false;
      if (res.data && res.data.__sys__ && res.data.__sys__.status == '1') {
        this.$toast("废次材信息上传成功");
        // 清空数据
        this.packList = [];
        this.vehicleNo = "";
        this.manualVehicleNo = "";
        this.packId = "";

        //   // 返回上一页
        // setTimeout(() => {
        //   this.$router.goBack();
        // }, 1500);
      } else {
        this.$toast("废次材提交失败!");
      }

    },
  },
};
</script>

<style lang="less" scoped>
.waste-content {
  padding: 0 16px 120px 16px;
  margin-top: 16px;
}

.pack-list-section {
  margin-top: 20px;
}

.list-header {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.fourline-blue {
  width: 4px;
  height: 20px;
  background-color: #007aff;
  display: inline-block;
  margin-right: 8px;
}

.baletext2 {
  font-size: 14px;
  color: #333;
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.span-count {
  margin-left: 5px;
  color: #007aff;
  font-weight: bold;
}

.pack-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #007aff;
  margin-bottom: 4px;
}

.pack-info {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.delete-pack-btn {
  height: 100%;
  width: 80px;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12px 16px;
  border-top: 1px solid #ebedf0;
  z-index: 100;
}

.submit-btn {
  background: linear-gradient(135deg, #007aff 0%, #4a90e2 100%) !important;
  border: none !important;
  color: #fff !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  height: 48px !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3) !important;

  &:active {
    background: linear-gradient(135deg, #0056cc 0%, #3a7bc8 100%) !important;
  }
}

.all-font-size {
  font-size: 16px;
}

/deep/ .van-field__label {
  width: 80px;
}

/deep/ .van-field__control {
  font-size: 16px;
}

.search-list {
  height: 250px;
  background-color: #fafafa;
  overflow-y: auto;
  margin: 12px 0;
  border-radius: 8px;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #c8c9cc;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f7f8fa;
  }
}

.dialog-buttons {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.search-btn {
  flex: 1;
  height: 44px;
  background: linear-gradient(135deg, #007aff 0%, #4a90e2 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(0, 122, 255, 0.4);
  }
}

.cancel-btn {
  flex: 1;
  height: 44px;
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  letter-spacing: 1px;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1px);
    background: #e9ecef;
  }
}

.seg-title {
  font-size: 16px;
  color: #323233;
  font-weight: 500;
  line-height: 22px;
}

.tips-content {
  padding: 40px 20px;
  text-align: center;
}

.tips-content .title {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.tips-content .msg {
  font-size: 14px;
  color: #666;
}

.activeColor {
  color: #007aff;
}

/deep/ .van-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 90%;
  max-width: 400px;
  overflow: hidden;
  font-size: 16px;
  background-color: #fff;
  border-radius: 16px;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/deep/ .van-dialog__header {
  padding: 20px 24px 12px;
  background: linear-gradient(135deg, #007aff 0%, #4a90e2 100%);
  color: #fff;
  text-align: center;
  border-radius: 16px 16px 0 0;
}

/deep/ .van-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  line-height: 24px;
  letter-spacing: 0.5px;
}

/deep/ .van-dialog__content {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

/deep/ .van-search {
  padding: 8px 12px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
}

/deep/ .van-search__content {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/deep/ .van-cell {
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

  &:last-child {
    margin-bottom: 0;
  }
}

// PDA 小屏幕专用适配样式
@media screen and (max-width: 480px) {
  .waste-content {
    padding: 0 12px 120px 12px;
    margin-top: 12px;
  }
  
  /deep/ .van-nav-bar {
    height: 40px;
  }
  
  /deep/ .van-nav-bar__title {
    font-size: 16px;
  }
  
  /deep/ .van-field {
    padding: 10px 12px;
  }
  
  /deep/ .van-field__label {
    width: 70px;
    font-size: 14px;
  }
  
  /deep/ .van-field__control {
    font-size: 15px;
    height: 32px;
  }
  
  /deep/ .van-field__control::placeholder {
    font-size: 13px;
  }
  
  .pack-number {
    font-size: 15px;
    margin-bottom: 3px;
  }
  
  .pack-info {
    font-size: 12px;
    margin-top: 2px;
  }
  
  .baletext2 {
    font-size: 13px;
    margin-top: 6px;
  }
  
  .list-header {
    padding: 10px 12px;
    margin-bottom: 8px;
  }
  
  /deep/ .van-cell {
    padding: 10px 12px;
  }
  
  .delete-pack-btn {
    width: 70px;
    font-size: 13px;
  }
  
  .submit-section {
    padding: 10px 12px;
  }
  
  .submit-btn {
    height: 44px !important;
    font-size: 15px !important;
  }
  
  /deep/ .van-dialog {
    width: 95% !important;
    max-width: 350px !important;
  }
  
  /deep/ .van-dialog__title {
    font-size: 16px !important;
  }
  
  .search-list {
    height: 220px;
  }
  
  .seg-title {
    font-size: 15px;
  }
  
  .dialog-buttons button {
    height: 40px;
    font-size: 15px;
  }
  
  .tips-content .title {
    font-size: 15px;
  }
  
  .tips-content .msg {
    font-size: 13px;
  }
  
  .span-count {
    font-size: 13px;
  }
}
</style>