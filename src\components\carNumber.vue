<template>
  <div>
    <div class="content-wrapper">
      <div class="content-top-wrapper">
        <van-radio-group v-model="radioCarType" style="font-size: 16px;">
          <van-radio name="3"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
             黄牌
          </van-radio>
          <van-radio name="2"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
             绿牌
          </van-radio>
          <van-radio name="1"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
             蓝牌
          </van-radio>

        </van-radio-group>
      </div>
      <div class="content-middle-wrapper">
        <div class="content-car-number">
          <div class="content-section">
            <div class="content-section-flex flex-card">
              <div class="content-section-flex flex-card-border">
                <button class="flex-btn" @click="btnClickYue" v-bind:class="{ isClick: isYue }">
                  {{ areaName }}
                </button>
                <!--  <span class="blank-border blank-border-first"></span> -->
                <button id="letter" class="flex-btn" @click="btnClickA" v-bind:class="{ isClick: isA }">
                  {{ areaLetter }}
                </button>
                <!-- <div class="flex-mid"><span></span></div> -->
                <!-- <div class="flex-btns"> -->
                <button id="numOne" class="flex-btn" @click="btnClickNum('one')"
                  v-bind:class="{ isNumClick: isNumOne }">
                  {{ numOne }}
                </button>
                <!--   <span class="blank-border"></span> -->
                <button id="numTwo" class="flex-btn" @click="btnClickNum('two')"
                  v-bind:class="{ isNumClick: isNumTwo }">
                  {{ numTwo }}
                </button>
                <!--  <span class="blank-border"></span> -->
                <button id="numThree" class="flex-btn" @click="btnClickNum('three')"
                  v-bind:class="{ isNumClick: isNumThree }">
                  {{ numThree }}
                </button>
                <!--  <span class="blank-border"></span> -->
                <button id="numFour" class="flex-btn" @click="btnClickNum('four')"
                  v-bind:class="{ isNumClick: isNumFour }">
                  {{ numFour }}
                </button>
                <!--  <span class="blank-border"></span> -->
                <button id="numFive" class="flex-btn" @click="btnClickNum('five')"
                  v-bind:class="{ isNumClick: isNumFive }">
                  {{ numFive }}
                </button>
                <!-- <span class="blank-border" v-if="radioCarType == '2'"></span> -->
                <button v-if="radioCarType == '2'" id="numSix" @click="btnClickNum('six')" class="flex-btn"
                  v-bind:class="{ isNumClick: isNumSix }">
                  {{ numSix }}
                </button>
                <!-- </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button class="band-btn" :disabled="!checked" @click="bindCar">
      确定
    </button>
    <div class="shadow" v-if="keyboardShow" @click="completeClick"></div>
    <div class="keyboard" v-if="keyboardShow" :class="{ animationDown: isDown, animationUp: isUp }">
       <button class="btn-complete" @click="completeClick">
          <span>完成</span>
        </button>
      <div v-if="keyboard == 'txt'">
        <div class="keyboard-row" v-for="(item, rows) in carTxt" :key="rows+item">
          <button class="keyboard-row-item" v-for="(i, index) in item.name" :key="i+index"
            @click="btnWordClick(rows, index, i)">
            {{ i }}
          </button>
        </div>
        <div class="keyboard-row">
          <button class="keyboard-row-item bottom" @click="btnBottomClick('新')">
            新
          </button>
          <button class="keyboard-row-item bottom" @click="btnBottomClick('使')">
            使
          </button>
          <button v-for="(j,index) in noneBottomtxt" :key="j+index" class="none-botton">
            {{ j }}
          </button>
          <div class="keyboard-row-item clear" @click="clearClick">
            X
            <!--  <img src="./images/icon_input_delete.png" alt="删除" /> -->
          </div>
        </div>
      </div>
      <div v-if="keyboard == 'num'">
        <div class="keyboard-row" v-for="(item, rows) in carNum" :key="rows+item" style="justify-content: center;" >
          <button :disabled="
              !isSelectl ? isDisable && i < 10 : isDisable && rows != 0
            " class="keyboard-row-item" v-for="(i, index) in item.name" :key="index+i" style="margin-right: 8px;"
            @click="btnWordClick(rows, index, i)"  >
            {{ i }}
          </button>
        </div>
        <div class="keyboard-row" style="justify-content: center;">
          <button :disabled="isSelectl" v-for="(item, index) in carNumBottom" :key="index+item" style="margin-right: 8px;"
            class="keyboard-row-item bottom" @click="btnBottomNumClick(item)"   >
            {{ item }}
          </button>
        <!--  <button v-for="(j,index) in noneBottom" :key="j+index" class="none-botton">
            {{ j }}
          </button> -->
          <div class="keyboard-row-item clear" @click="clearClick">
            ⬅
            <!-- <img src="./images/icon_input_delete.png" alt="删除" /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        num: "",
        radioCarType: "3", // 单选
        radioImg: [
          // require("./images/radio0.png"),
          // require("./images/radio1.png"),
          // require("./images/checked1.png"),
          // require("./images/checked0.png"),
        ], //单选图标
        checked: true, //协议复选框
        isAble: false, //发送控制,
        province: "浙", //省份简称
        plateNumber: "", //默认显示E
        checkbox: false,
        isDown: false,
        isUp: false,
        selected: null,
        isSelectx: false,
        isSelectl: false,
        key: "1",
        areaName: "",
        areaLetter: "",
        numOne: "",
        numTwo: "",
        numThree: "",
        numFour: "",
        numFive: "",
        numSix: "",
        isYue: false,
        keyboardShow: false,
        keyboard: false,
        isA: false,
        isNumOne: false,
        isNumTwo: false,
        isNumThree: false,
        isNumFour: false,
        isNumFive: false,
        isNumSix: false,
        isDisable: false,
        carTxt: [{
            name: ["粤", "京", "冀", "沪", "津", "晋", "蒙", "辽", "吉", "黑"],
          },
          {
            name: ["苏", "浙", "皖", "闽", "赣", "鲁", "豫", "鄂", "湘", "桂"],
          },
          {
            name: ["琼", "渝", "川", "贵", "云", "藏", "陕", "甘", "青", "宁"],
          },
        ],
        //carNumBottom: ["W", "X", "Y", "Z"],
        carNumBottom: ["Z", "X", "C", "V","B","N","M"],
        noneBottom: ["", ""],
        noneBottomtxt: ["", "", "", "", "", ""],
        carNum: [{
            name: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"],
          },
          {
         //   name: ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K"],
             name: ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P"],
          },
          {
          //  name: ["L", "M", "N", "P", "Q", "R", "S", "T", "U", "V"],
             name: ["A", "S", "D", "F", "G", "H", "J", "K", "L"],
          },
        ],
      };
    },
    beforeCreate() {
      document
        .querySelector("body")
        .setAttribute("style", "background: #f5f5f5 !important");
    },
    destroyed() {
      document.querySelector("body").setAttribute("style", "background: #fff");
    },
    mounted() {
      this.isYue = true;
      this.isA = false;
      this.isUp = true;
      this.isNumOne = false;
      this.isNumTwo = false;
      this.isNumThree = false;
      this.isNumFour = false;
      this.isNumFive = false;
      this.isNumSix = false;
      this.keyboardShow = false;
      this.keyboard = "txt";
      this.key = 1;
    },
    watch: {
      radioCarType(news) {
        if (news) {
          this.isYue = false;
          this.keyboardShow = false;
          this.keyboard = false;
          this.key = "1";
          this.areaName = "";
          this.areaLetter = "";
          this.numOne = "";
          this.numTwo = "";
          this.numThree = "";
          this.numFour = "";
          this.numFive = "";
          this.numSix = "";
          this.isNumOne = false;
          this.isNumTwo = false;
          this.isNumThree = false;
          this.isNumFour = false;
          this.isNumFive = false;
          this.isNumSix = false;
        }
      },
    },
    methods: {
      // 车牌号第一位输入框选择
      btnClickYue() {
        this.isYue = true;
        this.isA = false;
        this.isUp = true;
        this.isNumOne = false;
        this.isNumTwo = false;
        this.isNumThree = false;
        this.isNumFour = false;
        this.isNumFive = false;
        this.isNumSix = false;
        this.keyboardShow = true;
        this.keyboard = "txt";
        this.key = 1;
      },
      // 键盘选择显示
      btnWordClick(rows, index, i) {
        this.selected = i;
        if (this.key === 1) {
          this.areaName = i;
          this.isSelectl = false;
          document.getElementById("letter").click();
        } else if (this.key === 2) {
          this.areaLetter = i;
          document.getElementById("numOne").click();
        } else if (this.key === 3) {
          this.numOne = i;
          document.getElementById("numTwo").click();
        } else if (this.key === 4) {
          this.numTwo = i;
          document.getElementById("numThree").click();
        } else if (this.key === 5) {
          this.numThree = i;
          document.getElementById("numFour").click();
        } else if (this.key === 6) {
          this.numFour = i;
          document.getElementById("numFive").click();
        } else if (this.key === 7) {
          this.numFive = i;
          if (this.radioCarType == "2") {
            document.getElementById("numSix").click();
          }
        } else if (this.key === 8) {
          this.numSix = i;
        }
        if ((this.key === 7 || this.key === 8) && this.radioCarType != "2") {
        //  this.carNumBottom = ["W", "X", "Y", "Z", "港", "澳", "学", "警", "领"];
          this.carNumBottom = ["港","Z", "X", "C", "V","B","N","M",  "澳"],
          this.noneBottom = [];
        } else if (
          this.key === 3 ||
          this.key === 4 ||
          this.key === 5 ||
          this.key === 6
        ) {
        //  this.carNumBottom = ["W", "X", "Y", "Z"];
        this.carNumBottom = ["Z", "X", "C", "V","B","N","M"]
          this.noneBottom = ["", "", "", ""];
        } else if (this.radioCarType == "2" && this.key === 2) {
         // this.carNumBottom = ["W", "X", "Y", "Z"];
         this.carNumBottom = ["Z", "X", "C", "V","B","N","M"]
          this.noneBottom = ["", "", "", ""];
        }
      },
      // 车牌第一位选择‘临’,’新‘后的键盘显示
      btnBottomClick(val) {
        this.areaName = val;
        this.isSelectx = true;
        this.isSelectl = false;
        document.getElementById("letter").click();
      },
      // 键盘最后一行字母点击事件
      btnBottomNumClick(i) {
        this.selected = i;
        if (this.key === 2) {
          this.areaLetter = i;
          document.getElementById("numOne").click();
        } else if (this.key === 3) {
          this.numOne = i;
          document.getElementById("numTwo").click();
        } else if (this.key === 4) {
          this.numTwo = i;
          document.getElementById("numThree").click();
        } else if (this.key === 5) {
          this.numThree = i;
          document.getElementById("numFour").click();
        } else if (this.key === 6) {
          this.numFour = i;
          document.getElementById("numFive").click();
        } else if (this.key === 7) {
          this.numFive = i;
          if (this.radioCarType == "2") {
            document.getElementById("numSix").click();
          }
        } else if (this.key === 8) {
          this.numSix = i;
        }
      },
      // 车牌号第二位输入框选择
      btnClickA() {
        this.isDisable = true;
        this.isA = true;
        this.isYue = false;
        this.isUp = true;
        this.isNumOne = false;
        this.isNumTwo = false;
        this.isNumThree = false;
        this.isNumFour = false;
        this.isNumFive = false;
        this.isNumSix = false;
        this.keyboardShow = true;
        this.keyboard = "num";
        this.key = 2;
      },
      // 车牌号后五位或六位输入框选择
      btnClickNum(name) {
        if (this.isSelectl) {
          this.isDisable = true;
        } else {
          this.isDisable = false;
        }
        this.keyboard = "num";
        this.keyboardShow = true;
        this.isYue = false;
        this.isA = false;
        this.isNumOne = false;
        this.isNumTwo = false;
        this.isNumThree = false;
        this.isNumFour = false;
        this.isNumFive = false;
        this.isNumSix = false;
        this.isUp = true;
        if (name === "one") {
          this.isNumOne = true;
          this.key = 3;
        } else if (name === "two") {
          this.isNumTwo = true;
          this.key = 4;
        } else if (name === "three") {
          this.isNumThree = true;
          this.key = 5;
        } else if (name === "four") {
          this.isNumFour = true;
          this.key = 6;
        } else if (name === "five") {
          this.isNumFive = true;
          this.key = 7;
        } else if (name === "six") {
          this.isNumSix = true;
          this.key = 8;
        }
        if ((name === "five" || name === "six") && this.radioCarType != "2") {
          this.carNumBottom = ["港","Z", "X", "C", "V","B","N","M", "澳"];
          this.noneBottom = [];
        } else {
          this.carNumBottom = ["Z", "X", "C", "V","B","N","M"];
          this.noneBottom = ["", "", "", ""];
        }
      },
      // 键盘点击‘完成’
      completeClick() {
        this.isYue = false;
        this.isA = false;
        this.isNumOne = false;
        this.isNumTwo = false;
        this.isNumThree = false;
        this.isNumFour = false;
        this.isNumFive = false;
        this.isNumSix = false;
        this.isUp = false;
        this.isDown = true;
        this.keyboardShow = false;
      },
      // 键盘点击‘删除图标’
      clearClick() {
        if (this.key === 1) {
          this.areaName = "";
        } else if (this.key === 2) {
          document.getElementById("font").click();
          this.areaLetter = "";
        } else if (this.key === 3) {
          document.getElementById("letter").click();
          this.numOne = "";
        } else if (this.key === 4) {
          document.getElementById("numOne").click();
          this.numTwo = "";
        } else if (this.key === 5) {
          document.getElementById("numTwo").click();
          this.numThree = "";
        } else if (this.key === 6) {
          document.getElementById("numThree").click();
          this.numFour = "";
        } else if (this.key === 7) {
          document.getElementById("numFour").click();
          this.numFive = "";
        } else if (this.key === 8) {
          document.getElementById("numFive").click();
          this.numSix = "";
        }
      },
      // 绑定车牌
      bindCar() {
        if (this.radioCarType != "2") {
          if (
            this.areaName === "" ||
            this.areaLetter === "" ||
            this.numOne === "" ||
            this.numTwo === "" ||
            this.numThree === "" ||
            this.numFour === "" ||
            this.numFive === ""
          ) {
            this.$toast("请输入完整车牌");
            return false;
          }
          this.num =
            this.areaName +
            this.areaLetter +
            this.numOne +
            this.numTwo +
            this.numThree +
            this.numFour +
            this.numFive;
        } else {
          if (
            this.areaName === "" ||
            this.areaLetter === "" ||
            this.numOne === "" ||
            this.numTwo === "" ||
            this.numThree === "" ||
            this.numFour === "" ||
            this.numFive === "" ||
            this.numSix === ""
          ) {
            this.$toast("请输入完整车牌");
            return false;
          }
          this.num =
            this.areaName +
            this.areaLetter +
            this.numOne +
            this.numTwo +
            this.numThree +
            this.numFour +
            this.numFive +
            this.numSix;

        }
        this.$emit( "chooseCarGo",this.num);
      },
    },
  };
</script>

<style lang="less" scoped>
  .activeColor {
    color: #007aff;
  }
  .content-wrapper {
    width: 100%;
    height: auto;
    background-color: #fff;
    margin-top: 0.2rem;
  }

  .content-top-wrapper {
    height: 0.96rem;
    padding: 0 0.3rem;
    font-size: 0.32rem;
    color: #212121;
    border-bottom: 1px solid #f5f5f5;
  }

  .content-top-wrapper img {
    width: 0.34rem;
    height: 0.34rem;
    vertical-align: initial;
  }

  .van-radio-group {
    width: 80%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .shadow {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    background: rgba(000, 000, 000, 0);
  }

  .content-middle-wrapper {
    width: 100%;
    height: 1.4rem;
    border-bottom: 10px solid #f5f5f5;
    padding: 0 0.15rem;
  }

  .plate-number-box {
    width: 100%;
    height: 1rem;
    padding-top: 0.4rem;
  }

  .province-box {
    width: 0.6rem;
    height: 0.6rem;
    position: relative;
  }

  .province-box .province-show {
    width: 100%;
    height: 100%;
    position: absolute;
    border-bottom: 1px solid #c7c7c7;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.36rem;
  }

  .province-box input {
    width: 100%;
    height: 100%;
    opacity: 0;
  }

  .plateNumber-box {
    width: calc(100% - 0.7rem);
    height: 0.6rem;
    position: relative;
    overflow: hidden;
  }

  .plateNumber-box input {
    width: 200%;
    height: 100%;
    opacity: 0;
    color: transparent;
    text-indent: -999em;
    margin-left: -100%;
  }

  .plateNumber-box .plateNumber-show {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .plateNumber-show div {
    width: 10%;
    height: 0.6rem;
    border-bottom: 1px solid #c7c7c7;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.3rem;
  }

  .plateNumber-show div.new-powwer {
    width: 0.85rem;
    font-size: 0.24rem;
    color: #999;
  }

  .van-checkbox__label {
    margin-left: 2px !important;
  }

  .band-btn {
    width: 7rem;
    height: 1.2rem;
    background-color: #007aff;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5rem auto;
    font-size: 0.5rem;
    border: none;
    border-radius: 0.04rem;
  }

  .band-btn:disabled {
    background-color: #bbb;
  }

  .auth-code {
    width: 4rem;
    height: 100%;
    font-size: 0.28rem;
  }

  .send-btn {
    width: 1.52rem;
    height: 0.6rem;
    border-radius: 0.04rem;
    border: 0.02rem solid #12b7f5;
    position: absolute;
    right: 0.3rem;
    top: 0;
    bottom: 0;
    margin: auto;
    font-size: 0.24rem;
    color: #12b7f5;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .send-btn:disabled {
    border-color: #999;
    color: #999999;
  }

  .provinceData-wrapper {
    width: 7rem;
    height: 4rem;
  }

  .provinceData {
    width: 100%;
    height: 100%;
    padding: 0.33rem 0.1rem 0.2rem 0.14rem;
  }

  .province-btn {
    width: 0.6rem;
    height: 0.7rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #b5b5b5;
    border-radius: 0.04rem;
    font-size: 0.28rem;
    margin-left: 0.11rem;
    margin-bottom: 0.15rem;
    border-radius: 0.1rem;
  }

  .area-wrapper {
    width: 100%;
    height: auto;
    padding: 0.3rem;
    background-color: #fff;
    margin-top: 0.2rem;
  }

  .area-wrapper .title {
    font-size: 0.26rem;
    margin-bottom: 0.3rem;
  }

  .menu-wrapper {
    width: 76%;
    height: 0.68rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .menu-wrapper .menu {
    width: 2.16rem;
    height: 0.68rem;
    color: #333333;
    border: 1px solid #cecccc;
    border-radius: 0.08rem;
    font-size: 0.24rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .menu-wrapper .menu.active {
    border-color: #12b7f5;
    background-color: #ebf9fe;
    color: #12b7f5;
  }

  .content-car-number {
    padding-top: 0.1rem;
  }

  .content-car-number .car-content-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 0.32rem;
    margin-bottom: 0.16rem;
  }

  .content-car-number .car-content-title span {
    font-size: 0.34rem;
  }

  .content-car-number .car-content-title .ze-checkbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .content-car-number .car-content-title .ze-radio-text {
    font-size: 0.28rem;
  }

  .content-car-number .car-content-title .ze-checkbox-icon {
    color: #ffedb1 !important;
    margin-right: 0.08rem;
  }

  .content-car-number .content-section .content-section-flex {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    font-size: 0.32rem;
  }

  .content-car-number .content-section .content-section-flex .blank-border {
    height: 0.44rem;
    width: 1px;
    /*no*/
    border-right: 1px solid #ddd;
    /*no*/
    margin-top: 0.22rem;
    /* margin-left: -0.15rem; */
    z-index: 1;
  }

  .content-car-number .content-section .content-section-flex .blank-border.blank-border-first {
    margin-top: 0;
  }

  .content-car-number .content-section .content-section-flex.flex-card {
    color: #000;
  }

  .content-car-number .content-section .content-section-flex.flex-card .flex-btn {
    width: 1.2rem;
    height: 1.2rem;
    border: 1px solid #B2B2B2;
    text-align: center;
    background: #fff;
    font-size: 0.56rem;
    color: #000;
  }

  .content-car-number .content-section .content-section-flex.flex-card .flex-btns {
    display: flex;
    background: #fff;
    position: relative;
    height: 0.88rem;
  }

  .content-car-number .content-section .content-section-flex.flex-card .flex-btns button {
    width: 0.78rem;
    height: 0.88rem;
    border: none;
    font-size: 0.32rem;
    color: #000;
    border-radius: 50%;
    background: transparent;
    z-index: 2;
    position: relative;
  }

  .content-car-number .content-section .content-section-flex.flex-card .flex-btns button:first-child {
    margin-left: 0;
  }

  .content-car-number .content-section .content-section-flex.flex-card .flex-mid {
    margin: 0 0.05rem;
  }

  .content-car-number .content-section .content-section-flex.flex-card .flex-mid span {
    display: block;
    width: 0.1rem;
    height: 0.1rem;
    border-radius: 20rem;
    background: #ddd;
  }

  .content-car-number .content-section .content-section-flex.flex-card .isClick {
    border: 1px solid #12b7f5;
    /*no*/
    z-index: 50;
    border-radius: 0.08rem;
  }

  .content-car-number .content-section .content-section-flex.flex-card .isNumClick {
    border-radius: 0.08rem !important;
    border: 1px solid #12b7f5 !important;
    /*no*/
    z-index: 10;
    background-color: #fff !important;
  }

  .content-car-number .content-section .content-section-flex .flex-card-border {
    // border: 1px solid #ddd;
    /*no*/
    // border-radius: 0.08rem;
  }

  .keyboard {
    width: 100%;
    height: 6rem;
    position: fixed;
    bottom: -5rem;
    background: #eeeeee;
    z-index: 10;
  }

  .keyboard.animationDown {
    -webkit-animation: slide_dowms 0.3s ease-out;
    animation: slide_dowms 0.3s ease-out;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
  }

  .keyboard.animationUp {
    -webkit-animation: slide_ups 0.3s ease-out;
    animation: slide_ups 0.3s ease-out;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    box-shadow: 1px 0 5px #cccccc;
  }

  .keyboard .btn-complete {
    height: 0.9rem;
    text-align: center;
    color: #12b7f5;
    font-size: 0.5rem;
    width: 100%;
    background: #fff;
    border: none;
    border-top: 1px solid #eee;
    margin-bottom: 0.3rem;
  }

  .keyboard .keyboard-row {
    margin-top: 0.2rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 0.08rem;
    color: #333;
  }

  .keyboard .keyboard-row:first-child {
    margin-top: 0;
  }

  .keyboard .keyboard-row .keyboard-row-items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: left;
    -ms-flex-pack: left;
    justify-content: left;
  }

  .keyboard .keyboard-row .keyboard-row-bottom {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .keyboard .keyboard-row .keyboard-row-item {
    width: 0.8rem;
    height: 0.9rem;
    background: #fff;
    font-size: 0.45rem;
    border: 1px solid #ccc;
    line-height: 0.8rem;
    border-radius: 0.1rem;
    color: #333;
  }

  .keyboard-row-item:disabled {
    opacity: 0.7;
  }

  .keyboard .keyboard-row .keyboard-row-item.bottom {
    height: 0.9rem;
    width: 0.8rem;
    line-height: 0.8rem;
    background: #fff;
    color: #333;
    /*margin-right: 0.08rem;*/
  }

  .keyboard .keyboard-row .keyboard-row-item.clear {
    width: 1rem;
    height: 0.8rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .keyboard .keyboard-row .keyboard-row-item.clear img {
    width: 0.62rem;
  }

  .keyboard .keyboard-row .none-botton {
    border: none;
    height: 0.8rem;
    width: 0.66rem;
    visibility: hidden;
  }

  .keyboard button:active {
    background: #f4f4f4 !important;
    color: #999 !important;
  }

  @-webkit-keyframes slide_ups {
    from {
      bottom: -5rem;
    }

    to {
      bottom: 0;
    }
  }

  @keyframes slide_ups {
    from {
      bottom: -5rem;
    }

    to {
      bottom: 0;
    }
  }

  @-webkit-keyframes slide_dowms {
    from {
      bottom: 0;
    }

    to {
      bottom: -5rem;
    }
  }

  @keyframes slide_dowms {
    from {
      bottom: 0;
    }

    to {
      bottom: -5rem;
    }
  }
</style>
