<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">作业人员确认</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>

    </van-sticky>

    <van-cell title="作业人员" @click="showPopup = true" :value="operator" class="all-font-size">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>
    <van-cell title="车牌号" @click="showCarPopup = true"  :value="vehicleNo" class="all-font-size">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>
    <van-cell title="配车单号"   :value="allocateVehicleNo" class="all-font-size">
    </van-cell>
    <van-cell title="允许进厂时间"   :value="workStartTime" class="all-font-size">
    </van-cell>
   <!--  <van-field label="库位" placeholder="库位" v-model="locationName" class="all-font-size" readonly /> -->
    <van-popup v-model="showPopup" position="bottom" :style="{ height: '50%' }">
        <van-search v-model="searchValue" shape="round" background="#007aff" placeholder="请输入搜索作业人员" @search="getOperatorsList"
          left-icon="" :clearable="false">
          <template #right-icon>
            <div class="iconfont icon-sousuo" @click="getOperatorsList" style="color: #999999"></div>
          </template>
        </van-search>
      <van-list>
        <van-cell v-for="item in filteredList" :key="item" :title="item" @click="checkItem(item)"  class="all-font-size" />
      </van-list>
    </van-popup>
    <van-popup v-model="showCarPopup" position="bottom" :style="{ height: '50%' }">
      <van-list style="">
        <van-cell v-for="item in vehicleList" :key="item.vehicleNo" :title="item.vehicleNo"  class="all-font-size" @click="checkCarItem(item)" />
      </van-list>
    </van-popup>
    <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" v-preventReClick="3000"
      @click="onConfirm">
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;认
      </button>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import chooseCarNumber from "@/components/carNumber.vue";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        allocateVehicleNo:'',
        workStartTime:'',
        operatorsList:[],
        searchValue: '',
        operator: '',
        showCarPopup: false,
        showPopup: false,
        showSearch: true,
        vehicleList: [],
        remark: "",
        currentIndex: -1,
        value: "",
        testCar: "",
        check: false,
        vehicleNo: "",
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    computed: {
      filteredList() {
        return this.operatorsList.filter((item) =>
          item.toLowerCase().includes(this.searchValue.toLowerCase())
        );
      },
    },
    created() {
      this.getOperatorsList();
    },
    components: {
      chooseCarNumber,
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
         this.$router.goBack();
      },
      onInput() {
        this.showPopup = true;
      },
      onSearch() {
        // 处理搜索逻辑
      },
      onCancel() {
        this.showPopup = false;
      },
      checkItem(val){
        this.operator = val;
        this.searchValue = val;
        this.showPopup = false;
        this.getBusinessVehicle();
      },
      checkCarItem(val){
        this.showCarPopup = false;
        this.vehicleNo = val.vehicleNo;
        this.allocateVehicleNo = val.allocateVehicleNo;
        this.workStartTime = val.workStartTime;
      },
      //点击确认
      async onConfirm(){
        const params = {
          serviceId: "S_UC_PR_211203",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          userCn: this.operator,
          vehicleNo:this.vehicleNo,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                  this.$toast(res.data.__sys__.msg);
                  this.vehicleNo = "";
                  this.allocateVehicleNo = "";
                  this.operator = "";
                  this.workStartTime = "";
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取作业人员列表
      async getOperatorsList() {
        const params = {
          serviceId: "S_UC_PR_211201",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          userCn: this.searchValue,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result.length > 0) {
                  this.operatorsList = dataObj.result;
                } else {
                  this.$toast("未查询到相关列表！");
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询作业人员车辆关系
      async getBusinessVehicle() {
        const params = {
          serviceId: "S_UC_PR_211202",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          userCn: this.operator,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result.length > 0) {
                  this.vehicleList = dataObj.result;
                } else {
                  this.$toast("未查询到相关列表！");
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  .load-content {
    padding-bottom: 120px;
  }

  .activeColor {
    color: #007aff;
  }

  .search-content {
    padding: 15px;


    .search-history {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #DCDCDC;
    }

    .list-content {
      display: flex;
      flex-wrap: wrap;
      padding-top: 10px;

      .content-item {
        background-color: #DCDCDC;
        border-radius: 20px;
        padding: 5px 10px 5px 10px;
        margin-left: 5px;
        margin-top: 5px;
        font-size: 15px;
      }
    }
  }

  .load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
  }

  .load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
  }
</style>
