<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">签名</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>

    </van-sticky>
    <div>
       <van-field label="配车单号" v-model="allocateVehicleNo" class="all-font-size" readonly />
       <van-popup :style="{ height: '100%' }" v-model="signShow" get-container="body" position="bottom">
         <signCanvas @close="closeDialog" :d_h="h" :d_w="w" :title="title"></signCanvas>
         <!--  <div class="canvans-tip">提示模块</div> -->
       </van-popup>
       <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" v-preventReClick="3000" @click="onConfirm">
         <button type="button" class="mui-btn">
           确&nbsp; &nbsp; &nbsp;&nbsp;认
         </button>
       </div>
    </div>
    </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
 import {
    isEmpty
  } from '@/utils/tools';
import { Dialog } from "vant";
  import signCanvas from '@/components/signature.vue';
export default {
  data() {
    return {
      vehicleList: [],
      resultImg:'',
      signShow:false,
      currentIndex: -1,
      allocateVehicleNo: "",
      title:"门卫签名",
      vehicleNo: "",
      check: false,
      w: document.documentElement.clientWidth, //实时屏幕宽度
      h: document.documentElement.clientHeight, //实时屏幕高度
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  created() {

  },
  components:{
    signCanvas
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    onConfirm(){
      this.signShow = true;
    },
    closeDialog(e) {
      // 签名赋值 这里的e就是base64了，直接使用
      // 关闭签名框,这里要调用上传图片接口不要直接确认

      if (!isEmpty(e)) {
        this.resultImg = e;
       // this.uploadImage([e]);
        //this.toConfirm();
      }
      this.signShow = false;

    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  padding-bottom: 100px;
}

.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
}

.activeColor {
  color: #007aff;
}
</style>
