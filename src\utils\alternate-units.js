//单位换算：数量，原单位，目标单位
 export function GetNumByUnit(num, unitname, outunitname) {
	var fRate = { //换算率
		ng: {
			μg: 0.001,
			mg: 0.001 * 0.001,
			g: 0.001 * 0.001 * 0.001,
			kg: 0.001 * 0.001 * 0.001 * 0.001,
			t: 0.001 * 0.001 * 0.001 * 0.001 * 0.001,
			ul: 0.001 * 0.001,
			ml: 0.001 * 0.001 * 0.001,
			L: 0.001 * 0.001 * 0.001 * 0.001
		},
		μg: {
			ng: 1000,
			mg: 0.001,
			g: 0.001 * 0.001,
			kg: 0.001 * 0.001 * 0.001,
			t: 0.001 * 0.001 * 0.001 * 0.001,
			ul: 0.001,
			ml: 0.001 * 0.001,
			L: 0.001 * 0.001 * 0.001
		},
		mg: {
			ng: 1000 * 1000,
			μg: 1000,
			g: 0.001,
			kg: 0.001 * 0.001,
			t: 0.001 * 0.001 * 0.001,
			ul: 1,
			ml: 0.001,
			L: 0.001 * 0.001
		},
		g: {
			ng: 1000 * 1000 * 1000,
			μg: 1000 * 1000,
			mg: 1000,
			kg: 0.001,
			t: 0.001 * 0.001,
			ul: 1000,
			ml: 1,
			L: 0.001
		},
		kg: {
			ng: 1000 * 1000 * 1000 * 1000,
			μg: 1000 * 1000,
			mg: 1000,
			g: 1000,
			t: 0.001,
			ul: 1000 * 1000,
			ml: 1000,
			L: 1
		},
		t: {
			ng: 1000 * 1000 * 1000 * 1000 * 1000,
			μg: 1000 * 1000 * 1000,
			mg: 1000 * 1000,
			g: 1000 * 1000,
			kg: 1000,
			ul: 1000 * 1000 * 1000,
			ml: 1000 * 1000,
			L: 1000
		},
		ml: {
			ng: 1000 * 1000 * 1000,
			μg: 1000 * 1000,
			mg: 1000,
			g: 1,
			kg: 0.001,
			t: 0.001 * 0.001,
			ul: 1000,
			L: 0.001
		},
		ul: {
			ng: 1000 * 1000,
			μg: 1000,
			ml: 1,
			g: 0.001,
			kg: 0.001 * 0.001,
			t: 0.001 * 0.001 * 0.001,
			ml: 0.001,
			L: 0.001 * 0.001
		},
		L: {
			ng: 1000 * 1000 * 1000 * 1000,
			μg: 1000 * 1000,
			mg: 1000,
			g: 1000,
			kg: 1,
			t: 0.001,
			ul: 1000 * 1000,
			ml: 1000
		},
	};
	var tnum = (num * fRate[unitname][outunitname]).toFixed(2);
	return tnum;
};
