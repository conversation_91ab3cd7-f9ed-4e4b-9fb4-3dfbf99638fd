<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">入库捆包清单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuoji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
        <!-- <template #right>
          <span @click="onClickRight" class="title-add">添加</span>
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <in-scan ref="child" @scanInfo="getScanInfo" @noInPack="getNoInPackInfo" @closeDialog="closeDialog"></in-scan>
    <van-tabs v-model="active" color="#007aff" style="width: 100%" line-width="60px" offset-top="44px"
      title-active-color="#007aff" sticky>
      <van-tab title="匹配入库计划" :title-style="fontSize">
        <div v-if="packList && packList.length > 0">
          <div class="inlist-content">
            <div class="detail_textarea">
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  已扫捆包合计 :
                  <span class="span-count">{{ packList.length }}</span>
                </div>
              </div>
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  总件数 :
                  <span class="span-count">{{ totalPieceNum }}</span>
                  总吨数 :
                  <span class="span-count">{{
                    formatNumber(totalNetWeight)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="content-cell">
              <van-swipe-cell v-for="(item, index) in packList" :key="index">
                <div style="text-align: left">
                  <div class="detail_textarea">
                    <div class="border_top">
                      <div>
                        <span class="check-spec">捆包：</span>
                        <span class="check-val div-pack">{{
                          item.packId
                        }}</span>
                      </div>
                      <div>
                        <span class="check-spec">入库计划：</span>
                        <span class="check-val">{{ item.putinPlanId }}</span>
                      </div>
                      <div>
                        <span class="check-spec">钢厂订单号：</span>
                        <span class="check-val">{{ item.factoryOrderNum }}</span>
                      </div>
                      <div>
                        <span class="check-spec div-tips">牌号：</span>
                        <span class="check-val div-tips">{{ item.shopsign }}</span>
                      </div>
                      <div>
                        <span class="check-spec div-tips">品种：</span>
                        <span class="check-val div-tips">{{ item.prodCname }}</span>
                      </div>
                      <div>
                        <span class="check-spec div-tips">提示：</span>
                        <span class="check-val div-tips">{{
                          item.noticeMsg
                        }}</span>
                      </div>
                      <div v-show="item.cargoDamageType.length > 0">
                        <span class="check-spec div-tips">货损类型：</span>
                        <span class="check-val div-tips">{{
                          item.cargoDamageType.toString()
                        }}</span>
                      </div>
                      <div class="content-spec">
                        <div>
                          <span class="check-spec">库位：</span>
                          <span class="check-val">{{ item.location }}</span>
                        </div>
                        <div v-show="item.pictureDetail.length > 0">
                          <van-button type="info" size="mini" @click="showImg(item, index)">图片</van-button>
                        </div>
                      </div>
                      <div>
                        <span class="check-spec">规格：</span>
                        <span class="check-val">{{ item.specsDesc }}</span>
                      </div>
                      <div class="content-spec">
                        <div>
                          <span class="check-spec">净/毛重：</span>
                          <span class="check-val">{{ item.netWeight }}/{{ item.grossWeight }}</span>
                        </div>
                        <div>
                          <span class="check-spec">贸易：</span>
                          <span class="check-val">{{
                            item.tradeCodeName
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <template #right>
                  <!--  <button class="swiper-btn-update" @click="updateListItem">
                    <span class="swiper-text">修改</span>
                  </button> -->
                  <button class="swiper-btn-delete" @click="deleteListItem(index)">
                    <span class="swiper-text">删除</span>
                  </button>
                </template>
              </van-swipe-cell>
            </div>
          </div>
          <div v-if="packList && packList.length > 0">
            <div class="mui-input-row3" v-show="isShowBottom">
              <button id="block_button" type="button" class="mui-btn3" @click="onConfirm">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
              <button type="button" class="mui-btn3" v-preventReClick="3000" @click="onInStorage">
                入&nbsp; &nbsp; &nbsp;&nbsp;库
              </button>
            </div>
          </div>
        </div>
        <div v-else>
          <van-empty description="暂无捆包清单，请添加扫描" class="all-font-size" />
          <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
            <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfirm">
              确&nbsp; &nbsp; &nbsp;&nbsp; 认
            </button>
          </div>
        </div>
      </van-tab>
      <van-tab title="未匹配" :title-style="fontSize">
        <div v-if="noInPackList && noInPackList.length > 0">
          <div class="inlist-content">
            <div class="content-cell">
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  已扫捆包合计 :
                  <span class="span-count">{{ noInPackList.length }}</span>
                </div>
              </div>


              <van-swipe-cell v-for="(item, index) in noInPackList" :key="index">
                <div style="text-align: left">
                  <div class="detail_textarea">
                    <div class="detail_text">
                      <div class="fourtext3">{{ item.packId }}</div>
                    </div>

                    <div class="border_top">
                      <div>
                        <span class="check-spec" style="color: #ff0000">未匹配入库计划</span>
                      </div>
                      <div v-if="item.factoryOrderNum">
                        <span class="check-spec">钢厂订单号：</span>
                        <span class="check-val">{{ item.factoryOrderNum }}</span>
                      </div>

                      <div v-show="item.cargoDamageType.length > 0">
                        <span class="check-spec div-tips">货损类型：</span>
                        <span class="check-val div-tips">{{
                          item.cargoDamageType.toString()
                        }}</span>
                      </div>
                      <div class="content-spec">
                        <div>
                          <span class="check-spec">库位</span>
                          <span class="check-val">{{ item.location }}</span>
                        </div>
                        <div v-show="item.pictureDetail.length > 0">
                          <van-button type="info" size="mini" @click="showImg(item, index)">图片</van-button>
                        </div>
                      </div>
                      <div class="content-spec">
                        <div v-if="item.specsDesc">
                          <span class="check-spec">规格：</span>
                          <span class="check-val">{{ item.specsDesc }}</span>
                        </div>
                        <div v-if="item.netWeight">
                          <span class="check-spec">净重：</span>
                          <span class="check-val">{{ item.netWeight }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <template #right>
                  <!-- <button class="swiper-btn-update2" @click="updateListItem(item,index)">
                    <span class="swiper-text">修改</span>
                  </button> -->
                  <button class="swiper-btn-delete2" @click="deleteListItem2(index)">
                    <span class="swiper-text">删除</span>
                  </button>
                </template>
              </van-swipe-cell>
            </div>
            <div class="mui-input-row3" style="margin: 0" v-show="isShowBottom">
              <button type="button" class="mui-btn3" v-preventReClick="3000" @click="onConfirm">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
              <button type="button" class="mui-btn3" v-preventReClick="3000" @click="unplannedWarehous">
                无计划入库
              </button>
            </div>
          </div>
        </div>
        <div v-else>
          <van-empty description="暂无捆包清单，请添加扫描" />
          <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
            <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfirm">
              确&nbsp; &nbsp; &nbsp;&nbsp; 认
            </button>
          </div>
        </div>
      </van-tab>
    </van-tabs>
    <van-popup v-model="showImgList" :style="{ height: '50%' }" position="bottom">
      <div class="img-list">
        <div v-for="(item, index) in imgList" :key="item.thumbnailDocId" class="div-item">
          <van-badge color="#007aff">
            <van-image width="120" height="120" :src="item.thumbnailDocUrl" @click="seeBigImg(item, index)" />
            <template #content>
              <div class="iconfont  icon-qingchu " style="font-size: 15px;" @click="deleteImg(index)"></div>
            </template>
          </van-badge>
        </div>
      </div>
    </van-popup>
    <van-image-preview v-model="showBigImage" :images="images" :start-position="imgIndex" @change="onChange">
      <template v-slot:index>第{{ imgIndex + 1 }}页</template>
    </van-image-preview>
    <van-dialog v-model="showField" title="修改信息" :beforeClose="beforeCloseField" show-cancel-button>
      <van-field clickable v-model="factoryOrderNum" label="钢厂资源号" placeholder="请填写" />
      <van-field clickable v-model="specsDesc" label="规格" placeholder="请填写" />
      <van-field clickable v-model="netWeight" label="净重" placeholder="请填写" />

    </van-dialog>
  </div>
</template>

<script>
  import {
    Dialog
  } from "vant";
  import inScan from "./index.vue";
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty
  } from "@/utils/tools";
  var elitetyc = require("../../../static/js/plugin2.js");
  export default {
    data() {
      return {
        remark: '', //接收备注
        showField: false,
        plugins: elitetyc,
        factoryOrderNum: '',
        specsDesc: '',
        netWeight: '',
        fieldIndex: -1,
        checkIndex: -1,
        packList: [],
        imgList: [],
        showBigImage: false,
        imgIndex: 0,
        images: [],
        cargoDamageType: [],
        showImgList: false,
        pushList: [],
        noInPackList: [],
        putinIdLists: [],
        putinType: "",
        vehicleNo: "",
        show: false,
        confirmColor: "#0000ff",
        active: 2,
        location: "",
        fontSize: " font-size: 16px;",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight
      };
    },
    components: {
      inScan
    },
    created() {
      console.log("this.$route", this.$route.query);
      this.vehicleNo = this.$route.query.carNumber;
      this.remark = this.$route.query.remark;
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
      if (window.history && window.history.pushState) {
        // 往历史记录里面添加一条新的当前页面的url
        history.pushState(null, null, document.URL);
        // 给 popstate 绑定一个方法 监听页面刷新
        window.addEventListener("popstate", this.backChange, false); //false阻止默认事件
      }
    },
    computed: {
      totalPieceNum() {
        let allPiece = this.packList.reduce(
          (sum, e) => sum + Number(e.pieceNum || 0),
          0
        );
        return allPiece;
      },
      totalNetWeight() {
        let allWeight = this.packList.reduce(
          (sum, e) => sum + Number(e.netWeight || 0),
          0
        );
        return allWeight;
      }
    },
    methods: {
      onClickLeft() {
        if (this.packList.length > 0 || this.noInPackList.length > 0) {
          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => {
              window.history.back();
              window.removeEventListener("popstate", this.backChange);
              this.$router.goBack()
            })
            .catch(() => {});
          return;
        }
        this.$router.goBack();
      },
      backChange() {
        // 手机自带返回
        if (this.packList.length > 0 || this.noInPackList.length > 0) {
          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => {
              this.$router.goBack();
            })
            .catch(() => {
              if (window.history && window.history.pushState) {
                // 手机点击了物理返回 然后又选择了取消 这时需要在添加一条记录
                history.pushState(null, null, document.URL);
              }
            });
          return;
        }
        this.$router.goBack();
      },
      seeBigImg(item, index) {
        this.imgIndex = index;
        var prodData = this.imgList.map(function(item) {
          return item["docUrl"];
        });
        this.showBigImage = true;
        this.images = prodData;
      },
      showImg(item, index) {
        this.showImgList = true;
        this.imgList = item.pictureDetail;
        this.checkIndex = index;
      },
      onChange(index) {
        this.imgIndex = index;
      },
      updateListItem(item, index) {
        this.showField = true;
        this.factoryOrderNum = item.factoryOrderNum;
        this.netWeight = item.netWeight;
        this.specsDesc = item.specsDesc;
        this.fieldIndex = index;
        //this.$router.togo("/instorage");
      },
      formatNumber(val) {
        return Math.round(val * 1000) / 1000; // 保留3位小数
      },
      onClickRight() {
        this.show = true;
        //  this.$router.togo("/instorage");
      },
      closeDialog() {
        this.show = false;
      },
      getScanInfo(val, val2, val3, val4, val5, val6) {
        this.location = val3;
        this.putinType = val2;
        val[0].location = this.location;
        val[0].locationName = val4;
        val[0].pictureDetail = val5;
        val[0].cargoDamageType = val6;
        let flag = this.packList.some(item => {
          return (
            item.matInnerId === val[0].matInnerId &&
            item.tradeCode === val[0].tradeCode
          );
        });
        if (isEmpty(val[0].location)) {
          this.$toast("库位为空！");
        } else {
          if (isEmpty(val[0].locationName)) {
            this.$toast("库位名称为空！");
          } else {
            if (flag) {
              this.$toast("此捆包号已添加，请重新扫描其他捆包号");
            } else {
              this.packList = [...val, ...this.packList];
            }
          }
        }
      },
      getNoInPackInfo(val) {
        //获取的值添加到list里面
        let flag = this.noInPackList.some(item => {
          return item.packId === val[0].packId;
        });
        if (flag) {
          this.$toast("此捆包号已添加，请重新扫描其他捆包号");
        } else {
          this.noInPackList = [...val, ...this.noInPackList];
        }
        console.log(this.noInPackList);
      },
      onConfirm() {
        this.$refs.child.saveBale();
      },
      beforeCloseField(action, done) {
        if (action === "confirm") {
          //添加前进判断调用哪个方法
          this.noInPackList[this.fieldIndex].netWeight = this.netWeight;
          this.noInPackList[this.fieldIndex].specsDesc = this.specsDesc;
          this.noInPackList[this.fieldIndex].specDesc = this.specsDesc;
          this.noInPackList[this.fieldIndex].factoryOrderNum = this.factoryOrderNum;
          console.log(this.noInPackList[this.fieldIndex]);
          //  this.$refs.child.saveBale();
          this.fieldIndex = -1;
          done();
          //  setTimeout(done, 1000);
        } else {
          this.fieldIndex = -1;
          this.factoryOrderNum = '';
          this.netWeight = '';
          this.specsDesc = '';
          done();
        }
      },
      beforeClose(action, done) {
        if (action === "confirm") {
          //添加前进判断调用哪个方法
          this.$refs.child.saveBale();
          done();
          //  setTimeout(done, 1000);
        } else {
          done();
        }
      },
      beforeClose2(action, done) {
        if (action === "confirm") {
          done();
          this.inStorage();
          //setTimeout(done, 1000);
        } else {
          done();
        }
      },

      onInStorage() {
        let that = this
        if (window.plus) {
          plus.nativeUI.confirm("下列匹配入库计划捆包列表确认要入库？", function(e) {
            if (e.index == 0) {
              that.inStorage();
              console.log("You clicked OK!");
            } else {
              console.log("You clicked Cancel!");
            }
          }, "提示", ["确认", "取消"]);
        } else {
          Dialog.confirm({
              title: '提示',
              message: '下列匹配入库计划捆包列表确认要入库？',
            })
            .then(() => {
              // on confirm
              that.inStorage();
            })
            .catch(() => {
              // on cancel
            });
        }

      },
      async inStorage() {
        let that = this;
        const segNo = localStorage.getItem("segNo");
        const query = {
          serviceId: "S_UC_EW_0387",
          packList: that.noInPackList.map(p => {
            return {
              ...p,
              segNo,
            };
          }),
        };
        const resultAlert = await baseApi.baseService(query);
        const dataAlert = resultAlert.data;
        if (dataAlert.dateStatus == 0) {
          if (window.plus) {
            plus.nativeUI.confirm(dataAlert.dateMsg, async function(e) {
              if (e.index == 0) {
                await that.continueWarehouse();
                console.log("You clicked OK!");
              }
            }, "提示", ["确认", "取消"]);
          } else {
            Dialog.confirm({
                title: '提示',
                message: dataAlert.dateMsg,
              })
              .then(async () => {
                await this.continueWarehouse();
              })
              .catch(() => {
                // on cancel
              });
          }
          return;
        }
        await that.continueWarehouse();
        // const params = {
        //   serviceId: "S_UC_PR_230636",
        //   iplat_transactionType: 3,
        //   userId: localStorage.getItem("userId"),
        //   accessToken: localStorage.getItem("accessToken"),
        //   segNo,
        //   segCname: localStorage.getItem("segName"),
        //   warehouseCode: localStorage.getItem("warehouseCode"),
        //   warehouseName: localStorage.getItem("warehouseName"),
        //   factoryArea: localStorage.getItem("factoryArea"),
        //   factoryAreaName: localStorage.getItem("factoryName"),
        //   putinType: that.putinType,
        //   vehicleNo: that.vehicleNo,
        //   rowList: that.packList,
        //   teamId: localStorage.getItem("groupVal"),
        //   loadingPointNo: sessionStorage.getItem("pdaLoadNo") || "",
        //   loadingPointName: sessionStorage.getItem("pdaLoadName") || "",
        //   workingShift: localStorage.getItem("classVal"),
        //   noInPackList: that.noInPackList,
        // };
        // await baseApi
        //   .baseService(params)
        //   .then(res => {
        //     if (res.data) {
        //       let dataObj = null;
        //       try {
        //         dataObj = res.data;
        //       } catch (e) {
        //         dataObj = {
        //           __sys__: {
        //             msg: "调用失败",
        //             status: -1
        //           }
        //         };
        //       }
        //       if (dataObj && dataObj.__sys__.status != -1) {
        //         if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
        //           if (window.plus) {
        //             plus.nativeUI.confirm(`${dataObj.__sys__.msg},是否进入下一步操作?`, function(e) {
        //               if (e.index == 0) {
        //                 that.$router.togo({
        //                   name: "endLoad",
        //                   params: {
        //                     putinIdList: dataObj.putinIdList,
        //                     carNumber: that.vehicleNo
        //                   },
        //                 });
        //                 console.log("You clicked OK!");
        //               } else {
        //                 console.log("You clicked Cancel!");
        //               }
        //             }, "提示", ["确认", "取消"]);
        //           } else {
        //             Dialog.confirm({
        //                 title: '提示',
        //                 message: `${dataObj.__sys__.msg},是否进入下一步操作?`,
        //               })
        //               .then(() => {
        //                 // on confirm
        //                 that.$router.togo({
        //                   name: "endLoad",
        //                   params: {
        //                     putinIdList: dataObj.putinIdList,
        //                     carNumber: that.vehicleNo
        //                   },
        //                 });
        //               })
        //               .catch(() => {
        //                 // on cancel
        //               });
        //           }

        //         }
        //         that.$toast(dataObj.__sys__.msg);
        //         that.packList = [];
        //         that.noInPackList = [];
        //         that.$refs.child.clearLoction();
        //       } else {
        //         that.$toast(res.data.__sys__.msg);
        //       }
        //     }
        //   })
        //   .catch(err => {
        //     console.log(err);
        //   });
      },
      async continueWarehouse() {
        let that = this
        const segNo = localStorage.getItem("segNo");
        const params = {
          serviceId: "S_UC_PR_230636",
          iplat_transactionType: 3,
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo,
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          putinType: that.putinType,
          vehicleNo: that.vehicleNo,
          remark: that.remark,
          rowList: that.packList,
          teamId: localStorage.getItem("groupVal"),
          loadingPointNo: sessionStorage.getItem("pdaLoadNo") || "",
          loadingPointName: sessionStorage.getItem("pdaLoadName") || "",
          workingShift: localStorage.getItem("classVal"),
          noInPackList: that.noInPackList,
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                that.putinIdLists = dataObj.putinIdList;
                //加个判断是否蓝牙打印
                that.$toast(dataObj.__sys__.msg);
                that.packList = [];
                that.noInPackList = [];
                that.$refs.child.clearLoction();
                if (dataObj.returnMain == 0) {
                  this.$router.goBack();
                  return;
                }
                if (dataObj.ejectPrint == 1) {
                  if (window.plus) {
                    plus.nativeUI.confirm(`是否进行蓝牙打印`, function(e) {
                      if (e.index == 0) {
                        that.getBaleByMatInnerId(dataObj.matInnerIdList);
                        console.log("You clicked OK!");
                      } else {
                        that.nextSelectionJudgment(dataObj);
                        console.log("You clicked Cancel!");
                      }
                    }, "提示", ["确认", "取消"]);
                  } else {
                    Dialog.confirm({
                        title: '提示',
                        message: `是否进行蓝牙打印`,
                      })
                      .then(() => {
                        // on confirm
                        that.getBaleByMatInnerId(dataObj.matInnerIdList);
                      })
                      .catch(() => {
                        // on cancel
                        that.nextSelectionJudgment(dataObj);
                      });
                  }
                } else {
                  that.nextSelectionJudgment(dataObj);
                }


              } else {
                that.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      nextSelectionJudgment(dataObj) {
        let that = this;
        if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
          if (window.plus) {
            plus.nativeUI.confirm(`${dataObj.__sys__.msg},是否进入下一步操作?`, function(e) {
              if (e.index == 0) {
                that.$router.togo({
                  name: "endLoad",
                  params: {
                    putinIdList: dataObj.putinIdList,
                    carNumber: that.vehicleNo
                  },
                });
                console.log("You clicked OK!");
              } else {
                console.log("You clicked Cancel!");
              }
            }, "提示", ["确认", "取消"]);
          } else {
            Dialog.confirm({
                title: '提示',
                message: `${dataObj.__sys__.msg},是否进入下一步操作?`,
              })
              .then(() => {
                // on confirm
                that.$router.togo({
                  name: "endLoad",
                  params: {
                    putinIdList: dataObj.putinIdList,
                    carNumber: that.vehicleNo
                  },
                });
              })
              .catch(() => {
                // on cancel
              });
          }

        }
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByMatInnerId(val) {
        let that = this;
        const params = {
          serviceId: "S_UC_PR_200005",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          matInnerIdList: val,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.__sys__.status != -1) {
                let resultList = res.data.result;
                console.log(resultList);
                that.$toast("开始调用蓝牙打印");
                res.data.result.forEach(item => {
                  item.netWeight = item.netWeight ? ((Number(item.netWeight) * 1000)).toFixed(2) : 0;
                  item.grossWeight = item.grossWeight ? ((Number(item.grossWeight) * 1000)).toFixed(2) : 0;
                  item.printCount = 1;
                  item.segNo = localStorage.getItem("segNo");
                })
                console.log(res.data.result);
                that.plugins.CalcNameAddNumFunction2(res.data.result, function(result) {
                  if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
                    that.$router.togo({
                      name: "endLoad",
                      params: {
                        putinIdList: that.putinIdLists,
                        carNumber: that.vehicleNo
                      },
                    });
                  }
                  console.log(JSON.stringify(result));
                  // resolve();
                });

                // that.printBale2(resultList);

              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async printBale2(valList) {
        for (let item of valList) {
          let params = {
            ...item,
            netWeight: item.netWeight ? ((Number(item.netWeight) * 1000)).toFixed(2) : 0,
            grossWeight: item.grossWeight ? ((Number(item.grossWeight) * 1000)).toFixed(2) : 0,
            printCount: 1,
            segNo: localStorage.getItem("segNo"),
          };
          await this.callNativeFunction(params);
          await this.delay(10000);
        }
        if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
          this.$router.togo({
            name: "endLoad",
            params: {
              putinIdList: this.putinIdLists,
              carNumber: this.vehicleNo
            },
          });
        }
      },

      async callNativeFunction(params) {
        return new Promise((resolve, reject) => {
          this.plugins.CalcNameAddNumFunction(params, function(result) {
            console.log(JSON.stringify(result));
            resolve(result);
          });
        });
      },

      delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
      },
      async printBale3(valList) {
        let that = this;
        for (let item of valList) {
          let params = {
            ...item,
            netWeight: item.netWeight ? (Number(item.netWeight) * 1000) : 0,
            grossWeight: item.grossWeight ? (Number(item.grossWeight) * 1000) : 0,
            printCount: 1,
            segNo: localStorage.getItem("segNo"),
          };
          await new Promise((resolve, reject) => {
            that.plugins.CalcNameAddNumFunction(params, function(result) {
              console.log(JSON.stringify(result));
              resolve();
            });
          });
        }
      },
      Scrollbottom() {
        let scrollTop =
          document.documentElement.scrollTop || document.body.scrollTop;
        let clientHeight = document.documentElement.clientHeight;
        let scrollHeight = document.documentElement.scrollHeight;
        if (scrollTop + clientHeight >= scrollHeight) {
          console.log("滚动到底部了");
          // this.pageNo++;
          // console.log(this.pageNo);
          // this.fetchData();
        }
      },
      deleteListItem(index) {
        //删除数组中值
        this.$delete(this.packList, index);
      },
      deleteImg(val) {
        this.$delete(this.packList[this.checkIndex].pictureDetail, val);
      },
      deleteListItem2(index) {
        //删除数组中值
        this.$delete(this.noInPackList, index);
      },
      // 无计划入库
      async unplannedWarehous() {
        let that = this;
        const segNo = localStorage.getItem("segNo");
        const query = {
          serviceId: "S_UC_EW_0387",
          packList: that.noInPackList.map(p => {
            return {
              ...p,
              segNo,
            };
          }),
        };
        const resultAlert = await baseApi.baseService(query);
        const dataAlert = resultAlert.data;
        if (dataAlert.dateStatus == 0) {
          if (window.plus) {
            plus.nativeUI.confirm(dataAlert.dateMsg, async function(e) {
              if (e.index == 0) {
                await that.unContinueWarehouse();
                console.log("You clicked OK!");
              }
            }, "提示", ["确认", "取消"]);
          } else {
            Dialog.confirm({
                title: '提示',
                message: dataAlert.dateMsg,
              })
              .then(async () => {
                await that.unContinueWarehouse();
              })
              .catch(() => {
                // on cancel
              });
          }
          return;
        }
        await that.unContinueWarehouse();
        // let that = this;
        // const segNo = localStorage.getItem("segNo");
        // const query = {
        //   serviceId: "S_UC_EW_0387",
        //   packList: that.noInPackList.map(p => {
        //     return {
        //       ...p,
        //       segNo,
        //     };
        //   }),
        // };
        // const resultAlert = await baseApi.baseService(query);
        // const dataAlert = resultAlert.data;
        // let isAlert = undefined;
        // if (dataAlert.dateStatus == 0) {
        //     const dia = await Dialog.confirm({
        //           title: '提示',
        //           message: dataAlert.dateMsg,
        //         })
        //         .catch(() => {});
        //         isAlert = dia;
        // }
        // if (!isAlert) {
        //     return;
        // }
        // const params = {
        //   serviceId: "S_UC_PR_230635",
        //   userId: localStorage.getItem("userId"),
        //   accessToken: localStorage.getItem("accessToken"),
        //   segNo,
        //   segCname: localStorage.getItem("segName"),
        //   factoryArea: localStorage.getItem("factoryArea"),
        //   factoryAreaName: localStorage.getItem("factoryName"),
        //   warehouseCode: localStorage.getItem("warehouseCode"),
        //   warehouseName: localStorage.getItem("warehouseName"),
        //   loadingPointNo: sessionStorage.getItem("pdaLoadNo") || "",
        //   loadingPointName: sessionStorage.getItem("pdaLoadName") || "",
        //   vehicleNo: that.vehicleNo,
        //   rowList: that.noInPackList
        // };
        // const resultData = await baseApi.baseService(params);
        // if (!resultData || !resultData.data) {
        //   that.$toast("调用失败");
        //   return;
        // }
        // if (resultData.data.__sys__.status == 1) {
        //   that.noInPackList = [];
        //   console.log("======res", resultData.data.result);
        //   if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
        //     if (window.plus) {
        //       plus.nativeUI.confirm(`${resultData.data.__sys__.msg},是否进入下一步操作?`, function(e) {
        //         if (e.index == 0) {
        //           that.$router.togo({
        //             name: "endLoad",
        //             params: {
        //               noPlanList: resultData.data.result,
        //               carNumber: that.vehicleNo
        //             },
        //           });
        //           console.log("You clicked OK!");
        //         } else {
        //           console.log("You clicked Cancel!");
        //         }
        //       }, "提示", ["确认", "取消"]);
        //     } else {
        //       Dialog.confirm({
        //           title: '提示',
        //           message: `${resultData.data.__sys__.msg},是否进入下一步操作?`,
        //         })
        //         .then(() => {
        //           // on confirm
        //           this.$router.togo({
        //             name: "endLoad",
        //             params: {
        //               noPlanList: resultData.data.result,
        //               carNumber: that.vehicleNo
        //             },
        //           });
        //         })
        //         .catch(() => {
        //           // on cancel
        //         });
        //     }

        //   }
        // }
        // that.$toast(resultData.data.__sys__.msg);
      },

      async unContinueWarehouse() {
        let that = this
        const segNo = localStorage.getItem("segNo");
        const params = {
          serviceId: "S_UC_PR_230635",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo,
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          loadingPointNo: sessionStorage.getItem("pdaLoadNo") || "",
          loadingPointName: sessionStorage.getItem("pdaLoadName") || "",
          vehicleNo: that.vehicleNo,
          rowList: that.noInPackList
        };
        const resultData = await baseApi.baseService(params);
        if (!resultData || !resultData.data) {
          that.$toast("调用失败");
          return;
        }
        if (resultData.data.__sys__.status == 1) {
          that.noInPackList = [];
          console.log("======res", resultData.data.result);
          if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
            if (window.plus) {
              plus.nativeUI.confirm(`${resultData.data.__sys__.msg},是否进入下一步操作?`, function(e) {
                if (e.index == 0) {
                  that.$router.togo({
                    name: "endLoad",
                    params: {
                      noPlanList: resultData.data.result,
                      carNumber: that.vehicleNo
                    },
                  });
                  console.log("You clicked OK!");
                } else {
                  console.log("You clicked Cancel!");
                }
              }, "提示", ["确认", "取消"]);
            } else {
              Dialog.confirm({
                  title: '提示',
                  message: `${resultData.data.__sys__.msg},是否进入下一步操作?`,
                })
                .then(() => {
                  // on confirm
                  this.$router.togo({
                    name: "endLoad",
                    params: {
                      noPlanList: resultData.data.result,
                      carNumber: that.vehicleNo
                    },
                  });
                })
                .catch(() => {
                  // on cancel
                });
            }

          }
        }
        that.$toast(resultData.data.__sys__.msg);
      }
    },
    beforeDestroy() {
      window.removeEventListener("popstate", this.backChange);
    }
  };
</script>

<style lang="less" scoped>
  .inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  /deep/ .van-tabs__wrap {
    height: 40px;
  }

  /deep/ .van-empty__description {
    margin-top: 16px;
    padding: 0 60px;
    color: #969799;
    font-size: 16px;
    line-height: 20px;
  }

  /deep/ .van-tabs__nav--card {
    box-sizing: border-box;
    height: 40px;
    margin: 0;
    //  border: 1px solid #ee0a24;
    border-radius: 2px;
  }

  .title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
  }

  .img-list {
    width: 98%;
    display: flex;
    flex-wrap: wrap;
  }

  .div-item {
    padding: 8px 5px 5px;
  }

  .content-cell {
    margin-bottom: 100px;
  }

  .check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
  }

  .content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .div-flex {
    display: flex;
    justify-content: space-between;
  }

  .swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
  }

  .swiper-btn-update2 {
    width: 56px;
    height: 100%;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete2 {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
  }

  .div-pack {
    color: #0000ff;
  }

  .div-tips {
    color: #d33017;
  }
</style>
