<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择车牌号</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>

      <!-- <van-search v-model="carNumber" shape="round" background="#007aff" placeholder="车牌号" @search="getVehicleList"
        left-icon="" :clearable="false">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
        </template>
      </van-search> -->
    </van-sticky>
    <van-field v-model="testCar" center label="车牌号" @keyup.enter.native="getVehicleList" class="all-font-size"
      placeholder="请输入车牌号">
      <template #button>
        <van-button size="small" type="info" @click="noCarGo" class="all-font-size">确认</van-button>
      </template>
    </van-field>
    <van-field v-model="remark" rows="1" autosize class="all-font-size" label="备注" type="textarea" maxlength="100"
      placeholder="请输入备注信息" show-word-limit />
    <van-cell title="手选车牌" @click="showChooseCar = true" class="all-font-size">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>
    <van-cell title="发货方向" v-if="showShippingDirection" @click="showPopup = true" :value="shippingDirection" class="all-font-size">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>

   <!-- <van-field class="all-font-size" label="发货方向" v-model="searchValue" placeholder="请输入搜索关键词" @input="onInput">
      <template #right-icon>
        <span class="iconfont icon-sousuo" style="color: #9f9f9f"></span>
      </template>
    </van-field> -->
    <van-popup v-model="showPopup" position="bottom" :style="{ height: '50%' }">
     <!-- <van-search v-model="searchValue" :show="showSearch" placeholder="请输入搜索关键词" @search="onSearch"
        @cancel="onCancel" /> -->
        <van-search v-model="searchValue" shape="round" background="#007aff" placeholder="请输入搜索发货方向" @search="getShippingDirectionList"
          left-icon="" :clearable="false">
          <template #right-icon>
            <div class="iconfont icon-sousuo" @click="getShippingDirectionList" style="color: #999999"></div>
          </template>
        </van-search>
      <van-list>
        <van-cell v-for="item in filteredList" :key="item" :title="item" @click="checkItem(item)" />
      </van-list>
    </van-popup>

    <van-popup v-model="showChooseCar" position="bottom" :style="{ height: '70%' }">
      <chooseCarNumber @chooseCarGo="chooseCarGo"></chooseCarNumber>
    </van-popup>
    <div class="load-content">
      <div v-if="vehicleList && vehicleList.length > 0">
        <van-cell-group>
          <van-cell v-for="(item, index) in vehicleList" :key="index" clickable
            @click="isChecked(index, item.vehicleNo)">
            <template #title>
              <div class="load-number">{{ item.vehicleNo }}</div>
            </template>
            <template #right-icon>
              <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div v-else>
        <div class="search-content">
          <div class="search-history">
            <div class="all-font-size">
              历史记录
            </div>
            <div @click="deleteItem">
              <span class="iconfont icon-qingchu"></span>
            </div>
          </div>
          <div class="list-content">
            <div class="content-item" v-for="(item,index) in searchHistoryList" :key="index" @click="onClickItem(item)">
              {{item}}
            </div>
          </div>
        </div>
        <!--   <van-empty description="暂未查到车牌号列表" /> -->
      </div>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import chooseCarNumber from "@/components/carNumber.vue";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        searchValue: '',
        shippingDirection: '',
        showShippingDirection: false,
        showPopup: false,
        showSearch: true,
        shippingDirectionList: [],
        showChooseCar: false,
        vehicleList: [],
        remark: "",
        searchHistoryList: [],
        currentIndex: -1,
        value: "",
        testCar: "",    
        check: false,
        carNumber: "",
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    computed: {
      filteredList() {
        return this.shippingDirectionList.filter((item) =>
          item.toLowerCase().includes(this.searchValue.toLowerCase())
        );
      },
    },
    created() {

      let list = localStorage.getItem("vehicleOutHistoryList");
      if (list != null) {
        this.searchHistoryList = JSON.parse(list);
      } else {
        this.searchHistoryList = [];
      }
      sessionStorage.clear();
      this.searchShippingDirectionDisplay();
      // this.getVehicleList();
    },
    components: {
      chooseCarNumber,
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        // this.$router.goBack();
        this.$router.replace('/');
      },
      onInput() {
        this.showPopup = true;
      },
      onSearch() {
        // 处理搜索逻辑
      },
      onCancel() {
        this.showPopup = false;
      },
      checkItem(val){
        this.shippingDirection = val;
        this.searchValue = val;
        this.showPopup = false;
      },
      chooseCarGo(val) {
        this.showChooseCar = false;
        if (this.isVehicleNumber(val)) {
          if (!this.searchHistoryList.includes(val)) {
            this.searchHistoryList.unshift(val);
            localStorage.setItem("vehicleOutHistoryList", JSON.stringify(this.searchHistoryList))
          } else {
            //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
            let i = this.searchHistoryList.indexOf(val);
            this.searchHistoryList.splice(i, 1);
            this.searchHistoryList.unshift(val);
          }
          this.$router.togo({
            name: "lodingBill",
            params: {
              carNumber: val,
              remark: this.remark,
              shippingDirection:this.shippingDirection
            },
          });
        } else {
          this.$toast("请输入正确车牌号")
        }
      },
      noCarGo() {
        if (this.isVehicleNumber(this.testCar)) {
          if (!this.searchHistoryList.includes(this.testCar)) {
            this.searchHistoryList.unshift(this.testCar);
            localStorage.setItem("vehicleOutHistoryList", JSON.stringify(this.searchHistoryList))
          } else {
            //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
            let i = this.searchHistoryList.indexOf(this.testCar);
            this.searchHistoryList.splice(i, 1);
            this.searchHistoryList.unshift(this.testCar);
          }
          this.$router.togo({
            name: "lodingBill",
            params: {
              carNumber: this.testCar,
              remark: this.remark,
              shippingDirection:this.shippingDirection
            },
          });
        } else {
          this.$toast("请输入正确车牌号")
        }
      },
      //清空搜索历史
      deleteItem() {
        localStorage.removeItem("vehicleOutHistoryList")
        this.searchHistoryList = [];
      },
      onClickItem(item) {
        this.testCar = item;
      },
      isVehicleNumber(vehicleNumber) {
        var xxreg =
          /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/; // 2021年新能源车牌不止有DF
        var creg =
          /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
        if (vehicleNumber.length == 7) {
          return creg.test(vehicleNumber);
        } else if (vehicleNumber.length == 8) {
          return xxreg.test(vehicleNumber);
        } else {
          return false;
        }
      },
      isChecked(index, item) {
        if (!this.searchHistoryList.includes(item)) {
          this.searchHistoryList.unshift(item);
          localStorage.setItem("vehicleOutHistoryList", JSON.stringify(this.searchHistoryList))
        } else {
          //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
          let i = this.searchHistoryList.indexOf(item);
          this.searchHistoryList.splice(i, 1);
          this.searchHistoryList.unshift(item);
        }
        this.$router.togo({
          name: "lodingBill",
          params: {
            carNumber: item,
            remark: this.remark,
            shippingDirection:this.shippingDirection
          },
        });
      },
      //查询是否显示发货方向
      async searchShippingDirectionDisplay() {
        const params = {
          serviceId: "S_UC_PR_230660",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result == 1) {
                  this.showShippingDirection = true;
                  this.getShippingDirectionList();
               }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //显示发货方向内容
      async getShippingDirectionList() {
        const params = {
          serviceId: "S_UC_PR_230661",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          shippingDirection: this.searchValue,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result.length > 0) {
                  this.shippingDirectionList = dataObj.result;
                } else {
                  this.$toast("未查询到相关列表！");
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询车牌号列表
      async getVehicleList() {
        const params = {
          serviceId: "S_UC_PR_200101",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          vehicleNo: this.testCar,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result.length > 0) {
                  this.vehicleList = dataObj.result;
                } else {
                  this.$toast("未查询到车牌号列表！");
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  .load-content {
    padding-bottom: 120px;
  }

  .activeColor {
    color: #007aff;
  }

  .search-content {
    padding: 15px;


    .search-history {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #DCDCDC;
    }

    .list-content {
      display: flex;
      flex-wrap: wrap;
      padding-top: 10px;

      .content-item {
        background-color: #DCDCDC;
        border-radius: 20px;
        padding: 5px 10px 5px 10px;
        margin-left: 5px;
        margin-top: 5px;
        font-size: 15px;
      }
    }
  }

  .load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
  }

  .load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
  }
</style>
