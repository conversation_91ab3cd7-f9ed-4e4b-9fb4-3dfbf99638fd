<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">盘点扫描</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuoji<PERSON>ou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content">
      <van-field v-model="stockCheckNum" label="盘库单" class="all-font-size" readonly />
      <van-field name="radio" label="上传图片" class="all-font-size">
        <template #input>
          <van-radio-group v-model="radio" direction="horizontal" @change="handleRadioChange">
            <van-radio name="00"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              否
            </van-radio>
            <van-radio name="0"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              是
            </van-radio>
            <van-radio name="1"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              无实物
            </van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field label="库位" @keyup.enter.native="searchLocationName" :rules="[{ required: true, message: '请输入或扫描库位' }]"
        class="all-font-size">
        <template #input>
          <input type="search" class="new-field" :disabled="isItDisable" placeholder="请输入或扫描库位" v-model="locationId" />
        </template>
        <template #button>
          <van-button size="small" :type="isItDisable ? dangerType : infoType" @click="lockIt">
            {{ isItDisable ? '已锁定':'未锁定'}}
          </van-button>
        </template>
      </van-field>
      <van-field label="库位名称" class="all-font-size" placeholder="库位名称" v-model="locationName" readonly />
      <van-field v-model="packId" class="all-font-size" label="捆包" ref="labelsecond" clearable placeholder="请输入或扫描捆包号"
        :rules="[{ required: true, message: '请输入或扫描捆包号' }]" @keyup.enter.native="getBaleByPackId" />
        <van-field name="radio1" label="无实物" class="all-font-size" v-show="radio==='1'">
          <template #input>
            <van-radio-group v-model="radio1" direction="horizontal">
              <van-radio name="1"><template #icon="props">
                  <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                </template>
                未入库
              </van-radio>
              <van-radio name="2"><template #icon="props">
                  <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                </template>
                已出库
              </van-radio>

            </van-radio-group>
          </template>
        </van-field>

      <van-field name="uploader" label="图片上传" class="all-font-size" v-if="radio==='0'">
        <template #input>
          <div class="photo-container">
            <van-button size="small" type="primary" color="#007aff" @click="takePhoto" :loading="takingPhoto">
              {{ takingPhoto ? '拍照中...' : '拍照' }}
            </van-button>
            <div v-if="uploaderList.length > 0" class="image-preview">
              <img :src="uploaderList[0].content" alt="预览图" />
            </div>
          </div>
        </template>
      </van-field>
      <van-field name="uploader" label="图片上传" class="all-font-size" v-if="radio==='1'">
        <template #input>
          <van-uploader v-model="uploaderList" capture="camera" :upload-icon="uploadIcon" @oversize="onOversize" multiple :max-count="1"
            accept="image/*" />
        </template>
      </van-field>
      <div class="detail_text" style="padding-bottom: 10px">
        <div class="fourline-blue"></div>
        <div class="baletext2" style="margin-left: 0; margin-top: 14px">
          盘库单总数 :
          <span class="span-count">{{ totalCount }}</span>
        </div>
        <div class="baletext2" style="margin-left: 0; margin-top: 14px">
          未扫捆包总数 :
          <span class="span-count">{{ sweepCount }}</span>
        </div>
      </div>
      <van-tabs v-model="active" color="#007aff" style="width: 100%" line-width="60px" offset-top="44px"
        title-active-color="#007aff" sticky>
        <van-tab title="已匹配" :title-style="fontSize">
          <div v-if="stockCheckList && stockCheckList.length > 0">
            <div class="">
              <div class="detail_textarea">
                <div class="detail_text" style="padding-bottom: 10px">
                  <div class="fourline-blue"></div>
                  <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                    已扫捆包合计 :
                    <span class="span-count">{{ stockCheckList.length }}</span>
                  </div>
                </div>

              </div>
              <div class="">
                <van-swipe-cell v-for="(item, index) in stockCheckList" :key="index">
                  <div style="text-align: left">
                    <div class="detail_textarea">
                      <div class="detail_text">
                        <div class="fourtext3">{{ item.packId }}</div>
                      </div>
                      <div class="border_top">
                        <div class="">
                          <div class="check-spec" style="color:#007aff ;">{{ item.labelId }}</div>
                        </div>
                        <div>
                          <div v-if="item.locationId">
                            <span class="check-spec">库位：</span>
                            <span class="check-val">{{ item.locationId }}/{{item.locationName}}</span>
                          </div>
                          <div>
                            <span class="check-spec">规：</span>
                            <span class="check-val">{{ item.specDesc }}</span>
                          </div>
                          <div>
                            <span class="check-spec">重/件：</span>
                            <span class="check-val">{{ item.netWeight }}/{{item.pieceNum}}</span>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                  <template #right>
                    <!--   <button class="swiper-btn-update" @click="updateListItem(item,index)">
                      <span class="swiper-text">修改</span>
                    </button> -->
                    <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
                      <span class="swiper-text">删除</span>
                    </button>
                  </template>
                </van-swipe-cell>
              </div>
            </div>
            <div>

            </div>
            <div class="mui-input-row3" v-show="isShowBottom">
              <button id="block_button" type="button" class="mui-btn3" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
              <button id="block_button" type="button" class="mui-btn3" @click="uploadOn" v-preventReClick="3000">
                上&nbsp; &nbsp; &nbsp;&nbsp; 传
              </button>
            </div>
          </div>
          <div v-else>
            <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
              <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
            </div>
            <van-empty description="暂无捆包清单，请添加扫描" />
          </div>
        </van-tab>

        <van-tab title="未上传" :title-style="fontSize">
          <div v-if="stockPackList && stockPackList.length > 0">
            <div class="">
              <div class="content-cell">
                <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getCheckPackList">
                  <van-swipe-cell v-for="(item, index) in stockPackList" :key="index">
                    <div style="text-align: left">
                      <div class="detail_textarea">
                        <div class="detail_text">
                          <div class="fourtext3">{{ item.packId }}</div>
                        </div>
                        <div class="border_top">
                          <!-- <div>
                           <span class="check-spec">库位：</span>
                           <span class="check-val">{{ item.location }}11111</span>
                         </div> -->
                          <div class="content-spec">
                            <div>
                              <span class="check-spec">库位</span>
                              <span class="check-val">{{ item.locationId }}/{{item.locationName}}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <template #right>
                      <!-- <button class="swiper-btn-update2" @click="updateListItem">
                       <span class="swiper-text">修改</span>
                     </button> -->
                      <button class="swiper-btn-delete" @click="deleteListItem2(index)">
                        <span class="swiper-text">删除</span>
                      </button>
                    </template>
                  </van-swipe-cell>
                </van-list>
              </div>
            </div>
            <div v-if="stockCheckList && stockCheckList.length > 0">
            <div class="mui-input-row3" v-show="isShowBottom">
              <button id="block_button" type="button" class="mui-btn3" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
              <button id="block_button"  type="button" class="mui-btn3" @click="uploadOn" v-preventReClick="3000">
                上&nbsp; &nbsp; &nbsp;&nbsp; 传
              </button>
            </div>

            </div>
            <div  v-else>
              <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" >
                <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                  确&nbsp; &nbsp; &nbsp;&nbsp; 认
                </button>
              </div>
            </div>

          </div>
          <div v-else>
            <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
              <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
            </div>
            <van-empty description="暂无捆包清单" />
          </div>
        </van-tab>
      </van-tabs>


    </div>
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
    <!--  <div class="mui-input-row" v-show="isShowBottom" @click="uploadOn">
      <button id="block_button" type="button" class="mui-btn">
        上&nbsp; &nbsp; &nbsp;&nbsp; 传
      </button>
    </div> -->
  </div>
</template>

<script>
  import HmPopup from "@/components/HmPopup.vue";
  import {
    Dialog
  } from "vant";
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty
  } from '@/utils/tools';
  export default {
    data() {
      return {
        stockCheckPositio:"",
        locationInfo: null,
        locationError: null,
        loading: false,
        baiduAK: 'aa3wdMuDXIBlTG4OweRXX13qAfyKsgvp', // 替换为您的百度地图AK
        uploaderList:[],
        docList:[],
        loading: false,
        finished: false,
        page: 1, // 当前页码
        pageSize: 10, // 每页数量
        hasMore: true,
        show: "",
        active: 0,
        fontSize: " font-size: 16px;",
        totalCount: 0,
        sweepCount: 0,
        testList: [],
        noScanPackList: [],
        baleList: {},
        chooseList: [],
        packList: [],
        stockCheckList: [],
        stockPackList: [],
        storeNumber: '',
        stockCheckNum: '',
        packId: '',
        matInnerId: '',
        locationId: '',
        locationName: '',
        infoType: "info",
        dangerType: "danger",
        radio: "00",
        radio1:"1",
        isItDisable: false,
        isInitialized: false,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        uploadIcon: require('@/assets/imgs/upload-icon.png'),
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        takingPhoto: false, // 拍照状态
      };
    },
    components: {
      HmPopup
    },
    created() {
      this.stockCheckNum = this.$route.query.stockCheckNum;
      this.getStorageCache();
      //this.getStorageCache2();
      this.getCheckList();
      this.getCheckPackList();
      // this.loadBaiduMapScript();
       // 监听plusready事件
       document.addEventListener('plusready', this.onPlusReady, false);
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };

      if (window.history && window.history.pushState) {
        // 往历史记录里面添加一条新的当前页面的url
        history.pushState(null, null, document.URL);
        // 给 popstate 绑定一个方法 监听页面刷新
        window.addEventListener("popstate", this.backChange, false); //false阻止默认事件
      }
    },
    watch: {
      locationId(newVal, oldVal) {
        if (this.isInitialized) {
          // 处理数据变化
          this.locationName = "";
        } else {
          this.isInitialized = true;
        }
      },
      // locationId: {
      //   handler: function() {
      //     this.locationName = "";
      //   }
      // }
    },
    methods: {
      onClickLeft() {
        if (this.stockCheckList.length > 0 ) {
          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => this.$router.push('/checkList'))
            .catch(() => {});
          return;
        }
        this.$router.goBack();

      },
      backChange() {
        // 手机自带返回
        if (this.stockCheckList.length > 0) {

          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => {
              this.$router.goBack();
            })
            .catch(() => {
              if (window.history && window.history.pushState) {
                // 手机点击了物理返回 然后又选择了取消 这时需要在添加一条记录
                history.pushState(null, null, document.URL);
              }
            });
          return;
        }
        this.$router.goBack();

      },
      // 加载百度地图脚本
      loadBaiduMapScript() {
        if (window.BMap) {
          this.initBaiduLocation();
          return;
        }

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = `https://api.map.baidu.com/api?v=3.0&ak=${this.baiduAK}&callback=onBMapCallback`;
        document.head.appendChild(script);

        // 全局回调函数
        window.onBMapCallback = () => {
          console.log('百度地图脚本加载完成');
          // 初始化定位
          this.initBaiduLocation();
        };
      },

      // 重试定位
      retryLocation() {
        this.locationError = null;
        this.initBaiduLocation();
      },

     // 初始化百度定位
     initBaiduLocation() {
       if (!window.BMap) {
         this.loadBaiduMapScript();
         return;
       }

       this.loading = true;

       const geolocation = new BMap.Geolocation();
       geolocation.enableSDKLocation();
       geolocation.getCurrentPosition(
         (result) => {
           this.loading = false;
           if (result && result.point) {
             // 基本位置信息
             this.locationInfo = {
               latitude: result.point.lat,
               longitude: result.point.lng,
               address: result.address
             };

             // 获取详细地址组件
             this.getAddressComponents(result.point.lat, result.point.lng);

             console.log('百度定位成功:', this.locationInfo);
             this.$toast.success('定位成功');
           } else {
               console.log('获取位置失败:');
              this.$toast('获取位置失败');
             this.locationError = '获取位置失败';
           }
         },
         (error) => {
           this.loading = false;
           console.error('百度定位失败:', error);
           this.locationError = '定位失败，请检查定位权限和网络连接';
         }
       );
     },

     // 获取地址组件
     getAddressComponents(lat, lng) {
       if (!window.BMap) {
         return;
       }

       const point = new BMap.Point(lng, lat);
       const geoc = new BMap.Geocoder();

       geoc.getLocation(point, (rs) => {
         if (rs && rs.addressComponents) {
           const components = rs.addressComponents;

           // 存储地址组件
           this.locationInfo.addressComponents = {
             province: components.province || '',
             city: components.city || '',
             district: components.district || '',
             street: components.street || '',
             streetNumber: components.streetNumber || ''
           };

           // 格式化地址
           this.locationInfo.formattedAddress = this.formatAddress(this.locationInfo.addressComponents);

           console.log('地址组件:', this.locationInfo.addressComponents);
           console.log('格式化地址:', this.locationInfo.formattedAddress);
           this.stockCheckPositio = this.locationInfo.formattedAddress;
         }
       });
     },

     // 格式化地址
     formatAddress(components) {
       if (!components) return '';

       const { province, city, district, street, streetNumber } = components;

       // 拼接地址，过滤掉空值
       return [province, city, district, street, streetNumber]
         .filter(item => item && item.trim() !== '')
         .join('');
     },

      // 获取位置
      getLocation() {
        this.loading = true;
        this.locationError = null;
        this.locationInfo = null;

        this.initBaiduLocation();
      },
      onOversize(file) {
        console.log(file);
        this.$toast("文件大小不能超过5MB");
      },
      lockIt() {
        this.isItDisable = !this.isItDisable;
      },
      getCheckInfo(val) {
        console.log("接收到了传过来的值", val);
        this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.packId = this.baleList.packId;
        this.locationName = this.baleList.locationName;
        this.locationId = this.baleList.locationId;
        this.matInnerId = this.baleList.matInnerId;
      },
      showtest() {
        console.log("点击了关闭弹窗");
        this.show = !this.show;
      },

      //查询库位名称
      async searchLocationName() {
        const params = {
          serviceId: "S_UC_PR_230623",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          type: "inventory",
          locationId: this.locationId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.locationId = dataObj.result.locationId;
                this.locationName = dataObj.result.locationName;
                this.$nextTick(() => {
                  this.$refs.labelsecond.focus();
                });
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            } else {
              this.$toast(res.data.__sys__.msg);
              this.locationId = "";
              this.locationName = "";
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取盘库列表
      async getCheckList() {
        const params = {
          serviceId: "S_UC_PR_230633",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.stockCheckNum,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data.__sys__.status != -1) {
              this.sweepCount = res.data.sweepCount;
              this.totalCount = res.data.totalCount;
              // this.noScanPackList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }

          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取未扫描捆包列表
      async getCheckPackList() {
        const params = {
          serviceId: "S_UC_PR_230664",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.stockCheckNum,
          pageIndex: this.page,
          pageSize: this.pageSize,
        };
       const data = await baseApi.baseService(params);
       if (!data || !data.data) {
         this.$toast("网络异常, 请联系管理员!");
         return;
       }
       data.data.result.forEach(d => {
         this.stockPackList.push(d);
       });

      this.page++;
       this.loading = false;
       this.finished = false;
       if (data.data.result.length == 0) {
         this.finished = true;
       }
        this.$toast(data.data.__sys__.msg);
        },
        //获取未扫描捆包列表
        async getReloadCheckPackList() {
          const params = {
            serviceId: "S_UC_PR_230664",
            userId: localStorage.getItem("userId"),
            accessToken: localStorage.getItem("accessToken"),
            segNo: localStorage.getItem("segNo"),
            warehouseName: localStorage.getItem("warehouseName"),
            warehouseCode: localStorage.getItem("warehouseCode"),
            stockCheckNum: this.stockCheckNum,
            pageIndex: 1,
            pageSize: 10,
          };
          const data = await baseApi.baseService(params);
          if (!data || !data.data) {
            this.isLoading = false;
            this.$toast("网络异常, 请联系管理员!");
            return;
          }
          this.stockPackList = data.data.result;
          this.page++;
          this.isLoading = false;
          },
          async uploadOn() {
            let noPhysicalFlag;
            if(this.radio == 1){
              noPhysicalFlag = 1;
            }else{
              noPhysicalFlag = 0;
            }
              const params = {
                serviceId: "S_UC_PR_230615",
                userId: localStorage.getItem("userId"),
                accessToken: localStorage.getItem("accessToken"),
                segNo: localStorage.getItem("segNo"),
                segCname: localStorage.getItem("segName"),
                warehouseCode: localStorage.getItem("warehouseCode"),
                warehouseName: localStorage.getItem("warehouseName"),
                factoryArea: localStorage.getItem("factoryArea"),
                factoryAreaName: localStorage.getItem("factoryName"),
                // iplat_transactionType: 3,
                stockCheckNum: this.stockCheckNum,
                //noPhysicalFlag:noPhysicalFlag,//0 ：有实物     1： 无实物 "
                //noPhysicalType:this.radio1,//1 ：未入库     2： 已出库"
                inventoryType:"2",
                stockCheckPosition:this.stockCheckPositio,//
                list: this.stockCheckList,
              };
              await baseApi
                .baseService(params)
                .then((res) => {
                  if (res.data) {
                    let dataObj = null;
                    try {
                      dataObj = res.data;
                    } catch (e) {
                      dataObj = {
                        __sys__: {
                          msg: "调用失败",
                          status: -1,
                        },
                      };
                    }
                    if (dataObj && dataObj.__sys__.status != -1) {
                      // this.$toast(res.data.__sys__.msg);
                      this.packId = "";
                      this.stockCheckList = [];
                      this.noScanPackList = [];
                      let _this = this
                      Dialog.alert({
                        title: '提示',
                        message: res.data.__sys__.msg,
                        beforeClose: this.beforeClose,
                      });
                      this.page = 1;
                      setTimeout(function() {
                        _this.deleteStorageCache();
                        _this.deleteStorageCache2();
                         _this.getReloadCheckPackList();
                        // 需要执行的代码
                      }, 1000);
                    } else {
                      this.$toast(res.data.__sys__.msg);
                    }
                  }
                })
                .catch((err) => {
                  console.log(err);
                });
            },
            beforeClose(action, done) {
              let _this = this;
              if (action === 'confirm') {
                setTimeout(function() {
                  _this.getCheckList();
                  // 需要执行的代码
                }, 3000);
                setTimeout(done, 3000);
              } else {
                done();
              }
            },
            onConfrim() {
              console.log("======this.uploaderList",this.uploaderList);
              if(this.isItDisable){
                 if (Object.keys(this.baleList).length != 0 ) {
                  if (isEmpty(this.packId)) {
                    this.$toast("捆包号不能为空!");
                  } else {
                      if (this.uploaderList.length > 0) {
                        let base64Img = this.uploaderList.map(f => f.content);
                        this.uploadImage(base64Img);
                      } else {
                        if(this.radio1 == "2"){
                          if(this.uploaderList.length > 0){
                             this.handleVal(this.packId);
                          }else{
                             this.$toast("未添加出库图片不允许上传!");
                          }
                        }else{
                          this.handleVal(this.packId);
                        }

                      }
                  }
                }else{
                   this.$toast("未查到对应捆包列表!");
                }

              }else{
                 if (Object.keys(this.baleList).length != 0 ) {
                if (isEmpty(this.locationName)) {
                  this.$toast("库位或库位名称不能为空!");
                } else {
                  if (isEmpty(this.packId)) {
                    this.$toast("捆包号不能为空!");
                  } else {
                      if (this.uploaderList.length > 0) {
                        let base64Img = this.uploaderList.map(f => f.content);
                        this.uploadImage(base64Img);
                      } else {
                        this.handleVal(this.packId);
                      }

                  }
                }
              }else{
                 this.$toast("未查到对应捆包列表!");
              }
              }
            },
            handleVal(val) {
              if (isEmpty(val)) {
                return;
              }
              let status = this.stockCheckList.some((item) => {
                return item.packId === val || item.labelId === val;
              });
              if (status) {
                this.$toast("此捆包号已扫描，请重新扫描其他捆包号");
                this.packId = "";
                this.baleList = {};
              } else {
                let noPhysicalFlag;
                if(this.radio == 1){
                  noPhysicalFlag = 1;
                }else{
                  noPhysicalFlag = 0;
                }
                const scanVal = [{
                  packId: Object.keys(this.baleList).length != 0 ? this.baleList.packId : this.packId,
                  matInnerId: Object.keys(this.baleList).length != 0 ? this.baleList.matInnerId : "",
                  labelId: Object.keys(this.baleList).length != 0 ? this.baleList.labelId : "",
                  locationId: this.isItDisable ? this.baleList.locationId : this.locationId,
                  locationName: this.isItDisable ? this.baleList.locationName : this.locationName,
                  specDesc: Object.keys(this.baleList).length != 0 ? this.baleList.specDesc : "",
                  netWeight: Object.keys(this.baleList).length != 0 ? this.baleList.netWeight : "",
                  pieceNum: Object.keys(this.baleList).length != 0 ? this.baleList.pieceNum : "",
                  tradeCode: Object.keys(this.baleList).length != 0 ? this.baleList.tradeCode : "",
                  settleUserNum: Object.keys(this.baleList).length != 0 ? this.baleList.settleUserNum : "",
                  settleUserName: Object.keys(this.baleList).length != 0 ? this.baleList.settleUserName : "",
                  noPhysicalFlag:noPhysicalFlag,//0 ：有实物     1： 无实物 "
                  noPhysicalType:this.radio1,//1 ：未入库     2： 已出库"
                }];
                if(this.docList.length > 0){
                  scanVal[0].uploadFileName = this.docList[0].docId;
                  scanVal[0].uploadFilePath = this.docList[0].docUrl;
                }
                this.stockCheckList = [...scanVal, ...this.stockCheckList];
                console.log(this.stockCheckList);
                this.packId = "";
                this.baleList = {};
                this.docList = [];
              }
            },
            deleteListItem(index, item) {
              //删除数组中值
              this.$delete(this.stockCheckList, index);
            },

            deleteListItem2(index) {
              this.$delete(this.noScanPackList, index);
            },
            async addStorageCache() {
                const params = {
                  serviceId: "S_UC_PR_270101",
                  userId: localStorage.getItem("userId"),
                  accessToken: localStorage.getItem("accessToken"),
                  segNo: localStorage.getItem("segNo"),
                  factoryArea: localStorage.getItem("factoryArea"),
                  warehouseCode: localStorage.getItem("warehouseCode"),
                  type: "02",
                  billNo: this.stockCheckNum,
                  rowList: this.stockCheckList,
                };
                await baseApi.baseService(params)
                  .then((res) => {
                    if (res.data.__sys__.status == '-1') {
                      this.$toast(res.data.__sys__.msg);
                    }
                  })
                  .catch((err) => {
                    console.log(err);
                  });
              },
              async getStorageCache() {
                  const params = {
                    serviceId: "S_UC_PR_270102",
                    userId: localStorage.getItem("userId"),
                    accessToken: localStorage.getItem("accessToken"),
                    segNo: localStorage.getItem("segNo"),
                    factoryArea: localStorage.getItem("factoryArea"),
                    warehouseCode: localStorage.getItem("warehouseCode"),
                    type: "02",
                    billNo: this.stockCheckNum,
                  };
                  let res = await baseApi.baseService(params);

                  if (!res || !res.data) {
                    this.$toast("调用失败");
                    return;
                  }

                  if (res.data.__sys__.status == '-1') {
                    this.$toast(res.data.__sys__.msg);
                    this.stockCheckList = [];
                    return;
                  }

                  if (res.data.result.length == 0) { // 本次没有数据
                    this.stockCheckList = [];
                  } else { // 本次有数据
                    this.stockCheckList = res.data.result;
                  }
                },
                async uploadImage(val) {
                  const params = {
                    serviceId: "S_UC_PR_230808",
                    userId: localStorage.getItem("userId"),
                    accessToken: localStorage.getItem("accessToken"),
                    segNo: localStorage.getItem("segNo"),
                    pictureList: val,
                  };
                  await baseApi.baseService(params)
                    .then((res) => {
                      if (res.data) {
                        let dataObj = null;
                        try {
                          dataObj = res.data;
                        } catch (e) {
                          dataObj = {
                            __sys__: {
                              msg: "调用失败",
                              status: -1,
                            },
                          };
                        }
                        this.$toast(res.data.__sys__.msg);
                        this.docList = res.data.detail;
                        this.uploaderList = [];
                        this.handleVal(this.packId);
                      } else {
                        this.$toast(res.data.__sys__.msg);
                      }
                    })
                    .catch((err) => {
                      console.log(err);
                    });
                },
                async deleteStorageCache() {
                    const params = {
                      serviceId: "S_UC_PR_270103",
                      userId: localStorage.getItem("userId"),
                      accessToken: localStorage.getItem("accessToken"),
                      segNo: localStorage.getItem("segNo"),
                      factoryArea: localStorage.getItem("factoryArea"),
                      warehouseCode: localStorage.getItem("warehouseCode"),
                      type: "02",
                      billNo: this.stockCheckNum,
                    };
                    await baseApi.baseService(params)
                      .then((res) => {
                        if (res.data.__sys__.status == '-1') {
                          this.$toast(res.data.__sys__.msg);
                        }
                      })
                      .catch((err) => {
                        console.log(err);
                      });
                  },
                  async addStorageCache2() {
                      const params = {
                        serviceId: "S_UC_PR_270101",
                        userId: localStorage.getItem("userId"),
                        accessToken: localStorage.getItem("accessToken"),
                        segNo: localStorage.getItem("segNo"),
                        factoryArea: localStorage.getItem("factoryArea"),
                        warehouseCode: localStorage.getItem("warehouseCode"),
                        type: "03",
                        billNo: this.stockCheckNum,
                        rowList: this.noScanPackList,
                      };
                      await baseApi.baseService(params)
                        .then((res) => {
                          if (res.data.__sys__.status == '-1') {
                            this.$toast(res.data.__sys__.msg);
                          }
                        })
                        .catch((err) => {
                          console.log(err);
                        });
                    },
                    async getStorageCache2() {
                        const params = {
                          serviceId: "S_UC_PR_270102",
                          userId: localStorage.getItem("userId"),
                          accessToken: localStorage.getItem("accessToken"),
                          segNo: localStorage.getItem("segNo"),
                          factoryArea: localStorage.getItem("factoryArea"),
                          warehouseCode: localStorage.getItem("warehouseCode"),
                          type: "03",
                          billNo: this.stockCheckNum,
                        };
                        let res = await baseApi.baseService(params);

                        if (!res || !res.data) {
                          this.$toast("调用失败");
                          return;
                        }

                        if (res.data.__sys__.status == '-1') {
                          this.$toast(res.data.__sys__.msg);
                          this.noScanPackList = [];
                          return;
                        }

                        if (res.data.result.length == 0) { // 本次没有数据
                          this.noScanPackList = [];
                        } else { // 本次有数据
                          this.noScanPackList = res.data.result;
                        }
                      },
                      async deleteStorageCache2() {
                          const params = {
                            serviceId: "S_UC_PR_270103",
                            userId: localStorage.getItem("userId"),
                            accessToken: localStorage.getItem("accessToken"),
                            segNo: localStorage.getItem("segNo"),
                            factoryArea: localStorage.getItem("factoryArea"),
                            warehouseCode: localStorage.getItem("warehouseCode"),
                            type: "03",
                            billNo: this.stockCheckNum,
                          };
                          await baseApi.baseService(params)
                            .then((res) => {
                              if (res.data.__sys__.status == '-1') {
                                this.$toast(res.data.__sys__.msg);
                              }
                            })
                            .catch((err) => {
                              console.log(err);
                            });
                        },
                        async getBaleByPackId() {
                            const params = {
                              serviceId: "S_UC_PR_230634",
                              userId: localStorage.getItem("userId"),
                              accessToken: localStorage.getItem("accessToken"),
                              segNo: localStorage.getItem("segNo"),
                              segCname: localStorage.getItem("segName"),
                              warehouseCode: localStorage.getItem("warehouseCode"),
                              stockCheckNum: this.stockCheckNum,
                              packId: this.packId,
                            };
                            await baseApi
                              .baseService(params)
                              .then((res) => {
                                if (res.data) {
                                  let dataObj = null;
                                  try {
                                    dataObj = res.data;
                                  } catch (e) {
                                    dataObj = {
                                      __sys__: {
                                        msg: "调用失败",
                                        status: -1,
                                      },
                                    };
                                  }
                                  if (dataObj && res.data.__sys__.status != -1) {
                                    this.packId = dataObj.packId;
                                    this.baleList = dataObj.result;
                                    if (typeof this.baleList === 'undefined' || this.baleList === null) {
                                         this.$toast("未查询到相应捆包列表!");
                                    } else {
                                      if (this.isItDisable) {
                                        if (isEmpty(this.baleList.locationName)) {
                                          this.$toast("库位或库位名称不能为空!");
                                        } else {
                                          if(this.radio == "00"){
                                             this.handleVal(this.packId);
                                          }
                                        }
                                      } else {
                                        if (isEmpty(this.locationName)) {
                                          this.$toast("库位或库位名称不能为空!");
                                        } else {
                                         if(this.radio == "00"){
                                            this.handleVal(this.packId);
                                         }
                                        }
                                      }
                                    }
                                  } else {
                                     this.packId = dataObj.packId;
                                    this.$toast(res.data.__sys__.msg);
                                  }
                                }
                              })
                              .catch((err) => {
                                console.log(err);
                              });
                          },
                          addNoPackInList(val) {
                            let status = this.noScanPackList.some((item) => {
                              return item.packId === val;
                            });
                            if (isEmpty(this.locationName)) {
                              this.$toast("库位或库位名称不能为空!");
                              return;
                            }
                            if (status) {
                              this.$toast("此捆包号已扫描，请重新扫描其他捆包号");
                              this.packId = "";
                            } else {
                              const chekVal = [{
                                locationId: this.locationId,
                                locationName: this.locationName,
                                packId: val,
                              }];
                              this.$toast("已添加到未匹配列表!");
                              this.noScanPackList = [...chekVal, ...this.noScanPackList];
                              console.log(this.noScanPackList);
                              this.packId = "";
                            }

                          },
                          onPlusReady() {
        console.log('plus environment is ready');
      },

      // 拍照方法
      takePhoto() {
        if (!window.plus) {
          this.$toast('请在APP环境下使用此功能');
          return;
        }

        this.takingPhoto = true;
        try {
          const camera = plus.camera.getCamera();
          camera.captureImage(
            (path) => {
              // 成功回调
              console.log('拍照成功，路径：', path);
              this.convertToBase64(path);
            },
            (error) => {
              // 失败回调
              console.error('拍照失败：', error);
              this.$toast('拍照失败：' + (error.message || '未知错误'));
              this.takingPhoto = false;
            },
            {
              filename: '_doc/camera/', // 保存路径
              index: 1, // 后置摄像头
              format: 'jpg', // 图片格式
              quality: 100 // 图片质量
            }
          );
        } catch (e) {
          console.error('调用相机失败：', e);
          this.$toast('调用相机失败：' + (e.message || '未知错误'));
          this.takingPhoto = false;
        }
      },

      // 将图片转换为base64
      convertToBase64(path) {
        plus.io.resolveLocalFileSystemURL(path, (entry) => {
          entry.file((file) => {
            const reader = new plus.io.FileReader();
            reader.onloadend = (e) => {
              const base64Data = e.target.result;
              // 更新uploaderList
              this.uploaderList = [{
                content: base64Data
              }];
              this.takingPhoto = false;
              console.log('图片转换为base64成功');
            };
            reader.onerror = (e) => {
              console.error('读取文件失败：', e);
              this.$toast('读取图片失败');
              this.takingPhoto = false;
            };
            reader.readAsDataURL(file);
          });
        }, (error) => {
          console.error('解析文件路径失败：', error);
          this.$toast('解析图片失败');
          this.takingPhoto = false;
        });
      },

      // 处理radio改变
      handleRadioChange(value) {
        console.log('radio changed:', value);
        // 清空图片数据
        this.uploaderList = [];
        this.takingPhoto = false;
      },
      },
      beforeDestroy() {
        window.removeEventListener("popstate", this.backChange);
        if (this.stockCheckList.length > 0) {
          this.addStorageCache();
        }
        // if (this.noScanPackList.length > 0) {
        //   this.addStorageCache2();

        // }
        // 移除plusready事件监听
        document.removeEventListener('plusready', this.onPlusReady);
      },
      // plus环境就绪

    };
</script>

<style lang="less" scoped>
  .bottom-btn {
    display: flex;
    justify-content: space-between;
    margin: 20px 16px 0 16px;
  }

  .btn {
    width: 146px;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
  }

  .span-btn {
    width: 32px;
    height: 23px;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
  }

  .inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }
  .activeColor {
    color: #007aff;
  }
  /deep/ .van-dialog {
    position: fixed;
    top: 60%;
    left: 50%;
    width: 320px;
    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
  }

  .title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
  }

  .content-cell {
    margin-bottom: 100px;
  }

  .check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
  }

  .check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .div-flex {
    display: flex;
    justify-content: space-between;
  }

  .swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
  }

  .photo-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .image-preview {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
</style>
