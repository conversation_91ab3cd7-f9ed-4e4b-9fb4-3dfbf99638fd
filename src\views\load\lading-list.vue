<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择提货单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
       <!-- <template #right>
          <div class="nav-bar-scan all-font-size" @click="showOpen">扫描</div>
        </template> -->
      </van-nav-bar>

      <van-field label="精细化车牌号">
        <template #input>
          <input type="search" readonly placeholder="请查询车牌号" v-model="vehicleNo" />
        </template>
      <template #right-icon>
          <span class="iconfont icon-sousuo"  style="width: 10%; color: #BEBEBE" @click="searchVehicleNo"></span>
        </template>
      </van-field>
      <van-field  label="手机号"  @keyup.enter.native="searchPhoneNumber">
        <template #input>
          <input type="search"  placeholder="请输入司机手机号" v-model="phoneNumber" />
        </template>
      <template #right-icon>
          <span class="iconfont icon-sousuo"  style="width: 10%; color: #BEBEBE" @click="searchPhoneNumber"></span>
        </template>
      </van-field>
      <van-field label="车牌号" @keyup.enter.native="searchVehicleNo2">
        <template #input>
          <input type="search" placeholder="请输入车牌号" v-model="vehicleNo2" />
        </template>
        <template #right-icon>
          <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE" @click="searchVehicleNo2"></span>
        </template>
      </van-field>
      <van-field v-model="ladingBillId" ref="ladRef" label="提货单" placeholder="请扫描或输入提货单号"
        @keyup.enter.native="topSearchFunc"  />
    </van-sticky>

    <div class="load-content">
      <div v-if="LadingBillList && LadingBillList.length > 0">
        <div class="baletext2" style="margin-left: 10px; margin-top: 14px">
          已扫提单合计 :
          <span class="span-count">{{ LadingBillList.length }}</span>
        </div>
        <van-swipe-cell v-for="(item, index) in LadingBillList" :key="index">
          <van-cell>
            <template #title>
              <div class="load-number">{{ item }}</div>
             <!-- <div class="">{{ item.settleUserName }}</div> -->
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" text="删除" class="delete-bale-btn" @click="deleteItem(index)" />
          </template>
        </van-swipe-cell>
        <div class="mui-input-row" style="margin: 0" @click="onConfim">
          <button type="button" class="mui-btn">
            确&nbsp; &nbsp; &nbsp;&nbsp;定
          </button>
        </div>
      </div>
      <div v-else>
        <van-empty description="暂未扫描提单号列表" />
      </div>
    </div>

    <van-dialog v-model="show" title="扫描提单号" :beforeClose="beforeClose" show-cancel-button :width="340">
      <van-search v-model="ladingBillId" shape="round" placeholder="提单号" @search="topSearchFunc" left-icon=""
        :clearable="true">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="topSearchFunc" style="color: #999999"></div>
        </template>
      </van-search>
      <div class="search-list" v-if="searchList && searchList.length > 0">
        <van-list v-model="searchLoading" :finished="searchFinshed" finished-text="没有更多了" @load="searchOnLoad">
          <van-checkbox-group v-model="result">
            <van-cell-group>
              <van-cell v-for="(item, index) in searchList" clickable :key="item.ladingBillId" @click="toggle(item)">
                <template #title>
                  <div class="load-number">{{ item.ladingBillId }}</div>
                  <div>仓库编号: {{ item.warehouseCode }}</div>
                  <div>客户: {{ item.settleUserName }}</div>
                </template>
                <template #right-icon>
                  <van-checkbox :name="item.ladingBillId" ref="checkboxes">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-checkbox>
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </van-list>
      </div>

      <div v-else>
        <div class="search-list">
          <div class="empty-des">暂未查询到提单号列表</div>
        </div>
      </div>
    </van-dialog>
    <van-dialog v-model="showVehicle" :show-confirm-button="showBtn">
      <div>
        <van-search v-model="value" shape="round" background="#007aff" placeholder="请输入车牌号" @search="getVehicleList"
          left-icon="" :clearable="false">
          <template #right-icon>
            <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
          </template>
        </van-search>
      </div>
      <div class="search-list">
        <div v-if="vehicleList && vehicleList.length > 0">
          <van-radio-group v-model="value">
            <van-cell-group>
              <van-cell v-for="(item, index) in vehicleList" :key="index" clickable @click="onCheck(index, item)">
                <template #title>
                  <div class="seg-title">{{ item }}</div>
                </template>
                <template #right-icon>
                  <van-radio :name="item">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-radio>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>

        <div class="tips-content" v-else>
          <div class="title">提示</div>
          <div class="msg">未查询到可选择的车牌！</div>
        </div>
      </div>
      <button class="search-btn" @click="onConfrimCarNumber">确认</button>
    </van-dialog>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  let previousRouterName = "";
  let previousRemark = "";
  let previousCarNum = "";
  export default {
    data() {
      return {
        searchLoading: false,
        searchFinshed: false,
        searchRefreshing: false,
        show: false,
        showVehicle:false,
        showBtn:false,
        LadingBillList: [],
        searchList: [],
        carList: [],
        result: [],
        vehicleNo:"",
        phoneNumber:"",
        confrimList: [],
        vehicleList:[],
        ladingBillId: "",
        logisticPlanId: "",
        currentIndex: -1,
        value: "",
        confirmPwd: "",
        testCar: "",
        check: false,
        carNumber: "",
        vehicleNo2:'',
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        // isLoadingQueryCode: false,
        isShowInputCode: false,
        textCode: '',
        textCodeIndex: -1,
        resultObjList: [],
        pageNo: 1,
        pageSize: 100,
        remark: "",
      };
    },

    created() {
      this.getVehicleList();
      this.$nextTick(() => {
        this.$refs.ladRef.focus();
      });
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
        //  this.$router.replace('/selectOption');
      },
      showOpen() {
        this.show = true;
      },
      onCheck(index, item) {
        this.value = item;
        this.currentIndex = index;
        this.vehicleNo = item;
      },
      searchOnRefresh() { // 下拉刷新时调用
        this.pageNo = 1;
        this.searchFinshed = false; // 每次下拉刷新，要将searchFinshed置为false
        this.searchList = [];
        this.getLadingBillList();
      },
      searchOnLoad() { // 滚动到底部时，会触发 searchOnLoad 事件并将 searchLoading 设置成 true
        this.searchLoading = true;
        this.pageNo++;
        this.getLadingBillList();
      },
      topSearchFunc() {
        this.pageNo = 1;
        this.searchLoading = true;
        // 之前如果数据全部加载完毕(searchFinshed为true)，本次又点击了搜索，必须要重新将searchFinshed置为false
        // 否则会出现，加载完第一页的数据之后，第二页就部进行数据请求了
        this.searchFinshed = false;
        this.searchList = [];
        // this.resultObjList = [];
        //  this.result = [];
        this.getLadingBillList();
      },
      async getLadingBillList() {
        const params = {
          serviceId: "S_UC_PR_230628",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          ladingBillId: this.ladingBillId,
          pageNo: this.pageNo,
          pageSize: this.pageSize
        };
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }

        if (res.data.__sys__.status == '-1') {
          this.$toast(res.data.__sys__.msg);
          this.searchList = [];
          return;
        }

        if (res.data.result.length == 0) { // 本次没有数据
          if (this.pageNo > 1) { // 不是第一页，上拉加载
            this.searchFinshed = true; // 数据全部加载完毕
          } else { // 第一页，下拉刷新
            this.searchList = [];

            this.searchRefreshing = false;
          }
        } else { // 本次有数据
          if (res.data.result.length == 1) {
            this.searchList = res.data.result.map(r => ({
              ladingBillId: r.ladingBillId,
              logisticPlanId: '',
              ebillPassword: '',
              warehouseCode: r.warehouseCode,
              settleUserName: r.settleUserName,
              putoutPlanId: r.putoutPlanId
            }))
            let obj = this.searchList[0];
            this.carList = this.LadingBillList.concat(obj.ladingBillId);
            this.LadingBillList = this.handleArr(this.carList);
            this.packId = "";

          } else {
            this.show = true;
            this.searchList = [
              ...this.searchList,
              ...res.data.result.filter(c => !this.result.some(d => d === c.ladingBillId)).map(r => ({
                ladingBillId: r.ladingBillId,
                logisticPlanId: '',
                ebillPassword: '',
                warehouseCode: r.warehouseCode,
                settleUserName: r.settleUserName,
                putoutPlanId: r.putoutPlanId
              })),
            ];
          }
          if (res.data.result.length < this.pageSize) { // 本次拿到的数据小于每页的最大条数，则后面的页肯定也没数据了
            this.searchFinshed = true; // 数据全部加载完毕
          }
          this.searchLoading = false; // 本次数据更新完毕，searchLoading置为false
        }
      },
      onConfim() {
        this.$router.togo({
          name: "lading-reversal",
          params: {
            ladingBillIdList: this.LadingBillList,
          },
        });

      },
      handleArr(arr) {
        let brr = [];
        for (let i = 0; i < arr.length; i++) {
          if (!brr.includes(arr[i])) {
            brr.push(arr[i]);
          } else {
            this.$toast("已扫描");
          }
        }
        return brr;
      },


      deleteItem(index) {
        //删除数组中值
        this.$delete(this.LadingBillList, index);
        this.$delete(this.confrimList, index);
        this.$delete(this.result, index);
        this.$delete(this.resultObjList, index);
      },
      onConfrimCarNumber(){
        this.value = "";
        this.showVehicle = false;
         this.getLadingList();
      },

      //查询车牌号列表
      searchVehicleNo(){
        this.showVehicle = true;
      },
      async searchPhoneNumber(){
        const params = {
          serviceId: "S_UC_PR_230651",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          mobile: this.phoneNumber,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                if(dataObj.result.length == 0){
                   this.$toast("未查到相应提单信息！");
                }
                this.carList = this.LadingBillList.concat(dataObj.result);
                this.LadingBillList = this.handleArr(this.carList);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async searchVehicleNo2(){
        const params = {
          serviceId: "S_UC_PR_230656",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          vehicleNo: this.vehicleNo2,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                if(dataObj.result.length == 0){
                   this.$toast("未查到相应提单信息！");
                }
                this.carList = this.LadingBillList.concat(dataObj.result);
                this.LadingBillList = this.handleArr(this.carList);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getLadingList() {
        const params = {
          serviceId: "S_UC_PR_230650",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          vehicleNo: this.vehicleNo,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.carList = this.LadingBillList.concat(dataObj.result);
                this.LadingBillList = this.handleArr(this.carList);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },

      async getVehicleList(){
      const params = {
        serviceId: "S_UC_PR_200102",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        factoryArea: localStorage.getItem("factoryArea"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        vehicleNo: this.value,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              //console.log("---", dataObj.result);
              this.vehicleList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
      },
      // 选择提单
      async toggle(item) {
        let id = item.ladingBillId;
        if (this.result.includes(id)) {
          let index = this.result.findIndex(item => item == id);
          this.result.splice(index, 1);
        } else {
          this.result.push(id);
        }
      },
      async beforeClose(action, done) {
        if (action === "confirm") {
          // 判断验证码是否正确
          this.carList = this.LadingBillList.concat(this.result);
          this.LadingBillList = this.handleArr(this.carList);
          this.packId = "";
          done();
        } else {
          this.result = [];
          done();
        }
      },
      // 判断验证码是否正确


    },
  }
</script>

<style lang="less" scoped>
  .load-content {
    padding-bottom: 120px;
  }

  .activeColor {
    color: #007aff;
  }

  .nav-bar-scan {
    color: #fff;
  }

  .span-count {
    margin-left: 10px;
    color: #0000ff;
  }

  .info-btn {
    overflow: hidden;
    position: fixed;
    bottom: 0;
  }

  .load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
  }

  .empty-des {
    text-align: center;
    padding-top: 30px;
    color: #BEBEBE;
  }

  .load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
  }

  .search-list {
    height: 180px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
    overflow-y: auto;

    // margin-bottom: 80px;
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }
  .tips-content {
    height: 110px;
    margin: 15px;
    background-color: #fff;
    font-size: 14px;
    padding: 10px;
    border-radius: 10px;

    div {
      margin: 10px;
    }

    .title {
      text-align: center;
    }

    .msg {
      margin-top: 10px;
    }
  }
  .search-btn {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    color: #fff;
  }
  /** 遮挡罩 加载动画定位 */
  div.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    div.block {
      width: 100px;
      height: 100px;
      background-color: #fff;
      text-align: center;
      line-height: 100px;
      border-radius: 10px;
    }

  }
</style>
