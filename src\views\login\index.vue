<template>
  <div class="login-page">
    <div class="container">
      <div class="login-head">
        <div>
          <img src="@/assets/logo/loginLogo.png" alt="" class="logo-img" />
        </div>
        <div class="login-title">IMC-PDA</div>
      </div>
      <div class="wave"></div>
      <div class="wave1"></div>
      <div class="wave2"></div>
      <div class="wave3"></div>
    </div>

    <div class="login-content">
      <div class="login-cell-group">
        <!-- <div class="login-cell">
        <div class="cell-remix">
          <span class="iconfont icon-31dingwei"></span>
          <span class="iconfont icon-shuxian icon-line"></span>
          <input type="text" placeholder="区域组织" class="login-input" readonly @click="checkOrganization"/>
        </div>
      </div> -->
        <div class="login-cell">
          <div class="cell-remix">
            <span class="iconfont icon-ren-copy" style="color: #333"></span>
            <span class="iconfont icon-shuxian icon-line"></span>
            <input type="text" placeholder="用户名" class="login-input" v-model="userName" />
          </div>
        </div>
        <div class="login-cell">
          <div class="cell-remix">
            <span class="iconfont icon-lock" style="color: #333"></span>
            <span class="iconfont icon-shuxian icon-line"></span>
            <input placeholder="密码" class="login-input" :type="type" v-model="password" @keydown="addByEnterKey" />
            <span class="iconfont icon-yanjing" @click="seePassword"
              :class="active ? 'see-password' : 'notsee-password'"></span>
          </div>
        </div>
        <div class="login-btn">
          <button class="btn" @click="goLogin">
            登&nbsp; &nbsp; &nbsp;&nbsp;录
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty,
    compare
  } from '@/utils/tools';
  export default {
    data() {
      return {
        active: false,
        userName: "",
        password: "",
        curVersion: "",
        newVersion: "",
        newUrl: "",
        forceVersionNo: "",
      };
    },
    created() {
      if (window.plus) {
        // 在这里调用h5+ API
        plus.runtime.getProperty(plus.runtime.appid, function(inf) {
          console.log("当前版本号", inf.version);
          localStorage.setItem("thisVerion", inf.version);
        });
      }

      //  this.checkVersion();
      let newV = localStorage.getItem("newVersion");
      let thisV = localStorage.getItem("thisVerion");
      var that = this;
      if (!isEmpty(newV) && !isEmpty(thisV)) {
        let f = compare(newV, thisV);
        if (f == 1) {
          that.checkVersion();
        }
      } else {
        if (window.plus) {
          // 在这里调用h5+ API
          that.checkVersion();
        } else {
          // 兼容老版本的plusready事件
          document.addEventListener(
            "plusready",
            function() {
              // 在这里调用h5+ API
              that.checkVersion();
            },
            false
          );
        }
      }

    },
    mounted() {

    },
    computed: {
      type() {
        return this.active ? "text" : "password";
      },
    },
    methods: {

      async checkVersion() {
        let that = this
        const params = {
          serviceId: "S_UC_PR_090202"
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                console.log("----dataObj", dataObj);
                that.newUrl = dataObj.result.versionAddress;
                that.newVersion = dataObj.result.versionNo
                localStorage.setItem("newVerion", that.newVersion);
                that.forceVersionNo = dataObj.forceVersionNo
                plus.runtime.getProperty(plus.runtime.appid, function(inf) {
                  let flag = compare(that.newVersion, inf.version)
                  let flagForce;
                  if (!isEmpty(that.forceVersionNo)) {
                    flagForce = compare(that.forceVersionNo, inf.version)
                  }
                  console.log("falg判断", flagForce);
                  if (isEmpty(flagForce)) {
                    if (flag == 1) {
                      that.downWgt();
                    }
                  } else {
                    if (flagForce == 1) {
                      that.downForce();
                    } else {
                      if (flag == 1) {
                        that.downWgt();
                      }
                    }
                  }
                });
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      downWgt() {
        let that = this
        const wgtUrl = this.newUrl;

        plus.nativeUI.confirm(`检测到新版本(${that.newVersion})，是否更新`, function(e) {
          if (e.index == 0) {
            plus.nativeUI.showWaiting("正在下载更新中，请稍等...");
            plus.downloader.createDownload(wgtUrl, {
              filename: "_doc/update/"
            }, function(d, status) {
              if (status == 200) {
                console.log("下载更新成功：" + d.filename);
                that.installWgt(d.filename); // 安装wgt资源包
              } else {
                console.log("下载更新失败！");
                plus.nativeUI.toast("下载更新失败！");
              }
              plus.nativeUI.closeWaiting();
            }).start();
          }

        }, "检测到新版本", ["确定", "取消"]);
      },
      downForce() {
        let that = this
        const wgtUrl = this.newUrl;
        plus.nativeUI.confirm(`当前版本低于强制更新版本(${that.forceVersionNo})，请更新最新版本(${that.newVersion})`, function(e) {
          if (e.index == 0) {
            plus.nativeUI.showWaiting("正在下载更新中，请稍等...");
            plus.downloader.createDownload(wgtUrl, {
              filename: "_doc/update/"
            }, function(d, status) {
              if (status == 200) {
                console.log("下载更新成功：" + d.filename);
                that.installWgt(d.filename); // 安装wgt资源包
              } else {
                console.log("下载更新失败！");
                plus.nativeUI.toast("下载更新失败！");
              }
              plus.nativeUI.closeWaiting();
            }).start();
          }
        }, "更新提示", ["确定"]);
      },
      installWgt(path) {
        plus.runtime.install(path, {}, function() {
          plus.nativeUI.alert("更新完成！", function() {
            //  更新完成后重启应用
            plus.runtime.restart();
          });
        }, function(e) {
          plus.nativeUI.closeWaiting();
          console.log("安装更新失败！[" + e.code + "]：" + e.message);
          plus.nativeUI.toast("安装更新失败！");
        });
      },
      addByEnterKey(e) {
        //Enter键的代码就是13
        if (e.keyCode == 13) {
          this.goLogin();
        }
      },

      async goLogin() {
        const params = {
          serviceId: "S_UC_PR_200001",
          userName: this.userName.trim(),
          password: this.password.trim(),
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data && res.data.segInfoList) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                localStorage.setItem("isLogin", true);
                localStorage.setItem("accessToken", res.data.accessToken);
                localStorage.setItem("userId", res.data.userId);
                localStorage.setItem("userName", res.data.userName);
                this.$router.toReplace({
                  name: "organization",
                  params: {
                    segInfoList: res.data.segInfoList,
                  },
                });
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      seePassword() {
        this.active = !this.active;
      },
    },
  };
</script>

<style lang="less" scoped>
  .login-page {
    background-color: #ffffff;
    height: 100vh;
  }

  .see-password {
    color: #007aff;
    padding-right: 5px;
    font-size: 22px;
  }

  .notsee-password {
    padding-right: 5px;
    font-size: 22px;
  }

  .container {
    position: relative;
    width: 100%;
    height: 200px;
    margin: 0 auto;
    overflow: hidden;
    background: linear-gradient(180deg,
        #74b1f6 2%,
        #007aff 64%,
        rgba(0, 122, 255, 0.3) 76%,
        rgba(255, 255, 255, 0) 100%);
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
  }

  .wave,
  .wave1,
  .wave3,
  .wave2 {
    position: absolute;
    width: 100%;
    height: 205px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-30%, 0) rotate(160deg);
  }

  .wave1 {
    background-image: repeating-linear-gradient(rgba(255, 255, 255, 0.1));
    transform: translate(-40%, 0) rotate(180deg);
  }

  .wave3 {
    background-image: repeating-linear-gradient(rgba(255, 255, 255, 0.3));
    transform: translate(55%, 0) rotate(200deg);
    top: 140px;
  }

  .wave2 {
    background: rgba(255, 255, 255, 0.2);
    transform: translate(40%, 0) rotate(120deg);
  }

  .login-head {
    height: 140px;
  }

  .logo-img {
    padding-top: 35px;
    width: 50px;
    height: 50px;
    display: block;
    margin: 0 auto;
    // text-align: center;
    // margin: 35px 155px 0 155px;
  }

  .login-title {
    text-align: center;
    font-size: 18px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    line-height: 25px;
    letter-spacing: 1px;
  }

  .login-content {
    width: 100%;
    background-color: #ffffff;
    text-align: center;
  }

  .login-cell-group {
    display: inline-block;
    vertical-align: middle;
  }

  .login-content:after {
    /*在行内伪造一个元素，称为middle的靶子*/
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle;
  }

  .login-cell {
    width: 100%;
    height: 48px;
    margin-top: 20px;
    background: #ffffff;
    box-shadow: 0px 4px 8px -1px rgba(30, 82, 138, 0.19);
    border-radius: 6px 6px 6px 6px;
    opacity: 1;
    border: 1px solid #007aff;
  }

  .cell-remix {
    margin: 10px 0px 10px 20px;
    display: flex;
    align-items: center;
  }

  .login-input {
    width: 190px;
    border: none;
    height: 30px;
    font-size: 16px;
  }

  input::-webkit-input-placeholder {
    /* WebKit browsers*/
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #999999;
    line-height: 18px;
  }

  .icon-line {
    margin: 0 5px 0 5px;
    color: #999999;
  }

  .login-btn {
    margin-top: 20px;
  }

  .btn {
    width: 100%;
    height: 46px;
    background: #007aff;
    border: none;
    border-radius: 6px 6px 6px 6px;
    opacity: 1;
    font-size: 15px;
    font-weight: 500;
    color: #ffffff;
    line-height: 20px;
    //  letter-spacing: 28px;
    font-family: Noto Sans SC;
  }
</style>