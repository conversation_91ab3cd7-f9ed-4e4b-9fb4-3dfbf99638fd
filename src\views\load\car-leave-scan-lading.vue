<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">扫描提单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <!-- 车辆信息显示 -->
      <van-cell-group title="车辆信息" v-if="vehicleNo">
        <van-cell title="车牌号" :value="vehicleNo" v-if="vehicleNo" />
        <!-- <van-cell title="备注" :value="remark" v-if="remark" /> -->
      </van-cell-group>
      <van-field v-model="ladingBillId" ref="ladRef" label="提单号" placeholder="请扫描或输入提单号"
        @keyup.enter.native="topSearchFunc" :rules="[{ required: true, message: '请扫描或输入提单号' }]" />
    </van-sticky>

    <div class="load-content">
      <div v-if="selectedLadingList && selectedLadingList.length > 0">
        <div class="baletext2" style="margin-left: 10px; margin-top: 14px">
          已选提单合计:
          <span class="span-count">{{ selectedLadingList.length }}</span>
        </div>
        <van-swipe-cell v-for="(item, index) in selectedLadingList" :key="index">
          <van-cell>
            <template #title>
              <div class="load-number">{{ item.ladingBillId }}</div>
              <div class="">{{ item.settleUserName }}</div>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" text="删除" class="delete-bale-btn" @click="deleteItem(index)" />
          </template>
        </van-swipe-cell>
        <div class="mui-input-row" style="margin: 0" @click="onConfirm">
          <button type="button" class="mui-btn">
            确&nbsp; &nbsp; &nbsp;&nbsp;定
          </button>
        </div>
      </div>
      <div v-else>
        <van-empty description="暂未扫描提单号列表" />
      </div>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import { isEmpty } from "@/utils/tools.js";

export default {
  data() {
    return {
      vehicleNo: "",
      // remark: "",
      ladingBillId: "",
      selectedLadingList: [],
    };
  },
  created() {
    this.initData();
    this.$nextTick(() => {
      this.$refs.ladRef.focus();
    });
  },
  beforeDestroy() {
    // 保存当前状态到 sessionStorage
    this.saveState();
  },
  methods: {
    // 初始化数据 - 优先从路由参数获取，如果没有则从 sessionStorage 恢复
    initData() {
      console.log(this.$route.params);
      
      if (this.$route.params.vehicleNo) {
        // 从路由参数获取（新进入页面）
        this.vehicleNo = this.$route.params.vehicleNo;
        // this.remark = this.$route.params.remark;
        this.selectedLadingList = [];
      } else {
        // 从 sessionStorage 恢复状态（返回页面）
        this.restoreState();
      }
    },

    // 保存状态到 sessionStorage
    saveState() {
      const state = {
        vehicleNo: this.vehicleNo,
        // remark: this.remark,
        selectedLadingList: this.selectedLadingList,
      };
      sessionStorage.setItem('carLeaveScanLadingState', JSON.stringify(state));
    },

    // 从 sessionStorage 恢复状态
    restoreState() {
      try {
        const savedState = sessionStorage.getItem('carLeaveScanLadingState');
        if (savedState) {
          const state = JSON.parse(savedState);
          this.vehicleNo = state.vehicleNo || "";
          // this.remark = state.remark || "";
          this.selectedLadingList = state.selectedLadingList || [];
        }
      } catch (err) {
        console.error("恢复状态失败:", err);
        this.vehicleNo = "";
        // this.remark = "";
        this.selectedLadingList = [];
      }
    },

    onClickLeft() {
      // 清除保存的状态
      sessionStorage.removeItem('carLeaveScanLadingState');
      this.$router.goBack();
    },

    async topSearchFunc() {
      if (isEmpty(this.ladingBillId)) {
        this.$toast("请输入提单号!");
        return;
      }
      
      await this.getLadingBillList();
    },

    async getLadingBillList() {
      const params = {
        serviceId: "S_UC_PR_230628",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        ladingBillId: this.ladingBillId,
        pageNo: 1,
        pageSize: 100
      };
      
      let res = await baseApi.baseService(params);

      if (!res || !res.data) {
        this.$toast("调用失败");
        return;
      }

      if (res.data.__sys__.status == '-1') {
        this.$toast(res.data.__sys__.msg);
        return;
      }

      if (res.data.result.length == 0) {
        this.$toast("未查到相应提单信息！");
      } else if (res.data.result.length == 1) {
        let item = {
          ladingBillId: res.data.result[0].ladingBillId,
          logisticPlanId: '',
          ebillPassword: '',
          warehouseCode: res.data.result[0].warehouseCode,
          settleUserName: res.data.result[0].settleUserName,
          putoutPlanId: res.data.result[0].putoutPlanId
        };
        
        // 检查是否已经扫描过
        if (this.selectedLadingList.some(existItem => existItem.ladingBillId === item.ladingBillId)) {
          this.$toast("已扫描");
        } else {
          this.selectedLadingList.push(item);
          // 保存状态
          this.saveState();
        }
      } else {
        // 多条结果时，自动添加第一条
        let item = {
          ladingBillId: res.data.result[0].ladingBillId,
          logisticPlanId: '',
          ebillPassword: '',
          warehouseCode: res.data.result[0].warehouseCode,
          settleUserName: res.data.result[0].settleUserName,
          putoutPlanId: res.data.result[0].putoutPlanId
        };
        
        if (this.selectedLadingList.some(existItem => existItem.ladingBillId === item.ladingBillId)) {
          this.$toast("已扫描");
        } else {
          this.selectedLadingList.push(item);
          // 保存状态
          this.saveState();
        }
      }
      
      this.ladingBillId = "";
    },

    deleteItem(index) {
      this.$delete(this.selectedLadingList, index);
      // 保存状态
      this.saveState();
    },

    onConfirm() {
      if (this.selectedLadingList.length === 0) {
        this.$toast("请选择提单!");
        return;
      }
      // 保存状态
      this.saveState();
      this.$router.push({
        name: "carLeavePackDetail",
        params: {
          vehicleNo: this.vehicleNo,
          // remark: this.remark,
          ladingBillList: this.selectedLadingList,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  padding-bottom: 120px;
}

.activeColor {
  color: #007aff;
}

.nav-bar-scan {
  color: #fff;
}

.span-count {
  margin-left: 10px;
  color: #0000ff;
}

.info-btn {
  overflow: hidden;
  position: fixed;
  bottom: 0;
}

.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}

.search-list {
  height: 180px;
  border: 1px solid #f2f2f2;
  background-color: #f2f2f2;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}

.empty-des {
  text-align: center;
  padding-top: 30px;
  color: #BEBEBE;
}

.delete-bale-btn {
  height: 100%;
}

.mui-input-row {
  padding: 0 15px;
  
  .mui-btn {
    width: 100%;
    background: #007aff;
    color: #fff;
    border: none;
    padding: 15px;
    border-radius: 4px;
    font-size: 16px;
  }
}

.baletext2 {
  background-color: #fff;
  padding: 10px;
  border-bottom: 1px solid #f2f2f2;
}
</style> 