<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">账套选择</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>jiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div v-if="segInfoList && segInfoList.length > 0">
      <div class="in-content">
        <van-radio-group v-model="radio">
          <van-cell-group>
            <van-cell v-for="(item, index) in segInfoList" :key="index" clickable @click="isChecked(item, index)">
              <template #title>
                <div class="seg-title">{{ item.segNo }}</div>
              </template>
              <template #label>
                <div class="seg-name">{{ item.segCname }}</div>
              </template>
              <template #right-icon>
                <van-radio :name="item.segNo"><template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
      <div class="mui-input-row" style="margin: 0" @click="goFactory" v-show="isShowBottom">
        <button type="button" class="mui-btn">
          确&nbsp; &nbsp; &nbsp;&nbsp;定
        </button>
      </div>

    </div>
    <div v-else>
      <van-empty description="暂未查到账套信息" />
    </div>
  </div>
</template>

<script>
    import * as baseApi from "@/api/base-api";
  export default {
    data() {
      return {
        radio: '',
        segInfoList: [],
        orgVal: "",
        check: false,
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        pathUrl: "",
      };
    },
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.pathUrl = from.fullPath;
      });
    },
    mounted() {
      if (this.pathUrl === "/") {
        this.getOrganization();
      } else {
        this.segInfoList = this.$route.params.segInfoList;
      }
      //console.log('wwwww',this.$route.params.segInfoList);

      // sessionStorage.setItem('segInfoList',this.$route.params.segInfoList)
      // this.segInfoList = sessionStorage.getItem('segInfoList')
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        if (this.pathUrl === "/") {
          this.$router.goBack();
        } else {
          //如果不是新标签页打开的则直接返回
          this.$router.togo("/login");
        }
      },

      goFactory() {
        if (this.check) {
          this.$router.toReplace("/factory");
        } else {
          this.$toast("请选择业务单元！");
        }
      },
      async getOrganization() {
        const params = {
          serviceId: "S_UC_PR_200007",
          userId:  localStorage.getItem("userId"),
          accessToken:localStorage.getItem("accessToken"),
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                  this.segInfoList = dataObj.segInfoList;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      isChecked(item, index) {
        this.radio = item.segNo;
        this.check = true;
        localStorage.setItem("isCheckSeg", true);
        localStorage.setItem("segNo", item.segNo);
        localStorage.setItem("segName", item.segCname);
      },
      //根据输入值进行搜索
      goSearch() {
        this.companyList.map((item) => {
          if (item.name.includes(this.orgVal)) {
            console.log(item);
            // this.showGoods.push(item);
          }
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .search-list {
    margin-top: 8px;
  }

  .activeColor {
    color: #007aff;
  }

  .seg-title {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #007aff;
    line-height: 20px;
  }

  .seg-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #333333;
    line-height: 22px;
  }
</style>
