<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">打印列表</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div>
      <van-cell-group>
        <van-cell title="捆包远程打印" to="printBale" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group>
        <van-cell title="标签蓝牙打印" to="printBlueTooth" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
      <!-- <van-cell-group>
        <van-cell title="入库单打印" to="printIn">
          <template #right-icon>
            <span
              class="iconfont icon-youjiantou"
              style="color: #9f9f9f"
            ></span>
          </template>
        </van-cell>
      </van-cell-group> -->
      <van-cell-group>
        <van-cell title="出库单打印" to="printOut" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group>
        <van-cell title="吊装清单打印" to="hoistingList" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group>
        <van-cell title="资材采购合同打印" to="label-printe" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group>
        <van-cell title="实绩捆包打印" to="actual-package-print" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group>
     <!-- <van-cell-group>
        <van-cell title="捆包列表蓝牙打印" to="printPackIdList" class="all-font-size">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group> -->
      <!-- <van-button @click="androidNative">测试按钮</van-button> -->
    </div>
  </div>
</template>

<script>
// 导入自己定义的插件js
var elitetyc = require("../../../static/js/plugin.js");
export default {
  data() {
    return {
      plugins: elitetyc,
    };
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "print-out") {
      from.meta.keepAlive = false;
    }
    if (from.name === "print-in") {
      from.meta.keepAlive = false;
    }
    if (from.name === "hoisting-list") {
      from.meta.keepAlive = false;
    }
    next();
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    androidNative() {
      var params = {};
      params.segName = "开封威仕科材料技术有限公司";
      params.printCount = 1;
      // 点击事件内部调用了plugin.js中暴露的方法
      this.plugins.CalcNameAddNumFunction(
        params,
        function (result) {
          // Toast(JSON.stringify(result));
          console.log(JSON.stringify(result));
        }
      );
    },


  },
};
</script>

<style lang="less" scoped></style>
