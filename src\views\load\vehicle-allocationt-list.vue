<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">配车单列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuoji<PERSON>ou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <van-search v-model="value" shape="round" placeholder="配车单号" left-icon="" @search="getVehicleList"
        :clearable="true">
        <template #right-icon>
          <div class="iconfont icon-sousuo" style="color: #999999" @click="getVehicleList"></div>
        </template>
      </van-search>
    </van-sticky>

    <div v-if="vehicleList !== undefined && vehicleList != null && vehicleList.length > 0"  class="home">
      <van-cell-group v-for="(item,index) in vehicleList" :key="index" class="cell-group">
        <van-cell>
          <template #title>
            <div>
              {{item.allocateVehicleNo}}
            </div>
             <div class="div-display">
               <div>
                 <van-tag type="primary" size="medium" style="margin-right: 4px;" ><span class="tag-title">车</span></van-tag>{{item.vehicleNo}}
               </div>
               <div>
                 <van-tag type="primary" size="medium" style="margin-right: 4px;" ><span class="tag-title">司</span></van-tag>{{item.driverName}}
               </div>
               <div>
                <van-tag type="primary" size="medium" @click="goItem(item.allocateVehicleNo)"><span class="tag-title">详情</span></van-tag>
               </div>
             </div>
           </template>

        </van-cell>
       <!-- <van-cell title="配车单号 " :value="item.allocateVehicleNo" />
        <van-cell title="车牌号 " :value="item.vehicleNo" />
        <van-cell title="司机名称 " :value="item.driverName" />
        <van-cell title="详情" @click="goItem(item.allocateVehicleNo)">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell> -->
        <van-cell title="加单" @click="goAdd(item.allocateVehicleNo)">
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
         <!-- <template #default>
            {{backList.length}} <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template> -->
        </van-cell>

      </van-cell-group>
    </div>
    <div v-else>
      <van-empty description="暂无配车单列表" />
    </div>

  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        backList: [],
        vehicleList: [],
        activeNames: [],
        rowList: [],
        currentIndex: -1,
        value: "",
        allocateVehicleNo: "",
        uuid: "",
        vehicleNo: "",
        check: false,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    beforeRouteEnter(to, from, next) {
      if (from.name === "vehicleAdd") {
        from.meta.keepAlive = false;
      }
      next();
    },
    created() {
      this.getVehicleList();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      isChecked(index, item) {
        this.currentIndex = index;
        this.check = true;
        this.uuid = item.uuid;
        this.rowList = [];
        //let arr =  this.rowList.push({uuid:item.uuid})
      },
      testOpen() {
        console.log("dianlij");
      },
      goAdd(val) {
        this.$router.togo({
          name: "vehicleAdd",
          params: {
            allocateVehicleNo: val,
          },
        });
      },
      goItem(val) {
        this.$router.togo({
          name: "vehicleAllocationtItem",
          params: {
            allocateVehicleNo: val,
          },
        });
      },
      //查询配车单列表
      async getVehicleList() {
        this.vehicleList = [];
        const params = {
          serviceId: "S_UC_PR_210107",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          allocateVehicleNo: this.value
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  .home {
    background-color: rgba(243, 243, 243, 1);
  }

  .load-content {
    padding-bottom: 100px;
  }

  .activeColor {
    color: #007aff;
  }
  .div-display{
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
  }
  .tag-title{
    padding: 3px;
  }
  .list {
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 25px;
    // border-radius: 8px;
    border: 1px solid #dcdcdc;
  }

  .list-left {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
    margin-left: 5px;
    margin-top: 10px;
  }

  .list-right {}

  .btn {
    margin-top: 5px;
  }

  .number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
    // margin-left: 5px;
  }

  .more-des {
    font-size: 15px;
  }

  .cell-group {
    margin-bottom: 8px;
  }
</style>
