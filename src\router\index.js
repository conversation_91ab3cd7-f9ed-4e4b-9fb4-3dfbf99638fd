import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)
// 需要左方向动画的路由用this.$router.to('****')
VueRouter.prototype.togo = function (path) {
  this.isleft = true
  this.isright = false
  this.push(path)
}
VueRouter.prototype.toReplace = function (path) {
  this.isleft = true
  this.isright = false
  this.replace(path)
}
// 需要右方向动画的路由用this.$router.goRight('****')
VueRouter.prototype.goRight = function (path) {
  this.isright = true
  this.isleft = false
  this.push(path)
}
// 需要返回按钮动画的路由用this.$router.goBack()，返回上一个路由
VueRouter.prototype.goBack = function () {
  this.isright = true
  this.isleft = false
  this.go(-1)
}
// 点击浏览器返回按钮执行，此时不需要路由回退
VueRouter.prototype.togoback = function () {
  this.isright = true
  this.isleft = false
}
// 点击浏览器前进按钮执行
VueRouter.prototype.togoin = function () {
  this.isright = false
  this.isleft = true
}
const Router = new VueRouter({
  routes: [
    {
      path: '/',
      name: 'index',
      meta: { auth: true },
      component: () => import('../views/index')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/login')
    },
    {
      path: '/instorage',
      name: 'instorage',
      component: () => import('../views/instorage')
    }, {
      path: '/inlist',
      name: 'inlist',
      component: () => import('../views/instorage/inlist')
    }, {
      path: '/selectCar',
      name: 'select-car',
      component: () => import('../views/instorage/select-car')
    },
    {
      path: '/outstorage',
      name: 'outstorage',
      component: () => import('../views/outstorage')
    },
    {
      path: '/lodingBill',
      name: 'lodingBill',
      component: () => import('../views/outstorage/lading-bill')
    },{
      path: '/outinventory',
      name: 'outinventory',
      component: () => import('../views/outstorage/outinventory')
    }, {
      path: '/selectOption',
      name: 'selectOption',
      meta: { check: true },
      component: () => import('../views/outstorage/select-option')
    }, {
      path: '/outlist',
      name: 'outlist',
      component: () => import('../views/outstorage/outlist')
    },
    {
      path: '/group',
      name: 'group',
      component: () => import('../views/group')
    },
    {
      path: '/check',
      name: 'check',
      component: () => import('../views/check')
    },
    {
      path: '/check2',
      name: 'check2',
      component: () => import('../views/check/index2')
    }, {
      path: '/checkStorage',
      name: 'check-storage',
      component: () => import('../views/check/check-storage')
    }, {
      path: '/withoutStorage',
      name: 'without-storage',
      component: () => import('../views/check/without-storage')
    },
    {
      path: '/scanCheck',
      name: 'scan-check',
      component: () => import('../views/check/scan-check')
    }, {
      path: '/checkList',
      name: 'check-list',
      component: () => import('../views/check/check-list')
    }, {
      path: '/checkMap',
      name: 'check-map',
      component: () => import('../views/check/check-map')
    }, {
      path: '/checkMapDemo',
      name: 'check-map-demo',
      component: () => import('../views/check/check-map-demo')
    },  {
      path: '/checkListDetail',
      name: 'check-list-detail',
      component: () => import('../views/check/check-list-detail')
    },
    {
      path: '/organization',
      name: 'organization',
      component: () => import('../views/login/organization')
    }, {
      path: '/factory',
      name: 'factory',
      component: () => import('../views/factory')
    }, {
      path: '/load',
      name: 'load',
      component: () => import('../views/load'),
    }, {
      path: '/carLoad',
      name: 'car-load',
      component: () => import('../views/load/car-load'),
    }, {
      path: '/pictureUpload',
      name: 'picture-upload',
      component: () => import('../views/load/picture-upload'),
    }, {
      path: '/ladingList',
      name: 'lading-list',
      component: () => import('../views/load/lading-list'),
    },{
      path: '/ladingReversal',
      name: 'lading-reversal',
      component: () => import('../views/load/lading-reversal'),
    }, {
      path: '/inventory',
      name: 'inventory-load',
      component: () => import('../views/load/inventory-load'),
    },{
      path: '/factoryLoad',
      name: 'factory-load',
      component: () => import('../views/load/factory-load'),
    }, {
      path: '/choose',
      name: 'choose-load',
      component: () => import('../views/load/choose-load'),
    }, {
      path: '/callNumber',
      name: 'call-number',
      component: () => import('../views/load/call-number'),
    },  {
      path: '/gateGuardFactory',
      name: 'gate-guard-factory',
      component: () => import('../views/load/gate-guard-factory'),
    }, {
      path: '/chooseInventory',
      name: 'choose-inventory',
      component: () => import('../views/load/choose-inventory'),
    }, {
      path: '/searchList',
      name: 'search-list',
      component: () => import('../views/search-list'),
    }, {
      path: '/searchInstorage',
      name: 'search-instorage',
      meta: { keepAlive: true, },
      component: () => import('../views/search-list/search-instorage'),
    }, {
      path: '/searchOutstorage',
      name: 'search-outstorage',
      meta: { keepAlive: true, },
      component: () => import('../views/search-list/search-outstorage'),
    }, {
      path: '/searchBale',
      name: 'search-bale',
      component: () => import('../views/search-list/search-bale'),
    }, {
      path: '/lableContrast',
      name: 'lable-contrast',
      component: () => import('../views/search-list/lable-contrast'),
    }, {
      path: '/uni-board',
      name: 'uni-board',
      meta: { keepAlive: true, },
      component: () => import('../views/search-list/uni-board'),
    }, {
      path: '/search-inventory',
      name: 'search-inventory',
      component: () => import('../views/search-list/search-inventory'),
    }, {
      path: '/uni-board/uni-board-info',
      name: 'uni-board-info',
      component: () => import('../views/search-list/uni-board-info'),
    },
    {
      path: '/invertedStorehouse',
      name: 'inverted-storehouse',
      component: () => import('../views/inverted-storehouse'),
    },
    {
      path: '/nextLoad',
      name: 'nextLoad',
      component: () => import('../views/load/next-load'),
    },
    {
      path: '/callbackCar',
      name: 'callbackCar',
      component: () => import('../views/load/callback-car'),
    },
    {
      path: '/loadList',
      name: 'loadList',
      component: () => import('../views/load/load-list'),
    },
    {
      path: '/endNext',
      name: 'endNext',
      component: () => import('../views/load/end-next'),
    },
    {
      path: '/endLoad',
      name: 'endLoad',
      component: () => import('../views/load/end-load'),
    },
    {
      path: '/factoryConfirmation',
      name: 'factoryConfirmation',
      component: () => import('../views/load/factory-confirmation'),
    },{
      path: '/vehicleAllocationtList',
      name: 'vehicleAllocationtList',
       meta: { keepAlive: true },
      component: () => import('../views/load/vehicle-allocationt-list'),
    },{
      path: '/vehicleAllocationtItem',
      name: 'vehicleAllocationtItem',
      component: () => import('../views/load/vehicle-allocationt-item'),
    },{
      path: '/vehicleAdd',
      name: 'vehicleAdd',
     meta: { keepAlive: true },
      component: () => import('../views/load/vehicle-add'),
    },{
      path: '/vehicleAddItem',
      name: 'vehicleAddItem',
      component: () => import('../views/load/vehicle-add-item'),
    },
    {
      path: '/checkOutlist',
      name: 'checkOutlist',
      component: () => import('../views/load/check-outlist'),
    },
    {
      path: '/leaveFactory',
      name: 'leave-factory',
      component: () => import('../views/leave-factory'),
    }, {
      path: '/inFactory',
      name: 'in-factory',
      component: () => import('../views/leave-factory/in-factory'),
    }, {
      path: '/leaveBale',
      name: 'leave-bale',
      component: () => import('../views/leave-factory/leave-bale'),
    }, {
      path: '/baleInfo',
      name: 'bale-info',
      component: () => import('../views/leave-factory/bale-info'),
    }, {
      path: '/baleScan',
      name: 'bale-scan',
      component: () => import('../views/leave-factory/bale-scan'),
    }, {
      path: '/searchDetail',
      name: 'search-detail',
      component: () => import('../views/search-list/search-detail'),
    }, {
      path: '/printLabel',
      name: 'print-label',
      component: () => import('../views/print-label'),
    }, {
      path: '/hoistingList',
      name: 'hoisting-list',
      meta: { keepAlive: true },
      component: () => import('../views/print-label/hoisting-list'),
    }, {
      path: '/printPackIdList',
      name: 'packId-list',
      meta: { keepAlive: true },
      component: () => import('../views/print-label/packId-list'),
    },{
      path: '/printHoisting',
      name: 'print-hoisting',
      component: () => import('../views/print-label/print-hoisting'),
    }, {
      path: '/printBale',
      name: 'print-bale',
      component: () => import('../views/print-label/print-bale'),
    }, {
      path: '/printBlueTooth',
      name: 'print-bluetooth',
      component: () => import('../views/print-label/print-bluetooth'),
    }, {
      path: '/printIn',
      meta: { keepAlive: true },
      name: 'print-in',
      component: () => import('../views/print-label/print-in'),
    }, {
      path: '/printOut',
      meta: { keepAlive: true },
      name: 'print-out',
      component: () => import('../views/print-label/print-out'),
    }, {
      path: '/printDetail',
      name: 'print-detail',
      component: () => import('../views/print-label/print-detail'),
    }, {
      path: '/uninstall',
      name: 'uninstall',
      component: () => import('../views/uninstall'),
    }, {
      path: '/signature',
      name: 'signature',
      component: () => import('../views/signature'),
    }, {
      path: '/signatureIn',
      name: 'signatureIn',
      component: () => import('../views/signature/signature'),
    }, {
      path: '/prallelPackage',
      name: 'prallel-package',
      component: () => import('../views/prallel-package'),
    }, {
      path: '/generatePackage',
      name: 'generate-package',
      component: () => import('../views/prallel-package/generate-package'),
    }, {
      path: '/revokePackage',
      name: 'revoke-package',
      component: () => import('../views/prallel-package/revoke-package'),

    }, {
      path: '/label-printe',
      name: 'label-printe',
      component: () => import('../views/print-label/label-printe'),
    }, {
      path: '/actual-package-print',
      name: 'actual-package-print',
      component: () => import('../views/print-label/actual-package-print'),
    },
    {
      path: '/operatorVehicles',
      name: 'operator-vehicles',
      component: () => import('../views/load/operator-vehicles'),
    },
    {
      path: '/carLeaveSelectVehicle',
      name: 'carLeaveSelectVehicle',
      component: () => import('../views/load/car-leave-select-vehicle'),
    },
    {
      path: '/carLeaveScanLading',
      name: 'carLeaveScanLading',
      component: () => import('../views/load/car-leave-scan-lading'),
    },
    {
      path: '/carLeavePackDetail',
      name: 'carLeavePackDetail',
      component: () => import('../views/load/car-leave-pack-detail'),
    },
    {
      path: '/wasteMaterial',
      name: 'waste-material',
      component: () => import('../views/load/waste-material'),
    },
    // ,{
    //   path: '/testScan',
    //   name: 'test-scan',
    //   component: () => import('../views/test-scan'),
    // },{
    //   path: '/otherUrl',
    //   name: 'other-url',
    //   component: () => import('../views/test-scan/other-url'),
    // }
  ]
})
Router.beforeEach((to, from, next) => {
  if (to.matched.some(m => m.meta.auth)) {
    if (localStorage.getItem('isLogin') && localStorage.getItem("isCheckSeg")) {
      next()
    } else {
      next({ path: '/login' })
    }
  } else {
    next()
  }
})
// Router.beforeEach((to, from, next) => {
//   if (to.matched.some(m => m.meta.check)) {
//     // localStorage.setItem("teamName", this.groupName);
//     // localStorage.setItem("factoryArea", this.factoryArea);
//     console.log(localStorage.getItem('factoryArea'));
//     if (localStorage.getItem('factoryArea') && localStorage.getItem("teamName")) {
//       next()
//     } else {
//       next({ path: '/factory' })
//     }
//   } else {
//     next()
//   }
// })
export default Router
