/* eslint-disable */
import axios from 'axios'
import {
  Toast,
  Dialog
} from 'vant';
/**
 * 定义请求常量
 * TIME_OUT、ERR_OK
 */
export const TIME_OUT = 30000; // 请求超时时间
export const ERR_OK = true; // 请求成功返回状态，字段和后台统一
export let baseUrl = '';
export let isSG = false;
//export const baseUrl = process.env.BASE_URL   // 引入全局url，定义在全局变量process.env中，开发环境为了方便转发，值为空字符串

// 请求超时时间
axios.defaults.timeout = TIME_OUT
let loading

function startLoading() {
  loading = Toast.loading({
    duration: 0,
    forbidClick: true,
  })
}

function endLoading() {
  loading.clear()
}


// 封装请求拦截
axios.interceptors.request.use(
  config => {
    startLoading();
    let token = localStorage.getItem('accessToken') // 获取token
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    if (token != null) { // 如果token不为null，否则传token给后台
      config.headers['Xplat-Token'] = token
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)
// 封装响应拦截，判断token是否过期
axios.interceptors.response.use(
  response => {
    endLoading();
    if (isSG) {
      return Promise.resolve(response)
    }
    if (response.data.__sys__.status == -1) {
      // Toast(response.data.__sys__.msg);
      Toast({
        message: response.data.__sys__.msg,
        duration: 3000,
      });
    } else if (response.data.__sys__.status == 0) {
      Dialog.alert({
        title: '提示',
        message: '登录信息失效，请重新登录',
      }).then(() => {
        localStorage.clear();
        sessionStorage.clear();
        this.$router.push("/login");
        // on close
      });
      // Toast('登录信息失效，请重新登录');
    } else if (response.data.__sys__.status == 2) {
      localStorage.setItem("accessToken", response.data.accessToken);
    } else {
      return Promise.resolve(response)
    }
    return Promise.resolve(response)
  },
  err => {
    endLoading();
    Toast('请求异常,请重试')
    return Promise.reject(err)
  }
)

    if (process.env.NODE_ENV == "production") {
      baseUrl = 'http://imc.baogang.info';
    } else if (process.env.NODE_ENV == "test") {
      baseUrl = 'http://imctest.baogang.info';
    } else {
      baseUrl = '/api';
    }
  
// 封装post请求
export function fetch(requestUrl, params) {
  isSG = false;
  return new Promise((resolve, reject) => {
    axios.post(requestUrl, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 封装post请求
export function fetch2(params) {
  isSG = false;
  return new Promise((resolve, reject) => {
    axios.post(`${baseUrl}/elim-bc/service`, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  })
}

export function fetch3(params) {
  isSG = true;
  return new Promise((resolve, reject) => {
    axios.post(`${baseUrl}/imc-sg/service`, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 生产模块接口
export function fetch5(params) {
  isSG = true;
  return new Promise((resolve, reject) => {
    axios.post(`${baseUrl}/imc-ma/service`, params)
      .then(response => {
        resolve(response);
      }, err => {
        reject(err);
      })
      .catch((error) => {
        reject(error)
      })
  })
}
