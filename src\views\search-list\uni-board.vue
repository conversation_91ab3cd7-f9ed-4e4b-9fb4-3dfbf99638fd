<template>
    <div class="beijin">
      <van-sticky>
        <van-nav-bar>
          <template #title>
            <div class="global-hfont">优尼看板</div>
          </template>
          <template #left>
            <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
          </template>
        </van-nav-bar>
        <van-cell title="要货时间起" class="all-font-size" :value="demandPlanDateStart" is-link @click="showCalendarStart" />
        <van-cell title="要货时间止" class="all-font-size" :value="demandPlanDateEnd" is-link @click="showCalendarEnd" />
        <van-field v-model="customerId" clearable class="all-font-size" label="客户代码" placeholder="请输入客户代码"
          input-align="right" />

        <van-field v-model="partId" @keyup.enter.native="getUniBoard" center clearable class="all-font-size" label="客户零件号"
          placeholder="请输入客户零件号">
          <template #button>
            <van-button size="small" type="info" class="all-font-size" @click="getUniBoard">查询</van-button>
          </template>
        </van-field>
      </van-sticky>
      <div class="in-content beijin" v-if="searchList && searchList.length != 0">
        <h2 class="van-doc-demo-block__title">主项信息</h2>
        <van-cell-group class="detail_textarea" inset v-for="(item, index) in searchList" :key="index"
          @click="getItemInfo(item);">
          <van-cell title="客户订单号" :value="item.custPurOrderNum" />
          <van-cell title="客户零件号" :value="item.custPartId" />
          <van-cell title="客户代码" :value="item.customerId" />
          <van-cell title="客户名称" :value="item.customerName" />
          <van-cell title="规格" :value="item.specDesc" />
          <van-cell title="牌号" :value="item.shopsign" />
          <van-cell title="台份" :value="item.demandPlanQty" />
          <van-cell title="看板接收时间" :value="item.demandPlanDate" />
          <van-cell title="上传张数" :value="item.demandDeliveryQty" />
          <van-cell title="上传重量" :value="item.sumNetWeight" />
        </van-cell-group>
      </div>
      <div v-else>
        <van-empty description="暂无优尼看板信息" />
      </div>


      <calendar @change="onStartChange" :show.sync="showStart" :weekNames="weekList" />
      <calendar @change="onEndChange" :show.sync="showEnd" :weekNames="weekList" />
    </div>
</template>
  
<script>
import * as baseApi from "@/api/base-api";
import HmInput from "@/components/input.vue";
export default {
  data() {
    return {
      customerId: "",
      searchList: [],
      demandPlanDateStart: '',
      showStart: false,
      weekList: ["一", "二", "三", "四", "五", "六", "日"],
      dateStart: '',
      dateEnd: '',
      showEnd: false,
      demandPlanDateEnd: '',
      partId: '',
    };
  },
  components: {
    HmInput,
  },
  created() {
    const dateNow = new Date();
    const year = dateNow.getFullYear();
    const month = dateNow.getMonth() + 1;
    const strMonth = month < 10 ? `0${month}` : month;
    const day = dateNow.getDate();
    const strDay = day < 10 ? `0${day}` : day;
    this.demandPlanDateStart = `${year}/${strMonth}/${strDay}`;
    this.demandPlanDateEnd = this.demandPlanDateStart;
    this.dateStart = `${year}${strMonth}${strDay}`;
    this.dateEnd = this.dateStart;
    this.customerId = '010127';
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goInList() {
      this.$router.togo("/inlist");
    },
    showCalendarStart() {
      this.showStart = true;
    },
    onStartChange(date) {
      console.log(date,'112321');
      this.demandPlanDateStart = date.format("YYYY/MM/DD");
      this.dateStart = date.format("YYYYMMDD");
    },
    showCalendarEnd() {
      this.showEnd = true;
    },
    onEndChange(date) {
      this.demandPlanDateEnd = date.format("YYYY/MM/DD");
      this.dateEnd = date.format("YYYYMMDD");
    },
    // 查看详情
    getItemInfo(data) {
      this.$router.togo({
        name: "uni-board-info",
        params: {
          data,
        },
      });
    },

    async getUniBoard() {
      const userId = localStorage.getItem("userId");
      const segNo = localStorage.getItem("segNo");
      const segName = localStorage.getItem('segName');
      const params = {
        serviceId: "S_UW_DP_0131",
        flag: 'new',
        __blocks__: {
          inqu_status: {
            meta: {
              columns: [
                {
                  "pos": 0,
                  "name": "unitCode",
                  "descName": " "
                },
                {
                  "pos": 1,
                  "name": "segName",
                  "descName": " "
                },
                {
                  "pos": 2,
                  "name": "segNo",
                  "descName": " "
                },
                {
                  "pos": 3,
                  "name": "customerId",
                  "descName": " "
                },
                {
                  "pos": 4,
                  "name": "demandPlanDateStart",
                  "descName": " "
                },
                {
                  "pos": 5,
                  "name": "demandPlanDateEnd",
                  "descName": " "
                },
                {
                  "pos": 6,
                  "name": "custPartId",
                  "descName": " "
                },
              ],
              "attr": {},
              "desc": ""
            },
            "attr": {},
            "rows": [
              [
                segNo,
                segName,
                segNo,
                this.customerId, // 客户代码
                this.dateStart, // 要货时间起
                this.dateEnd, // 要货时间止
                this.partId
              ],
            ],
          },
          "loginUser": {
            "meta": {
              "columns": [
                {
                  "pos": 0,
                  "name": "loginName",
                  "descName": " "
                },
                {
                  "pos": 1,
                  "name": "userName",
                  "descName": " "
                },
                {
                  "pos": 2,
                  "name": "userId",
                  "descName": " "
                }
              ]
            },
            "rows": [
              [
                userId,
                localStorage.getItem("userName"),
                userId
              ]
            ]
          },
        },
      };
      await baseApi.sgBaseService(params)
        .then((res) => {
          console.log(res);
          if (!res || !res.data || !res.data.__sys__ || res.data.__sys__.status != 0) {
            this.$toast("未查询到数据!");
            this.searchList = [];
            return;
          }
          this.$toast(res.data.__sys__.msg);
          this.searchList = res.data.infoList.map(d => {
            return {
              ...d,
              demandDeliveryQty: d.demandDeliveryQty == '0E-8' ? '0' : d.demandDeliveryQty,
              sumNetWeight: d.sumNetWeight == '0E-8' ? '0' : d.sumNetWeight,
            };
          });

        })
        .catch((err) => {
          this.searchList = [];
          console.log(err);
        });
    },
  },
};
</script>
  
<style lang="less" scoped>
.span-count {
  margin-left: 10px;
  color: #0000ff;
}

.search-out-btn {
  width: 18%;
  padding: 5px;
  background: #007aff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 22px;
}

.status-color {
  color: #0000ff;
}

.detail_textarea {
  margin-bottom: 1em;
}

.beijin {
  background-color: rgba(243, 243, 243, 1);
}

.van-doc-demo-block__title {
  margin: 0;
  padding: 16px 16px 5px 16px;
  color: rgba(69, 90, 100, .6);
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
}
</style>
  