<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">单据明细</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div style="text-align: left">
      <div
        class="detail_textarea"
        v-for="(item, index) in searchList"
        :key="index"
      >
        <div class="border_top">
          <div class="content_div">
            <div class="big-font">捆包号：</div>
            <div class="big-font">{{ item.packId }}</div>
          </div>
          <div class="content_div2">
            <div class="div-flex">
              <div class="check-spec">毛重/净重：</div>
              <div class="check-val">
                {{ item.grossWeight }}/{{ item.netWeight }}
              </div>
            </div>
            <div class="div-flex">
              <div class="check-spec">件数：</div>
              <div class="check-val">{{ item.pieceNum }}</div>
            </div>
          </div>
          <div class="content_div">
            <div class="check-spec">库位：</div>
            <div class="check-val">{{ item.locationId }}</div>
          </div>
          <div class="content_div">
            <div class="check-spec">库位名称：</div>
            <div class="check-val">{{ item.locationName }}</div>
          </div>
          <div class="content_div">
            <div class="check-spec">规格：</div>
            <div class="check-val">{{ item.specsDesc }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      putinId: "",
      putoutId: "",
      searchList: [],
    };
  },
  created() {
    this.putinId = this.$route.query.putinId;
    this.putoutId = this.$route.query.putoutId;
    if (this.$route.query.putinId != null) {
      this.getSearchInList();
    }
    if (this.$route.query.putoutId != null) {
      this.getSearchOutList();
    }
  },
  beforeRouteLeave(to, from, next) {
    to.meta.keepAlive = true;
    next(0);
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    //查询入库单明细
    async getSearchInList() {
      const params = {
        serviceId: "S_UC_PR_230608",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        // userId: "R00647",
        // accessToken:
        //   "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        // segNo: "QL000000",
        putinId: this.putinId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.searchList = dataObj.result;
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //查询入库单明细
    async getSearchOutList() {
      const params = {
        serviceId: "S_UC_PR_230610",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putoutId: this.putoutId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.searchList = dataObj.result;
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.big-font {
  font-size: 16px;
  color: #007aff;
  font-family: Noto Sans SC;
  font-weight: 500;
  line-height: 22px;
}
.check-spec {
  font-size: 15px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 21px;
}
.check-val {
  font-size: 16px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}
.div-flex {
  display: flex;
  justify-content: space-between;
}
</style>
