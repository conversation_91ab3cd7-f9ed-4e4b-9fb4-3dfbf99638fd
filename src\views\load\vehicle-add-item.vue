<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">物流计划捆包列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div v-if="vehicleList !== undefined && vehicleList != null && vehicleList.length > 0">
      <div class="in-content home">
         <van-cell-group :title="logisticPlanId">
         </van-cell-group>
        <van-cell-group v-for="(item,index) in vehicleList" :key="index" class="cell-group">
          <van-cell title="捆包号: " :value="item.packId" />
          <van-cell title="规格: " :value="item.specsDesc" />
          <van-cell title="毛重: " :value="item.grossWeight" />
          <van-cell title="净重: " :value="item.netWeight" />
          <van-cell title="件数: " :value="item.pieceNum" />
        </van-cell-group>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无捆包列表" />
    </div>

  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  let previousRouterName = "";
  let selectUser = "";
  export default {
    data() {
      return {
        vehicleList: [],
        activeNames: [],
        rowList: [],
        currentIndex: -1,
        logisticPlanId: "",
        uuid: "",
        vehicleNo: "",
        check: false,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    created() {
      this.logisticPlanId = this.$route.params.logisticPlanId;
      if (this.logisticPlanId) {
        this.getVehicleList();
      }

    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    beforeRouteLeave(to, from, next) {
      to.meta.keepAlive = true;
      next(0);
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      //查询物流计划捆包
      async getVehicleList() {
        this.vehicleList = [];
        const params = {
          serviceId: "S_UC_PR_210110",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          logisticPlanId: this.logisticPlanId
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  .home {
    background-color: rgba(243, 243, 243, 1);
  }


  .cell-group {
    margin-bottom: 8px;
  }
</style>
