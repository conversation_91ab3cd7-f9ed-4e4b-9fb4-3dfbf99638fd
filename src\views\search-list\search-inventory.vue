<template>
  <div class="beijin">
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">库存查询</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content beijin">
      <van-pull-refresh
        v-model="isLoading"
        success-text="刷新成功"
        @refresh="inentoryListLoad"
      >
        <template v-if="searchList && searchList.length != 0">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="getInentoryList"
          >
            <van-cell-group
              inset
              v-for="(item, index) in searchList"
              :key="index"
              class="detail_textarea"
            >
              <van-cell
                title="标签号："
                :value="item.labelId"
                :border="false"
              />
              <van-cell title="捆包：" :value="item.packId" :border="false" />
              <van-cell
                title="提单号："
                :value="item.entityBillId"
                :border="false"
              />
              <van-cell title="规格：" :value="item.specDesc" :border="false" />
              <van-cell
                title="数量/件数："
                :value="item.pieceNum"
                :border="false"
              />
              <van-cell
                title="库位编码："
                :value="item.locationId"
                :border="false"
              />
              <van-cell
                title="库存状态："
                value-class="status-color"
                :value="item.instockFlagName"
                :border="false"
              />
              <van-cell
                value-class="status-color"
                title="锁定状态："
                :value="item.blockadeTypeName"
                :border="false"
              />
              <van-cell
                title="库位名称："
                :value="item.locationName"
                :border="false"
              />
              <van-cell
                title="毛重/净重："
                :value="`${item.grossWeight}/${item.netWeight}`"
                :border="false"
              />
              <van-cell
                title="仓库："
                :value="item.warehouseName"
                :border="false"
              />
              <van-cell
                title="客户："
                :value="item.customerName"
                :border="false"
              />
              <van-cell title="车辆：" :value="item.vehicleCode" />
            </van-cell-group>
          </van-list>
        </template>
        <template v-else>
          <van-empty description="暂无库存" />
        </template>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";

export default {
  data() {
    return {
      searchVal: "",
      searchList: [],
      segNo: "",
      warehouseCode: "",
      loading: false,
      finished: false,
      isLoading: false,
      offset: 0
    };
  },
  async created() {
    await this.getInentoryList();
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    async getInentoryList() {
      const params = {
        serviceId: "S_UC_PR_200008",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        offSet: this.offset,
        limit: 10
      };
      const data = await baseApi.baseService(params);
      if (!data || !data.data) {
        this.$toast("网络异常, 请联系管理员!");
        return;
      }
      if (data.data.result.length > 0) {
        data.data.result.forEach(d => {
          this.stockPackList.push(d);
        });
        this.page++;
          
        } else {
         this.finished = true; // 没有更多数据，设置 finished 为 true
        }
        this.loading = false; // 加载完成


      // data.data.result.forEach(d => {
      //   this.searchList.push(d);
      // });

      // this.offset += 10;
      // this.loading = false;
      this.finished = false;
      // if (this.offset >= data.data.totalCount) {
      //   this.finished = true;
      // }
      this.$toast(data.data.__sys__.msg);
    },
    async inentoryListLoad() {
      const params = {
        serviceId: "S_UC_PR_200008",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        offSet: 0,
        limit: 10
      };
      const data = await baseApi.baseService(params);
      if (!data || !data.data) {
        this.isLoading = false;
        this.$toast("网络异常, 请联系管理员!");
        return;
      }
      this.searchList = data.data.result;
      this.offset = 10;
      this.isLoading = false;
    }
  }
};
</script>

<style lang="less" scoped>
.detail_textarea {
  margin-bottom: 1em;
  background-color: #fff;
}
.beijin {
  background-color: #f3f3f3;
}
.status-color {
  color: #0000ff;
}
</style>
