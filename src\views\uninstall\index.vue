<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">卸货确认</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <van-field name="radio" label="装卸类型" class="all-font-size">
      <template #input>
        <van-radio-group v-model="radio" direction="horizontal">
          <van-radio name="00"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
            卸
          </van-radio>
          <van-radio name="10"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
            装
          </van-radio>
        </van-radio-group>
      </template>
    </van-field>
    <van-field name="imgRadio" label="上传图片" class="all-font-size">
      <template #input>
        <van-radio-group v-model="imgRadio" direction="horizontal">
          <van-radio name="00"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
            否
          </van-radio>
          <van-radio name="10"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
            是
          </van-radio>
        </van-radio-group>
      </template>
    </van-field>
    <van-field v-model="packId" label="捆包" class="all-font-size" ref="labelsecond" clearable placeholder="请输入或扫描捆包号"
      :rules="[{ required: true, message: '请输入或扫描捆包号' }]" @keyup.enter.native="getBaleByPackId" />
    <van-field label="库位" v-model="locationId" class="all-font-size" placeholder="库位" disabled />
    <van-field label="库位名称" v-model="locationName" class="all-font-size" placeholder="库位名称" disabled />
    <van-field name="uploader" label="图片上传" class="all-font-size" v-show="imgRadio==='10'">
      <template #input>
        <van-uploader v-model="uploaderList"  :upload-icon="uploadIcon"  @oversize="onOversize" multiple :max-count="5"
          accept="image/*" />
      </template>
    </van-field>
    <div class="in-content">
      <div class="detail_textarea">
        <div class="detail_text" style="padding-bottom: 10px">
          <div class="fourline-blue"></div>
          <div class="baletext2" style="margin-left: 0; margin-top: 14px">
            已扫捆包合计 : <span class="span-count">{{ packList.length }}</span>
          </div>
        </div>
      </div>

      <div class="search-list" v-if="packList && packList.length > 0">
        <van-swipe-cell v-for="(item, index) in packList" :key="index">
          <van-cell :border="false">
            <template #title>
              <div class="custom-title">标签号：{{ item.labelId }}</div>
              <div class="custom-title">捆包号: {{ item.packId }}</div>
              <div class="div-location">
                <div>
                  库位编码: {{ item.locationId }}
                </div>
                <div>
                  库位名称: {{item.locationName}}
                </div>
              </div>
            </template>
            <template #default>
              <div class="custom-title">重/件：{{ item.netWeight }}/{{ item.pieceNum }}</div>
              <div v-show="item.pictureDetail.length > 0">
                <van-button type="info" @click="showImg(item,index)">图片</van-button>
              </div>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" class="van-btn-height" text="删除" @click="deleteListItem(index)" />
          </template>
        </van-swipe-cell>
      </div>
      <div v-else></div>
    </div>
    <div class="mui-input-row3" v-show="isShowBottom">
      <button id="block_button" type="button" class="mui-btn3" @click="onConfirm">
        确&nbsp; &nbsp; &nbsp;&nbsp; 认
      </button>
      <button id="block_button" type="button" class="mui-btn3" @click="generateInventoryLst" v-preventReClick="3000">
        上&nbsp; &nbsp; &nbsp;&nbsp;传
      </button>
    </div>
    <HmPopup v-if="showPop" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
    <van-popup v-model="showImgList" :style="{ height: '50%' }" position="bottom">
      <div class="img-list">
        <div v-for="(item,index) in imgList" :key="item.thumbnailDocId" class="div-item">
          <van-badge color="#007aff">
            <van-image width="120" height="120" :src="item.thumbnailDocUrl" @click="seeBigImg(item,index)" />
            <template #content>
              <div class="iconfont  icon-qingchu " style="font-size: 15px;" @click="deleteImg(index)"></div>
            </template>
          </van-badge>
        </div>
      </div>
    </van-popup>
    <van-image-preview v-model="showBigImage" :images="images" :start-position="imgIndex" @change="onChange">
      <template v-slot:index>第{{ imgIndex+1 }}页</template>
    </van-image-preview>
  </div>
</template>


<script>
 import * as baseApi from "@/api/base-api";
 import HmPopup from "@/components/HmPopup.vue";
 //引入想要使用的图片
  import uploadIcon from '@/assets/imgs/upload-icon.png'
  export default {
    data() {
      return {
     scanList: [],
     testList: [],
     baleList: {},
     packList: [],
     uploaderList: [],
     docList: [],
     imgList: [],
     showBigImage: false,
     imgIndex: 0,
     images: [],
     showImgList: false,
     locationId: "",
     uploadIcon: require('@/assets/imgs/upload-icon.png'),
    // uploadIcon: uploadIcon,
     show: false,
     radio: "00",
     imgRadio: "00",
     showPop: false,
     packId: "",
     warehouseName: "",
     locationName: "",
     isDisable: false,
     activeIcon: "icon-31xuanzhong activeColor",
     inactiveIcon: "icon-weixuanzhong",
     isShowBottom: true, //显示或者隐藏footer
     documentHeight: document.documentElement.clientHeight,
      };
    },
    components: {
      HmPopup,
    },
    created() {
      this.warehouseName = localStorage.getItem("warehouseName");
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      showtest() {
        console.log("点击了关闭弹窗");
        this.showPop = !this.showPop;
      },
      onChange(index) {
        this.imgIndex = index;
      },
      showImg(item, index) {
        this.showImgList = true;
        this.imgList = item.pictureDetail;
        this.checkIndex = index;
      },
      seeBigImg(item, index) {
        this.imgIndex = index;
        var prodData = this.imgList.map(function(item) {
          return item['docUrl'];
        });
        this.showBigImage = true;
        this.images = prodData;
      },
      async uploadImage(val) {
        const params = {
          serviceId: "S_UC_PR_230808",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          pictureList: val,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              this.$toast(res.data.__sys__.msg);
              this.docList = [...res.data.detail, ...this.docList];
              this.uploaderList = [];
              this.getScanInfo();
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      deleteImg(val) {
        this.$delete(this.packList[this.checkIndex].pictureDetail, val);
      },
      getCheckInfo(val) {
        console.log("接收到了传过来的值", val);
		this.showPop = !this.showPop;
        this.scanList = val;
        this.baleList = {
          ...val[0],
        };
        this.packId = this.baleList.packId;
        this.locationId = this.baleList.locationId;
        this.locationName = this.baleList.locationName;
        this.getScanInfo();
      },
      photoAfterRead(file) {
        console.log(file);
      },
     onOversize(file) {
       console.log(file);
       this.$toast("文件大小不能超过5MB");
     },
     onConfirm() {
       if (this.scanList.length > 0) {
         if (this.uploaderList.length > 0) {
           let base64Img = this.uploaderList.map(f => f.content);
           this.uploadImage(base64Img);
         } else {
           this.getScanInfo();
         }
       } else {
         this.$toast("未查到相应捆包信息!");
       }
     },
      getScanInfo() {
        let flag = this.packList.some((item) => {
          return item.packId === this.scanList[0].packId;
        });
        if (flag) {
          this.$toast("此捆包号已添加，请重新扫描其他捆包号");
          this.clearList();
        } else {
          this.scanList[0].pictureDetail = this.docList;
          this.packList = [...this.scanList, ...this.packList];
          this.clearList();
        }
      },
      clearList() {
        this.baleList = {};
        this.scanList = [];
        this.testList = [];
        this.packId = "";
        this.locationId = "";
        this.locationName = "";
        this.uploaderList = [];
        this.docList = [];
      },
      onClickLeft() {
        this.$router.goBack();
      },
      showScan() {
        this.show = true;
      },
      async getBaleByPackId() {
        const params = {
        serviceId: "S_UC_PR_230627",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        packId: this.packId,
        warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status == 1) {
                  this.scanList.unshift(dataObj.result);
                  this.baleList = dataObj.result;
                  this.locationId = dataObj.result.locationId;
                  this.locationName = dataObj.result.locationName;

                  if (this.imgRadio === "00") {
                    this.getScanInfo();
                  }
                 //this.getScanInfo();
                  }
              } else {
                 this.scanList = [];
                this.$toast(res.data.__sys__.msg);
              }
          })
          .catch(err => {
            console.log(err);
          });
      },
      async generateInventoryLst() {
        const params = {
          serviceId: "S_UC_PR_230801",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          loadingType: this.radio,
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          rowList: this.packList,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj) {
                if (res.data.__sys__.status != -1) {
                  this.$toast(res.data.__sys__.msg);
                  this.packList = [];
                  this.scanList = [];
                  this.locationId = "";
                  this.packId = "";
                } else {
                  this.$toast(res.data.__sys__.msg);
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      deleteListItem(index) {
        //删除数组中值
        this.$delete(this.packList, index);
      },
    },
  };
</script>

<style lang="less" scoped>
  .div-location {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .search-list {
    width: 98%;
    height: 200px;
    //margin-top: 6px;
    // margin: 8px;
    border: 1px solid #dcdcdc;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .activeColor {
    color: #007aff;
  }

  .van-btn-height {
    height: 100%;
  }
  .img-list {
    width: 98%;
    display: flex;
    flex-wrap: wrap;
  }

  .div-item {
    padding: 8px 5px 5px;
  }
</style>
