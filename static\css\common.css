/* 公用样式 */
body {
	margin: 0;
	padding: 0;
     /* background-color: rgba(243, 243, 243, 1);  */
}

/* 头部导航样式 */
.global-bar-nav {
	padding: 0;
	margin: 0;
	height: auto;
	width: 100%;
	background-color: #F3F3F3;
	display: flex;
	flex-direction: column;
	position: sticky;
	top: 0;
	z-index: 999;
}

.global-bar-nav1 {
	width: 100%;
	height: 42px;
	display: flex;
	justify-content: center;
	align-items: center;
}


/* 头部字体样式 */
.global-hfont {
	height: 26px;
	font-size: 18px;
	/* font-family: Noto Sans SC; */
	font-weight: 400;
	color: #FFFFFF;
	line-height: 25px;
}

/* 背景颜色 */
.global-bg-color {
	background-color: #007AFF;
}

/* 顶部左箭头样式 */
.global_left_arrow {
	position: absolute;
	left: 16px;
	width: 9px;
	height: 9px;
	border-top: 2px solid #FFFFFF;
	border-left: 2px solid #FFFFFF;
	transform: rotate(-45deg);
}
.global_right_icon {
	position: absolute;
	right: 16px;
  }
/* 右箭头样式 */

.global_right_arrow {
	position: absolute;
	left: 16px;
	width: 9px;
	height: 9px;
	border-top: 2px solid #FFFFFF;
	border-right: 2px solid #FFFFFF;
	transform: rotate(45deg);
}
.right_arrow{
    position: absolute;
	right: 16px;
    width: 9px;
	height: 9px;
	border-top: 2px solid #9F9F9F;
	border-right: 2px solid #9F9F9F;
	transform: rotate(45deg);
}
/* 去除按钮默认样式 */
button {
	border: none;
	outline: none;
	resize: none;
}

/* 底部按钮 */
.global-btn {
	width: 100%;
	height: 48px;
	background: #007AFF;
	border-radius: 4px 4px 4px 4px;
	opacity: 1;
	margin-bottom: 28px;
	font-family: Noto Sans SC;
	font-weight: 500;
	color: #FFFFFF;
	font-size: 16px;
	line-height: 22px;
}

.global-input-row {
	overflow: hidden;
	position: fixed;
	bottom: 0;
	width: 90%;
	margin: 0 5%;

}
