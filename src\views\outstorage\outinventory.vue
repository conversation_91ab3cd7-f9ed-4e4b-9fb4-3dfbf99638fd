<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">出库清单</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="in-content">
      <van-search
        v-model="value"
        shape="round"
        placeholder="车牌号"
        @search="onFocusInput"
        background="#007aff"
        left-icon=""
      >
        <template #right-icon>
          <div
            class="iconfont icon-sousuo"
            @click="onFocusInput"
            style="color: #999999"
          ></div>
        </template>
      </van-search>
      <div class="inventory-content">
        <div
          v-if="
            loadList !== undefined && loadList != null && loadList.length > 0
          "
        >
          <div class="load-content" v-for="item in 4" :key="item">
            <div class="load-cell">
              <div>
                <div class="load-number">PDA000000000</div>
                <div class="load-name">沪ATF0813</div>
                <div class="load-name">上海宝钢新事业发展总公司</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="mui-input-row"
      style="margin: 0"
      @click="goOutStorage"
      v-show="isShowBottom"
    >
      <button id="block_button" type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;定
      </button>
    </div>
    <!-- <div class="mui-input-row" style="margin: 0" >
      <button class="confirm-btn">确定</button>
    </div> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: "",
      loadList: [],
      previousRouterName: "",
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name === "selectOption") {
        vm.loadList = from.params.loadList;
      }
    });
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    onFocusInput() {
      this.$router.togo("/selectOption");
    },
    goOutStorage() {
      this.$router.togo("/outstorage");
    },
  },
};
</script>

<style lang="less" scoped>
.global-search {
  width: 100%;
  // height: 54px;
  display: flex;
  padding: 5px 8px 5px 0;
  background-color: #fff;
  border: 1px solid #dcdcdc;
}
.inventory-content {
  width: 100%;
  height: 240px;
  margin-top: 6px;
  // margin: 8px;
  border: 1px solid #dcdcdc;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}
.load-cell {
  background-color: #fff;
  padding: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  // margin-top: 8px;
}
</style>