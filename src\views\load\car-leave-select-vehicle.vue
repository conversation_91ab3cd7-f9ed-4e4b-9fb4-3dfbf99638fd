<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择车牌号</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>

    <van-field v-model="vehicleNo" center label="车牌号" @keyup.enter.native="getVehicleList" placeholder="请输入车牌号"
      class="all-font-size">
      <template #button>
        <van-button size="small" type="info" @click="confirmVehicle" class="all-font-size">确认</van-button>
      </template>
    </van-field>

    <!-- <van-field
      v-model="remark"
      rows="1"
      autosize
      class="all-font-size"
      label="备注"
      type="textarea"
      maxlength="100"
      placeholder="请输入备注信息"
      show-word-limit
    /> -->

    <van-cell title="手选车牌" @click="showChooseCar = true" class="all-font-size">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>

    <van-popup v-model="showChooseCar" position="bottom" :style="{ height: '70%' }">
      <chooseCarNumber @chooseCarGo="chooseCarGo"></chooseCarNumber>
    </van-popup>

    <div class="load-content">
      <div v-if="vehicleList && vehicleList.length > 0">
        <van-cell-group>
          <van-cell v-for="(item, index) in vehicleList" :key="index" clickable @click="selectVehicle(item.vehicleNo)">
            <template #title>
              <div class="load-number">{{ item.vehicleNo }}</div>
            </template>
            <template #right-icon>
              <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div v-else>
        <div class="search-content">
          <div class="search-history">
            <div class="all-font-size">
              历史记录
            </div>
            <div @click="deleteItem">
              <span class="iconfont icon-qingchu"></span>
            </div>
          </div>
          <div class="list-content">
            <div class="content-item" v-for="(item, index) in searchHistoryList" :key="index" @click="onClickItem(item)">
              {{ item }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import chooseCarNumber from "@/components/carNumber.vue";
import { isEmpty } from "@/utils/tools.js";

export default {
  components: {
    chooseCarNumber,
  },
  data() {
    return {
      vehicleNo: "",
      // remark: "",
      vehicleList: [],
      searchHistoryList: [],
      showChooseCar: false,
    };
  },
  created() {
    // 获取历史记录
    const historyList = localStorage.getItem("carLeaveHistoryList");
    if (historyList) {
      this.searchHistoryList = JSON.parse(historyList);
    }
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },

    confirmVehicle() {
      if (this.isVehicleNumber(this.vehicleNo)) {
        if (!this.searchHistoryList.includes(this.vehicleNo)) {
          this.searchHistoryList.unshift(this.vehicleNo);
          localStorage.setItem("carLeaveHistoryList", JSON.stringify(this.searchHistoryList));
        } else {
          let i = this.searchHistoryList.indexOf(this.vehicleNo);
          this.searchHistoryList.splice(i, 1);
          this.searchHistoryList.unshift(this.vehicleNo);
        }
        this.goToScanLading();
      } else {
        this.$toast("请输入正确车牌号");
      }
    },

    selectVehicle(vehicleNo) {
      this.vehicleNo = vehicleNo;
      this.goToScanLading();
    },

    goToScanLading() {
      this.$router.togo({
        name: "carLeaveScanLading",
        params: {
          vehicleNo: this.vehicleNo,
          // remark: this.remark,
        },
      });
    },

    chooseCarGo(carNumber) {
      this.vehicleNo = carNumber;
      this.showChooseCar = false;
    },

    deleteItem() {
      localStorage.removeItem("carLeaveHistoryList");
      this.searchHistoryList = [];
    },

    onClickItem(item) {
      this.vehicleNo = item;
    },

    isVehicleNumber(vehicleNumber) {
      var xxreg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/;
      var creg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
      if (vehicleNumber.length == 7) {
        return creg.test(vehicleNumber);
      } else if (vehicleNumber.length == 8) {
        return xxreg.test(vehicleNumber);
      } else {
        return false;
      }
    },

    async getVehicleList() {
      // 模拟接口调用
      const params = {
        serviceId: "S_UC_PR_200101",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        vehicleNo: this.vehicleNo,
      };

      // 模拟数据，实际应该调用真实API
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              if (dataObj.result.length > 0) {
                this.vehicleList = dataObj.result;
              } else {
                this.$toast("未查询到车牌号列表！");
              }
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });

      // // 模拟数据
      // setTimeout(() => {
      //   this.vehicleList = [
      //     { vehicleNo: "苏A12345" },
      //     { vehicleNo: "苏B67890" },
      //     { vehicleNo: "苏C11111" },
      //   ].filter(item => item.vehicleNo.includes(this.vehicleNo));
      // }, 500);
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  padding-bottom: 120px;
}

.search-content {
  padding: 15px;

  .search-history {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #DCDCDC;
  }

  .list-content {
    display: flex;
    flex-wrap: wrap;
    padding-top: 10px;

    .content-item {
      background-color: #DCDCDC;
      border-radius: 20px;
      padding: 5px 10px 5px 10px;
      margin-left: 5px;
      margin-top: 5px;
      font-size: 15px;
    }
  }
}

.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
</style>