<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">提单计划列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <van-search v-model="ladingBillId" shape="round" placeholder="提单号" left-icon="" :clearable="true"
        @search="getVehicleList">
        <template #right-icon>
          <div class="iconfont icon-sousuo" style="color: #999999" @click="getVehicleList"></div>
        </template>
      </van-search>

    </van-sticky>
    <div v-if="result && result.length > 0" style="padding-bottom: 100px;">
        <div class="baletext2" style="margin-left: 10px;background-color: #fff   ;">
          已扫物流计划号合计 :
          <span class="span-count">{{ result.length }}</span>
        </div>
        <van-swipe-cell v-for="(item, index) in result" :key="index">
          <van-cell>
            <template #title>
              <div class="load-number">{{ item }}</div>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" text="删除" class="delete-bale-btn" @click="deleteItem(index)" />
          </template>
        </van-swipe-cell>
      </div>
    <van-dialog v-model="show2" title="扫描提单号" :beforeClose="beforeClose"  :width="340">
      <van-search v-model="ladingBillId" shape="round" placeholder="提单号" @search="getVehicleList" left-icon=""
        :clearable="true">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
        </template>
      </van-search>
      <div v-if="vehicleList !== undefined && vehicleList != null && vehicleList.length > 0">

        <div class="search-list home">
          <van-checkbox-group v-model="result" ref="checkboxGroup">
            <van-cell-group class="cell-group" v-for="(item, index) in vehicleList" clickable :key="index">
              <van-cell @click="toggle(item.logisticPlanId)">
                <template #title>
                  <div class="title-name">提单号: {{item.ladingBillId}}</div>
                </template>
                <template #right-icon>
                  <van-checkbox :name="item.logisticPlanId" ref="checkboxes">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-checkbox>
                </template>
              </van-cell>
              <van-cell title="物流计划号: " :value="item.logisticPlanId" />
              <van-cell title="客户名称: " :value="item.settleUserName" />
              <van-cell title="详情" @click="goItem(item.logisticPlanId)">
                <template #right-icon>
                  <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>

        </div>
      </div>
      <div v-else>
        <van-empty description="暂无物流计划列表" />
      </div>

      </van-dialog>


    <div class="mui-input-row" style="margin: 0">
      <button type="button" class="mui-btn" v-preventReClick="3000" @click="addBack">
        添&nbsp; &nbsp; &nbsp;&nbsp;加
      </button>
    </div>
    <van-popup v-model="show" position="bottom" :style="{ height: '50%' }">
      <div v-if="vehicleListItem !== undefined && vehicleListItem != null && vehicleListItem.length > 0">
        <div class="in-content home">
        <!--  <van-cell-group :title="logisticPlanId">
          </van-cell-group> -->
          <van-cell-group v-for="(item,index) in vehicleListItem" :key="index" class="cell-group">
            <van-cell title="捆包号: " :value="item.packId" />
            <van-cell title="规格: " :value="item.specsDesc" />
            <van-cell title="毛重: " :value="item.grossWeight" />
            <van-cell title="净重: " :value="item.netWeight" />
            <van-cell title="件数: " :value="item.pieceNum" />
          </van-cell-group>
        </div>
      </div>
      <div v-else>
        <van-empty description="暂无捆包列表" />
      </div>
    </van-popup>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        ladingBillId: "",
        show: false,
        show2: false,
        result: [],
        vehicleList: [],
        vehicleListItem: [],
        rowList: [],
        currentIndex: -1,
        value: "",
        allocateVehicleNo: "",
        uuid: "",
        vehicleNo: "",
        check: false,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    created() {
      this.allocateVehicleNo = this.$route.params.allocateVehicleNo;
      // this.getVehicleList();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },

    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      deleteItem(index) {
        //删除数组中值
        this.$delete(this.result, index);
      },
      checkAll() {
        this.$refs.checkboxGroup.toggleAll(true);
      },
      async beforeClose(action, done) {
        if (action === "confirm") {
          // 判断验证码是否正确
          done();
        } else {
          done();
        }
      },
      //查询物流计划列表
      async getVehicleList() {
        const params = {
          serviceId: "S_UC_PR_210109",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          ladingBillId: this.ladingBillId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
                if (this.vehicleList.length == 1) {
                  this.toggle(this.vehicleList[0].logisticPlanId);
                }
                 if (this.vehicleList.length > 1) {
                  this.show2 = true;

                }

              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询配车单捆包
      async getVehicleListItem(val) {
        this.vehicleListItem = [];
        const params = {
          serviceId: "S_UC_PR_210110",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          logisticPlanId: val,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleListItem = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      goItem(val) {
        this.show = true;
        this.getVehicleListItem(val);
        // this.$router.togo({
        //   name: "vehicleAddItem",
        //   params: {
        //     logisticPlanId: val,
        //   },
        // });
      },
      async addBack() {
        const params = {
          serviceId: "S_UC_PR_210111",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem('factoryName'),
          allocateVehicleNo: this.allocateVehicleNo,
          logisticPlanIdList: this.result,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (window.plus) {
                  plus.nativeUI.confirm(`${dataObj.__sys__.msg} 回到配车单列表`, function(e) {
                    if (e.index == 0) {
                      this.$router.goBack();
                    }
                  }, "提示", ["确认"]);
                } else {
                  Dialog.alert({
                    title: '提示',
                    message: `${dataObj.__sys__.msg} 回到配车单列表`,
                  }).then(() => {
                    this.$router.goBack();
                    // on close
                  });
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
        //  this.$router.goBack();
      },
      toggle(val) {
        const index = this.result.indexOf(val);
        if (index > -1) {
          // 如果已存在，移除
          this.result.splice(index, 1);
        } else {
          // 如果不存在，添加
          this.result.push(val);
        }
      },

    },
  };
</script>

<style lang="less" scoped>
  .home {
    background-color: rgba(243, 243, 243, 1);
  }

  .title-name {
    color: #007aff;
  }

  .activeColor {
    color: #007aff;
  }

  .cell-group {
    margin-bottom: 10px;
  }

  .span-count {
    margin-left: 10px;
    color: #0000ff;
  }

  .load-number {
    font-size: 16px;
    color: #0000ff;
  }
  .search-list {
    height: 220px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
    overflow-y: auto;

    // margin-bottom: 80px;
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

</style>
