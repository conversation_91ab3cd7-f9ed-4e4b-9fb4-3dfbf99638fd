<template>
  <div id="sign_box">
    <!-- <van-nav-bar :title="title" :left-arrow="false" :fixed="true" :placeholder="true"
      class="nav-bar" /> -->
    <div class="canvans-title">
      {{title}}
    </div>
    <div class="canvaspanel">
      <div class="canvasborder">
        <vue-esign ref="esign" :width="d_w" :height="d_h" :isCrop="isCrop" :lineWidth="lineWidth" :lineColor="lineColor"
          :bgColor.sync="bgColor" />
      </div>

      <div class="buttongroup">
        <van-button type="info" plain size="small" @click="closeDialog" class="empty">取消</van-button>
        <van-button type="danger" size="small" @click="handleReset" class="autograph">清空</van-button>
        <van-button type="info" size="small" @click="preview" class="autograph">预览</van-button>
        <van-button type="primary" size="small" @click="handleGenerate" class="autograph">确定</van-button>
      </div>
    </div>
    <img :src="resultImg" alt="" v-show="false">
  </div>
</template>
<script>
  import {
    Toast,
    Dialog,
    ImagePreview
  } from 'vant'
  import vueEsign from 'vue-esign'
  import {
    rotateImg
  } from '@/utils/imgBase64';
  export default {
    name: 'esign',
    components: {
      vueEsign
    },
    props: {
      d_h: {
        type: [String, Number]
      },
      d_w: {
        type: [String, Number]
      },
      title: {
        type: [String, Number]
      }
    },
    data() {
      return {
        lineWidth: 4,
        lineColor: '#000000',
        bgColor: '#FFF',
        resultImg: '',
        isCrop: false
      }
    },
    methods: {
      // 清空画板
      handleReset() {
        this.$refs.esign.reset();
        this.bgColor = '#FFF';
      },
      preview() {
        this.$refs.esign.generate()
          .then((res) => {
            this.resultImg = res; // 得到了签字生成的base64图片
            ImagePreview([res]);
          })
          .catch(() => {
            // 没有签名，点击生成图片时调用
            this.$toast("未签名!");
          });
      },
      // rotateImg(src, rotate) {
      //   return new Promise(((resolve, reject) => {

      //     let img = new Image()
      //     img.src = src
      //     img.setAttribute('crossOrigin', 'Anonymous')
      //     img.onload = () => {
      //       let canvas = document.createElement('canvas')
      //       let context = canvas.getContext('2d')
      //       if (rotate > 45 && rotate <= 135) { // 90 宽高颠倒
      //         canvas.width = img.height
      //         canvas.height = img.width
      //       } else if (rotate > 225 && rotate <= 315) { // 270 宽高颠倒
      //         canvas.width = img.height
      //         canvas.height = img.width
      //       } else {
      //         canvas.width = img.width
      //         canvas.height = img.height
      //       }
      //       context.clearRect(0, 0, canvas.width, canvas.height)
      //       context.translate(canvas.width / 2, canvas.height / 2)
      //       context.rotate(rotate * Math.PI / 180)
      //       context.translate(-canvas.width / 2, -canvas.height / 2)
      //       context.drawImage(img, canvas.width / 2 - img.width / 2, canvas.height / 2 - img.height / 2, img.width, img.height)
      //       context.translate(canvas.width / 2, canvas.height / 2)
      //       context.rotate(-rotate * Math.PI / 180)
      //       context.translate(-canvas.width / 2, -canvas.height / 2)
      //       context.restore()
      //       let base64 = canvas.toDataURL()
      //       resolve(base64)
      //     }
      //     img.onerror = reject
      //   }))

      // },
      // 写完提交
      handleGenerate() {
        this.$refs.esign.generate().then(res => {
          this.resultImg = res
          if (this.resultImg) {
            this.to_fater()
          } else {
            Toast.fail('请签名')
          }
        }).catch((e) => {
          console.log(e);
          Toast.fail('请签名')
        })
      },
      to_fater() {
        let that = this;
        if (window.plus) {
          plus.nativeUI.confirm("您是否确定上传签名？", function(e) {
            if (e.index == 0) {
             // let p1 =  rotateImg(that.resultImg,-90);
             //  p1.then(value => {
             //   that.$emit('close',value)
             //  });
              that.$emit('close', that.resultImg)
            } else {
              console.log("You clicked Cancel!");
            }
          }, "提示", ["确认", "取消"]);
        } else {
          Dialog.confirm({
              title: '提示',
              message: '您是否确定上传签名'
            })
            .then(() => {
              // on confirm
              // let p1 =  rotateImg(that.resultImg,-90);
              //  p1.then(value => {
              //   that.$emit('close',value)
              //  });
              that.$emit('close', that.resultImg)
            })
            .catch(() => {
              // on cancel
            })
        }

      },
      // 关闭悬浮框
      closeDialog() {
        this.resultImg = ''
        this.$emit('close', this.resultImg)
      }
    }
  }
</script>
<style lang="less" scope>
  #sign_box {

    //横向操作，由于微信不自动横屏，所以手动横屏
    .nav-bar.van-nav-bar__placeholder {
      width: 100%;
      height: 46px !important;
      transform: rotate(90deg);
      position: fixed;
      top: 10%;
      right: -100%;
      transform-origin: 0% 0%;
      z-index: 10;
    }

    .van-nav-bar {
      background-color: #fff;
    }
  }

  .canvans-title {
    width: 100%;
    height: 46px !important;
    transform: rotate(90deg);
    position: fixed;
    top: 30%;
    right: -98%;
    transform-origin: 0% 0%;
    z-index: 10;
    font-size: 16px;
    font-weight: 600px;
  }

  .canvasborder {
    border: solid 1px #ccc;
  }

  .canvaspanel {
    position: relative;
  }

  .buttongroup {
    font-size: 0.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: rotate(90deg);
    height: 40px;
    z-index: 10;
    left: -30px;
    position: fixed;
    bottom: 100px;

    .autograph {
      margin-left: 15px;
    }
  }

  .autograph {
    margin-left: 20px;
  }
</style>
