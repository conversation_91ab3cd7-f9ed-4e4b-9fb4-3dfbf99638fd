<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择车牌号</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <!-- <van-search v-model="carNumber" shape="round" background="#007aff" placeholder="车牌号" @search="getVehicleList"
        left-icon="" :clearable="false">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
        </template>
      </van-search> -->
    </van-sticky>
    <!--  -->
    <van-cell title="跳过" @click="noCar" class="all-font-size"  v-if="isVehicleRequired == 0">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>
    <van-field v-model="testCar" center label="车牌号" @keyup.enter.native="getVehicleList" placeholder="请输入车牌号"
      class="all-font-size">
      <template #button>
        <van-button size="small" type="info" @click="noCarGo" class="all-font-size">确认</van-button>
      </template>
    </van-field>
    <van-field
      v-model="remark"
      rows="1"
      autosize
       class="all-font-size"
      label="备注"
      type="textarea"
      maxlength="100"
      placeholder="请输入备注信息"
      show-word-limit
    />
    <van-cell title="手选车牌" @click="showChooseCar = true" class="all-font-size">
      <template #right-icon>
        <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
      </template>
    </van-cell>
    <van-popup v-model="showChooseCar" position="bottom" :style="{ height: '70%' }">
      <chooseCarNumber @chooseCarGo="chooseCarGo"></chooseCarNumber>
    </van-popup>
    <div class="load-content">
      <div v-if="vehicleList && vehicleList.length > 0">
        <van-cell-group>
          <van-cell v-for="(item, index) in vehicleList" :key="index" clickable
            @click="isChecked(index, item.vehicleNo)">
            <template #title>
              <div class="load-number">{{ item.vehicleNo }}</div>
            </template>
            <template #right-icon>
              <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div v-else>
        <div class="search-content">
          <div class="search-history">
            <div class="all-font-size">
              历史记录
            </div>
            <div @click="deleteItem">
              <span class="iconfont icon-qingchu"></span>
            </div>
          </div>
          <div class="list-content">
            <div class="content-item" v-for="(item,index) in searchHistoryList" :key="index" @click="onClickItem(item)">
              {{item}}
            </div>
          </div>
        </div>
        <!--  <van-empty description="暂未查到车牌号列表" /> -->
      </div>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  import chooseCarNumber from "@/components/carNumber.vue";
  export default {
    data() {
      return {
        remark:'',//备注信息
        isVehicleRequired : 0, //0
        showChooseCar: false,
        testCar: "",
        vehicleList: [],
        currentIndex: -1,
        searchHistoryList: [],
        value: "",
        check: false,
        carNumber: "",
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    components: {
      chooseCarNumber,
    },
    created() {
      this.searchIsVehicleRequired();
      // this.getVehicleList();
      let list = localStorage.getItem("vehicleInHistoryList");
      if (list != null) {
        this.searchHistoryList = JSON.parse(list);
      } else {
        this.searchHistoryList = [];
      }
      sessionStorage.clear();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
        // this.$router.replace('/');
      },
      noCarGo() {
        if (this.isVehicleNumber(this.testCar)) {
          if (!this.searchHistoryList.includes(this.testCar)) {
            this.searchHistoryList.unshift(this.testCar);
            localStorage.setItem("vehicleInHistoryList", JSON.stringify(this.searchHistoryList))
          } else {
            //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
            let i = this.searchHistoryList.indexOf(this.testCar);
            this.searchHistoryList.splice(i, 1);
            this.searchHistoryList.unshift(this.testCar);
          }
         this.$router.push({ path: '/inlist', query: { carNumber: this.testCar, remark: this.remark } });

        //  this.$router.togo("/inlist?carNumber=" + this.testCar + '&remark='+this.remark);
        } else {
          this.$toast("请输入正确车牌号");
        }
      },
      chooseCarGo(val) {
        this.showChooseCar = false;
        console.log("-----val", val);
        if (this.isVehicleNumber(val)) {
          if (!this.searchHistoryList.includes(val)) {
            this.searchHistoryList.unshift(val);
            localStorage.setItem("vehicleInHistoryList", JSON.stringify(this.searchHistoryList))
          } else {
            //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
            let i = this.searchHistoryList.indexOf(val);
            this.searchHistoryList.splice(i, 1);
            this.searchHistoryList.unshift(val);
          }
          this.$router.push({ path: '/inlist', query: { carNumber:val, remark: this.remark } });
         // this.$router.togo("/inlist?carNumber=" + val);
        } else {
          this.$toast("请输入正确车牌号");
        }
      },
      noCar() {
         this.$router.push({ path: '/inlist', query: { carNumber:this.testCar, remark: this.remark } });
        //this.$router.togo("/inlist?carNumber=" + this.testCar);
      },
      //清空搜索历史
      deleteItem() {
        localStorage.removeItem("vehicleInHistoryList")
        this.searchHistoryList = [];
      },
      onClickItem(item) {
        this.testCar = item;
      },
      isVehicleNumber(vehicleNumber) {
        var xxreg =
          /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/; // 2021年新能源车牌不止有DF
        var creg =
          /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
        if (vehicleNumber.length == 7) {
          return creg.test(vehicleNumber);
        } else if (vehicleNumber.length == 8) {
          return xxreg.test(vehicleNumber);
        } else {
          return false;
        }
      },
      isChecked(index, item) {
        if (!this.searchHistoryList.includes(item)) {
          this.searchHistoryList.unshift(item);
          localStorage.setItem("vehicleInHistoryList", JSON.stringify(this.searchHistoryList))
        } else {
          //有搜索记录，删除之前的旧记录，将新搜索值重新push到数组首位
          let i = this.searchHistoryList.indexOf(item);
          this.searchHistoryList.splice(i, 1);
          this.searchHistoryList.unshift(item);
        }
        this.$router.togo("/inlist?carNumber=" + item);
      },
      //查询待入库车牌号列表
      async getVehicleList() {
        const params = {
          serviceId: "S_UC_PR_200101",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          vehicleNo: this.testCar,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result.length > 0) {
                  this.vehicleList = dataObj.result;
                } else {
                  this.$toast("未查询到车牌号列表！");
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询是否显示跳过
      async searchIsVehicleRequired() {
        const params = {
          serviceId: "S_UC_PR_200108",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.isVehicleRequired = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onConfirm() {
        if (
          this.carNumber == "" ||
          this.carNumber == null ||
          this.carNumber == undefined
        ) {
          this.$toast("请选择车辆！");
        } else {
          this.$router.togo("/inlist?carNumber=" + this.carNumber);
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .load-content {
    padding-bottom: 120px;
  }

  .activeColor {
    color: #007aff;
  }

  .search-content {
    padding: 15px;


    .search-history {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #DCDCDC;
    }

    .list-content {
      display: flex;
      flex-wrap: wrap;
      padding-top: 10px;

      .content-item {
        background-color: #DCDCDC;
        border-radius: 20px;
        padding: 5px 10px 5px 10px;
        margin-left: 5px;
        margin-top: 5px;
        font-size: 15px;
      }
    }
  }

  .load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
  }

  .load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
  }
</style>
