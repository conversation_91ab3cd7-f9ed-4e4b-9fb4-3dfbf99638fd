<template>
  <div>
    <van-nav-bar>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
      <template #title>
        <div class="global-hfont">精细化管理</div>
      </template>
      <template #right>
        <div class="" @click="goChooseLoad">
          <span class="iconfont icon-a-18zhuangxiehuodidian"></span>
        </div>
      </template>
    </van-nav-bar>
    <div class="load-content">
      <button class="mui-btn" @click="goFactoryLoad">
        <div class="search icon"></div>
        <span class="load-span">厂内装卸点管理</span>
      </button>
      <button class="mui-btn" @click="goCarIn">
        <div class="search icon"></div>
        <span class="load-span">车辆入厂管理</span>
      </button>

      <button class="mui-btn" @click="goCarLoad">
        <div class="search icon"></div>
        <span class="load-span">车辆装卸货管理</span>
      </button>
      <button class="mui-btn" @click="goVehicleAllocationtList">
         <div class="search icon"></div>
         <span class="load-span">配车单管理</span>
       </button>
     <!-- <button class="mui-btn" @click="goBaleLeave">
        <div class="search icon"></div>
        <span class="load-span">捆包出厂管理</span>
      </button> -->
      <button class="mui-btn" @click="goSignature">
         <div class="search icon"></div>
         <span class="load-span">补充签字管理</span>
       </button>
       <button class="mui-btn" @click="goFactoryConfirm">
          <div class="search icon"></div>
          <span class="load-span">出厂确认管理</span>
        </button>
     <button class="mui-btn" @click="goLadingInverted">
        <div class="search icon"></div>
        <span class="load-span">提单找货管理</span>
      </button>
      <button class="mui-btn" @click="goCarLeave">
        <div class="search icon"></div>
        <span class="load-span">车辆出厂管理</span>
      </button>
      <button class="mui-btn" @click="goGateGuardFactory">
        <div class="search icon"></div>
        <span class="load-span">门卫出厂确认</span>
      </button>
      <button class="mui-btn" @click="goPictureUpload">
        <div class="search icon"></div>
        <span class="load-span">捆包图片上传</span>
      </button>
      <button class="mui-btn" @click="goCallNumber">
        <div class="search icon"></div>
        <span class="load-span">排队信息管理</span>
      </button>
      <button class="mui-btn" @click="goWasteMaterial" v-if="isShowWasteMaterial">
        <div class="search icon"></div>
        <span class="load-span">废次材</span>
      </button>
      <!-- <button class="mui-btn" @click="goOperatorVehicles">
        <div class="search icon"></div>
        <span class="load-span">作业人员车辆</span>
      </button> -->
    </div>
  </div>
</template>

<script>
import { Dialog } from "vant";
export default {
  data() {
    return {
      // 长春宝钢增加废次材功能
      isShowWasteMaterial: localStorage.getItem("segNo") == "JN000000",
      // isShowWasteMaterial: true,
    };
  },
  created() {
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "vehicleAllocationtList") {
      from.meta.keepAlive = false;
    }
    next();
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goChooseLoad() {
      this.$router.togo("/choose");
    },
    goFactoryLoad() {
      this.$router.togo("/factoryLoad");
    },
    goCarLoad() {
      this.$router.togo("/carLoad");
    },
    goCarLeave() {
      // 襄阳宝钢增加车辆出厂功能
      if (localStorage.getItem("segNo") == "JJ000000") {
        this.$router.togo("/carLeaveSelectVehicle");
      } else {
        this.$router.togo("/leaveFactory");
      }
    },
    goCarIn() {
      this.$router.togo("/inFactory");
    },
    goLadingInverted(){
      this.$router.togo("/ladingList");
    },
    goFactoryConfirm() {
      this.$router.togo("/factoryConfirmation");
    },
    goBaleLeave() {
      this.$router.togo("/leaveBale");
    },
    goVehicleAllocationtList(){
      this.$router.togo("/vehicleAllocationtList");
    },
    goCallNumber(){
      this.$router.togo("/callNumber");
    },
    goGateGuardFactory(){
        this.$router.togo("/gateGuardFactory");
    },
    goPictureUpload(){
      this.$router.togo("/pictureUpload");
    },
    goSignature(){
       this.$router.togo("/signature");
    },
    goOperatorVehicles(){
        this.$router.togo("/operatorVehicles");
    },
    goWasteMaterial(){
        this.$router.togo("/wasteMaterial");
    }
  },
};
</script>

<style lang="less" scoped>
// .van-nav-bar {
//   background-color: #007aff;
//   width: 100%;
//   height: 42px;
//   /deep/ .van-nav-bar__title {
//     font-size: 18px;
//     font-weight: 400;
//     color: #ffffff;
//     line-height: 25px;
//   }

//   /deep/ .van-icon {
//     color: ffffff;
//   }
// }
.load-content {
  margin: 20px 20px 8px 20px;
}
.load-span {
  margin-left: 25px;
}
</style>
