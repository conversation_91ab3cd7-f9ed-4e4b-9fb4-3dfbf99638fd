<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">{{titleName}}</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="in-content">
      <div class="detail_row">
        <div class="fourtext" style="width: 20%">车牌号</div>
        <span class="iconfont icon-shuxian icon-line"></span>
        <input class="detail_input" type="text" readonly value="" v-model="vehicleNo" placeholder="请获取查询车牌号"
          style="width: 70%" />
        <span class="iconfont icon-sousuo" style="width: 10%; color: #d0d0d0" @click="onFocus"></span>
      </div>
    </div>
    <van-dialog v-model="show" :show-confirm-button="false">
      <div>
        <van-search v-model="value" shape="round" background="#007aff" placeholder="请输入车牌号" @search="getVehicleList"
          left-icon="" :clearable="false">
          <template #right-icon>
            <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
          </template>
        </van-search>
      </div>

      <div class="search-list">
        <div v-if="vehicleList && vehicleList.length > 0">
          <van-radio-group v-model="value">
            <van-cell-group>
              <van-cell v-for="(item, index) in vehicleList" :key="index" clickable @click="onCheck(index, item)">
                <template #title>
                  <div class="seg-title">{{ item }}</div>
                </template>
                <template #right-icon>
                  <van-radio :name="item">
                    <template #icon="props">
                      <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                    </template>
                  </van-radio>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>

        <div class="tips-content" v-else>
          <div class="title">提示</div>
          <div class="msg">未查询到可选择的车牌！</div>
        </div>
      </div>
      <button class="search-btn" @click="onConfrimCarNumber">确认</button>
    </van-dialog>
    <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" v-preventReClick="3000" @click="onConfirm">
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;认
      </button>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty
  } from '@/utils/tools';
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        titleName: "",
        typeCar: "",
        show: false,
        signShow: false,
        vehicleNo: "",
        value: "",
        nextWarehouseCode: "",
        nextWarehouse: "请选择下个目标",
        nextWarehouseName: "",
        nextLoadingPointNo: "",
        nextLoadingPoint: "请选择下个装卸点",
        nextLoadingPointName: "",
        warehouseOption: [],
        loadList: [],
        vehicleList: [],
        currentIndex: -1,
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
      };
    },
    created() {
      this.typeCar = this.$route.params.typeCar;
      if (this.$route.params.typeCar == 1) {
        this.titleName = "召回车辆确认"
        this.getBackVehicleList()
      }
      if (this.$route.params.typeCar == 2) {
        this.titleName = "未装离厂确认"
        this.getNeverVehicleList();
      }
      this.vehicleNo = this.$route.params.carNumber || "";
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onFocus() {
        this.show = true;
      },
      onClickLeft() {
        this.$router.goBack();
      },
      onCheck(index, item) {
        this.value = item;
        this.currentIndex = index;
        this.vehicleNo = item;
      },
      isChecked(item) {
        this.nextWarehouse = item.warehouseName;
        this.nextWarehouseName = item.warehouseName;
        this.nextWarehouseCode = item.warehouseCode;
        this.getLoadList();
      },
      isChecked2(item) {
        this.nextLoadingPoint = item.loadingPointName;
        this.nextLoadingPointName = item.loadingPointName;
        this.nextLoadingPointNo = item.loadingPointNo;
      },
      onConfrimCarNumber() {
        this.show = false;
        this.value = "";
      },
      openItem() {
        if (this.nextWarehouseCode.length == 0) {
          this.$toast("请先选择仓库");
        }
      },
      getVehicleList() {
        if (this.typeCar == 1) {
          this.getBackVehicleList()
        } else {
          this.getNeverVehicleList();
        }
      },
      //查询未装离厂列表
      async getNeverVehicleList() {
        const params = {
          serviceId: "S_UC_PR_230103",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNoName: this.value,
          loadingPointNo: "",
          type: "never",
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询车辆召回列表
      async getBackVehicleList() {
        const params = {
          serviceId: "S_UC_PR_230103",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          vehicleNoName: this.value,
          warehouseCode: localStorage.getItem("warehouseCode"),
          loadingPointNo: "",
          type: "back",
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onConfirm() {
        if (isEmpty(this.vehicleNo)) {
          this.$toast("请选择车辆!");
        } else {
          if (this.typeCar == 1) {
            this.toConfirm1()
          } else {
            this.toConfirm2();
          }
        }

      },
      // 车辆召回 装卸完成
      async toConfirm1() {
        let that = this;
        const params = {
          serviceId: "S_UC_PR_230506",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          loadingPointNo: sessionStorage.getItem("pdaLoadNo"),
          loadingPointName: sessionStorage.getItem("pdaLoadName"),
          vehicleNo: that.vehicleNo,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //this.$toast(res.data.__sys__.msg);
                if (window.plus) {
                  plus.nativeUI.confirm(`${dataObj.__sys__.msg} 回到车辆装卸货管理`, function(e) {
                    if (e.index == 0) {
                      that.$router.replace('/carLoad');
                    } else {
                      console.log("You clicked Cancel!");
                    }
                  }, "提示", ["确认", "取消"]);
                } else {
                  Dialog.alert({
                    title: '提示',
                    message: `${dataObj.__sys__.msg} 回到车辆装卸货管理`,
                  }).then(() => {
                    this.$router.replace('/carLoad');
                    // on close
                  });
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }

            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //未装离厂 装卸完成
      async toConfirm2() {
        let that = this;
        const params = {
          serviceId: "S_UC_PR_230507",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          loadingPointNo: sessionStorage.getItem("pdaLoadNo"),
          loadingPointName: sessionStorage.getItem("pdaLoadName"),
          vehicleNo: that.vehicleNo,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //this.$toast(res.data.__sys__.msg);
                if (window.plus) {
                  plus.nativeUI.confirm(`${dataObj.__sys__.msg} 回到车辆装卸货管理`, function(e) {
                    if (e.index == 0) {
                      that.$router.replace('/carLoad');
                    } else {
                      console.log("You clicked Cancel!");
                    }
                  }, "提示", ["确认", "取消"]);
                } else {
                  Dialog.alert({
                    title: '提示',
                    message: `${dataObj.__sys__.msg} 回到车辆装卸货管理`,
                  }).then(() => {
                    that.$router.replace('/carLoad');
                    // on close
                  });
                }

              } else {
                this.$toast(res.data.__sys__.msg);
              }

            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  /deep/ .van-dropdown-menu__item {
    display: flex;
    justify-content: flex-start;
    padding-left: 10px;
  }

  .factory-body {
    height: 100vh;
    background-color: #f3f3f3;
  }

  .search-list {
    height: 150px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
    overflow-y: auto;
    margin-bottom: 80px;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .tips-content {
    height: 110px;
    margin: 15px;
    background-color: #fff;
    font-size: 14px;
    padding: 10px;
    border-radius: 10px;

    div {
      margin: 10px;
    }

    .title {
      text-align: center;
    }

    .msg {
      margin-top: 10px;
    }
  }

  .search-btn {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    color: #fff;
  }

  .ware-title {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #007aff;
    line-height: 20px;
  }

  .ware-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #333333;
    line-height: 22px;
  }

  .activeColor {
    color: #007aff;
  }

  .canvans-tip {
    width: 100px;
    font-size: 15px;
    z-index: 10;
    left: 200px;
    position: fixed;
    bottom: 100px;
    transform: rotate(90deg);
  }

  .page-content {
    padding: 10px;
    background-color: #f1f1f1;

    .content {
      border: 1px solid #f1f1f1;
    }

    .sign-btn {
      margin: 10px;
      display: flex;
      justify-content: space-around;
      align-content: center;
    }
  }
</style>