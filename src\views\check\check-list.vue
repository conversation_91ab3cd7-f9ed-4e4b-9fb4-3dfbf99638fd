<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">盘库单列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <van-search v-model="inventoryNumber" shape="round" background="#007aff" placeholder="请输入或扫描盘库单号"
        @search="getCheckList" left-icon="" :clearable="true">
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="getCheckList" style="color: #999999"></div>
        </template>
      </van-search>
    </van-sticky>
    <div v-if="checkList !== undefined && checkList != null && checkList.length > 0"  style="overflow-y: auto; height: 100%; margin-bottom: 100px;">
     <van-radio-group v-model="check">
       <van-cell-group>
         <van-cell
           v-for="(item, index) in checkList"
           :key="index"
           clickable
           @click="isChecked2(index, item)"
         >
           <template #title>
             <div class="all-font-size" style="color:#007aff ;">{{item.stockCheckNum}}</div>
              <div class="" style="margin-top: 6px;  font-size: 16px;"><span style="font-weight: 400;">账套：</span>{{item.segCname}}</div>
           </template>
           <template #label>
              <div class="" style="color:#000 ; margin-top: 5px; font-size: 16px;"><span  style="font-weight: 400;">仓库：</span>{{item.warehouseName}}</div>
           </template>
           <template #right-icon>
              <van-radio :name="index"
                     ><template #icon="props">
                       <span
                         class="iconfont"
                         :class="props.checked ? activeIcon : inactiveIcon"
                       ></span>
                     </template>
                   </van-radio>
           </template>
         </van-cell>
       </van-cell-group>
     </van-radio-group>
     <!-- <van-cell-group v-for="(item,index) in checkList" :key="index">
        <van-cell @click="goCheckCell(item)">
          <template #title>
            <div class="all-font-size" style="color:#007aff ;">{{item.stockCheckNum}}</div>
          </template>
          <template #label>
            <div class="all-font-size" style="color:#000 ;">{{item.warehouseName}}</div>
          </template>
          <template #right-icon>
            <span class="iconfont icon-youjiantou" style="color: #9f9f9f"></span>
          </template>
        </van-cell>
      </van-cell-group> -->
        <div class="mui-input-row3" v-if="showFlag">
          <button id="block_button" type="button" class="mui-btn3" v-preventReClick="3000" @click="finishCheckList()" >
           盘库完成
          </button>
          <button id="block_button" type="button" class="mui-btn3" v-preventReClick="3000" @click="goCheck()">
           盘库
          </button>
        </div>
      <div class="mui-input-row" style="margin: 0" v-else>
        <button type="button" class="mui-btn" @click="goCheck()" v-preventReClick="3000">
          盘库
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无盘库单列表" />
    </div>
    <van-dialog v-model="myshow" title="捆包信息" @confirm="onConfirmItem">
      <div class="dialog-content">
        <van-radio-group v-model="radio2">
          <van-cell-group>
            <van-cell v-for="(item, index) in 3" :key="index" clickable @click="isChecked(index, item)">
              <template #title>
                <div class="ware-title">11111{{item.packId}}</div>
              </template>
              <template #label>
                <div>
                  描述</div>
              </template>
              <template #right-icon>
                <van-radio :name="index"><template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-dialog>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  import {
    isEmpty
  } from "@/utils/tools";
  export default {
    data() {
      return {
        stockCheckPositio:"",
        locationInfo: null,
        locationError: null,
        loading: false,
        baiduAK: 'kKVDQKfenOGWfL8jRBlmDHdIs3omy978', // 替换为您的百度地图A
        inventoryType :"1",
        check: -1,
        checkNum: "",
        myshow: false,
        showFlag: false,
        checkList: [],
        inventoryNumber: "",
        radio2: -1,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
      };
    },
    created() {
      this.getCheckList();
      this.isShowbtn();
      // this.loadBaiduMapScript();
    },
    methods: {
      onClickLeft() {
        this.$router.replace('/checkStorage');
      },
      // 加载百度地图脚本
       loadBaiduMapScript() {
         if (window.BMap) {
           this.initBaiduLocation();
           return;
         }

         const script = document.createElement('script');
         script.type = 'text/javascript';
         script.src = `https://api.map.baidu.com/api?v=3.0&ak=${this.baiduAK}&callback=onBMapCallback`;
         document.head.appendChild(script);

         // 全局回调函数
         window.onBMapCallback = () => {
           console.log('百度地图脚本加载完成');
           // 初始化定位
           this.initBaiduLocation();
         };
       },

       // 重试定位
       retryLocation() {
         this.locationError = null;
         this.initBaiduLocation();
       },

      // 初始化百度定位
      initBaiduLocation() {
        if (!window.BMap) {
          this.loadBaiduMapScript();
          return;
        }

        this.loading = true;

        const geolocation = new BMap.Geolocation();
        geolocation.enableSDKLocation();
        geolocation.getCurrentPosition(
          (result) => {
            this.loading = false;
            if (result && result.point) {
              // 基本位置信息
              this.locationInfo = {
                latitude: result.point.lat,
                longitude: result.point.lng,
                address: result.address
              };

              // 获取详细地址组件
              this.getAddressComponents(result.point.lat, result.point.lng);

              console.log('百度定位成功:', this.locationInfo);
              this.$toast.success('定位成功');
            } else {
                console.log('获取位置失败:');
               this.$toast('获取位置失败');
              this.locationError = '获取位置失败';
            }
          },
          (error) => {
            this.loading = false;
            console.error('百度定位失败:', error);
            this.locationError = '定位失败，请检查定位权限和网络连接';
          }
        );
      },

      // 获取地址组件
      getAddressComponents(lat, lng) {
        if (!window.BMap) {
          return;
        }

        const point = new BMap.Point(lng, lat);
        const geoc = new BMap.Geocoder();

        geoc.getLocation(point, (rs) => {
          if (rs && rs.addressComponents) {
            const components = rs.addressComponents;

            // 存储地址组件
            this.locationInfo.addressComponents = {
              province: components.province || '',
              city: components.city || '',
              district: components.district || '',
              street: components.street || '',
              streetNumber: components.streetNumber || ''
            };

            // 格式化地址
            this.locationInfo.formattedAddress = this.formatAddress(this.locationInfo.addressComponents);

            console.log('地址组件:', this.locationInfo.addressComponents);
            console.log('格式化地址:', this.locationInfo.formattedAddress);
            this.stockCheckPositio = this.locationInfo.formattedAddress;
          }
        });
      },

      // 格式化地址
      formatAddress(components) {
        if (!components) return '';

        const { province, city, district, street, streetNumber } = components;

        // 拼接地址，过滤掉空值
        return [province, city, district, street, streetNumber]
          .filter(item => item && item.trim() !== '')
          .join('');
      },

       // 获取位置
       getLocation() {
         this.loading = true;
         this.locationError = null;
         this.locationInfo = null;

         this.initBaiduLocation();
       },
      isChecked(index, item) {
        this.radio2 = index;
       // this.checkNum = item.stockCheckNum;
         this.list = [item];
      },
      isChecked2(index, item) {
        this.check = index;
        this.checkNum = item.stockCheckNum;
        this.inventoryType = item.inventoryType;
      },
      onConfirmItem() {
        this.myshow = false;
      },
      goCheckCell(item) {
      //  this.$router.togo("/scanCheck?stockCheckNum=" + item.stockCheckNum);
        this.$router.togo("/checkMap?stockCheckNum=" + item.stockCheckNum);
      },
      goCheck() {
        if(!isEmpty(this.checkNum)){
          if(this.inventoryType == 1 ){
            // this.$router.togo("/checkMap?stockCheckNum=" +  this.checkNum);
              this.$router.togo("/scanCheck?stockCheckNum=" + this.checkNum);
          }else{
             this.$router.togo("/checkMap?stockCheckNum=" +  this.checkNum);
          }
        }else{
             this.$toast("请选择盘库单！");
        }

        //this.$router.togo("/checkListDetail?stockCheckNum=" + item.stockCheckNum);
      },
      //获取盘库列表
      async getCheckList() {
        const params = {
          serviceId: "S_UC_PR_230613",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.inventoryNumber
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.checkList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //是否显示盘库完成按钮
      async isShowbtn() {
        const params = {
          serviceId: "S_UC_PR_230662",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                if (res.data.result == "1") {
                  this.showFlag = true;
                } else {
                  this.showFlag = false;
                }
                //this.checkList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      finishCheckList(){
       let that = this;
       if(!isEmpty(this.checkNum)){
         Dialog.confirm({
             title: '提示',
             message: `是否确认盘库单号${this.checkNum}盘库完成?`,
           })
           .then(() => {
             that.continueFinishCheckList();
           })
           .catch(() => {
             // on cancel
           });
       }else{
           this.$toast("请选择盘库单！");
       }

      },
      //盘库完成
      async continueFinishCheckList() {

        const params = {
          serviceId: "S_UC_PR_230663",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.checkNum,
          stockCheckPosition: this.stockCheckPositio
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.__sys__.status != -1) {
                // this.checkList = dataObj.result;
                this.$toast(res.data.__sys__.msg);
                  setTimeout(() => {
                         this.getCheckList();
                      }, 3000);

              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },

  };
</script>

<style lang="less" scoped>
  .dialog-content {
    width: 100%;
    height: 250px;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .activeColor {
    color: #007aff;
  }

  .ware-title {
    font-size: 14px;
    color: #000;
  }

  .ware-name {
    font-size: 13px;
    color: #333333;
  }
</style>
