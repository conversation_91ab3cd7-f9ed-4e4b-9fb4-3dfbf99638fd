<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">捆包图片上传</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <van-field name="radio" label="类型" class="all-font-size">
      <template #input>
        <van-radio-group v-model="radio" direction="horizontal">
          <van-radio name="20"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
            入库
          </van-radio>
          <van-radio name="21"><template #icon="props">
              <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
            </template>
            拆包
          </van-radio>
        </van-radio-group>
      </template>
    </van-field>

    <van-field v-model="packId" label="捆包" class="all-font-size" ref="labelsecond" clearable placeholder="请输入或扫描捆包号"
      :rules="[{ required: true, message: '请输入或扫描捆包号' }]" />
    <van-field name="uploader" label="图片上传" class="all-font-size">
      <template #input>
        <van-uploader v-model="uploaderList" :upload-icon="uploadIcon" @oversize="onOversize" multiple :max-count="5"
          accept="image/*" />
      </template>
    </van-field>

    <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" v-preventReClick="3000"
      @click="uploadPackIdPicture">
      <button type="button" class="mui-btn">
        上&nbsp; &nbsp; &nbsp;&nbsp;传
      </button>
    </div>
  </div>
</template>


<script>
  import * as baseApi from "@/api/base-api";
  import HmPopup from "@/components/HmPopup.vue";
  //引入想要使用的图片
  import {
    isEmpty
  } from '@/utils/tools';
  import uploadIcon from '@/assets/imgs/upload-icon.png'
  export default {
    data() {
      return {
        scanList: [],
        testList: [],
        baleList: {},
        packList: [],
        uploaderList: [],
        docList: [],
        imgList: [],
        showBigImage: false,
        imgIndex: 0,
        images: [],
        showImgList: false,
        locationId: "",
        uploadIcon: require('@/assets/imgs/upload-icon.png'),
        // uploadIcon: uploadIcon,
        show: false,
        radio: "20",
        imgRadio: "00",
        showPop: false,
        packId: "",
        warehouseName: "",
        locationName: "",
        isDisable: false,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    components: {
      HmPopup,
    },
    created() {
      this.warehouseName = localStorage.getItem("warehouseName");
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      showtest() {
        console.log("点击了关闭弹窗");
        this.showPop = !this.showPop;
      },
      onChange(index) {
        this.imgIndex = index;
      },
      showImg(item, index) {
        this.showImgList = true;
        this.imgList = item.pictureDetail;
        this.checkIndex = index;
      },
      seeBigImg(item, index) {
        this.imgIndex = index;
        var prodData = this.imgList.map(function(item) {
          return item['docUrl'];
        });
        this.showBigImage = true;
        this.images = prodData;
      },
      uploadPackIdPicture() {
        if (isEmpty(this.packId)) {
          this.$toast("捆包号不能为空");
          return;
        }
        let base64Img = this.uploaderList.map(f => f.content);
        this.uploadImage(base64Img);
      },
      async uploadImage(val) {
        const params = {
          serviceId: "S_UC_PR_210120",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          segCname: localStorage.getItem("segName"),
          packId: this.packId,
          affixType: this.radio,
          pictureList: val,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              this.$toast(res.data.__sys__.msg);
              this.uploaderList = [];
              this.packId = "";
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      deleteImg(val) {
        this.$delete(this.packList[this.checkIndex].pictureDetail, val);
      },
      photoAfterRead(file) {
        console.log(file);
      },
      onOversize(file) {
        console.log(file);
        this.$toast("文件大小不能超过5MB");
      },
      onClickLeft() {
        this.$router.goBack();
      },
      showScan() {
        this.show = true;
      },
    },
  };
</script>

<style lang="less" scoped>
  .div-location {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .search-list {
    width: 98%;
    height: 200px;
    //margin-top: 6px;
    // margin: 8px;
    border: 1px solid #dcdcdc;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .activeColor {
    color: #007aff;
  }

  .van-btn-height {
    height: 100%;
  }

  .img-list {
    width: 98%;
    display: flex;
    flex-wrap: wrap;
  }

  .div-item {
    padding: 8px 5px 5px;
  }
</style>