<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">库位变更</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <van-form>
      <!-- <van-field
        label="新库位"
        @keyup.enter.native="searchLocationName"
      >
       <template #input>
           <input
          	type="search"
            	placeholder="请输入或扫描新库位"
          	   v-model="newLocaltionIdName" />
        </template>
      </van-field> -->
      <van-field name="radio" label="保留库位" class="all-font-size">
        <template #input>
          <van-radio-group v-model="radio" direction="horizontal">
            <van-radio name="00"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              是
            </van-radio>
            <van-radio name="10"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              否
            </van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field label="新库位" :required="true" class="all-font-size" @keyup.enter.native="searchLocationName">
        <template #input>
          <input type="search" class="new-field" placeholder="请输入或扫描新库位" ref="locationIdRef" v-model="newLocationId" />
        </template>
        <template #right-icon>
          <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE" @click="searchLocation"></span>
        </template>
      </van-field>
      <van-field label="新库位名称" class="all-font-size" placeholder="新库位名称" v-model="newLocationName" readonly />
      <van-field v-model="packId" class="all-font-size" label="捆  包" :required="true" ref="labelthird" clearable
        placeholder="请输入或扫描捆包号" @keyup.enter.native="getBaleByPackId" />
      <van-field label="旧库位" class="all-font-size" placeholder="旧库位" v-model="locationId" readonly />
      <van-field label="旧库位名称" class="all-font-size" placeholder="旧库位名称" v-model="locationName" readonly />
    </van-form>

    <div class="in-content">
      <div class="detail_textarea">
        <div class="detail_text" style="padding-bottom: 10px">
          <div class="fourline-blue"></div>
          <div class="baletext2" style="margin-left: 0; margin-top: 14px">
            已扫捆包合计 : <span class="span-count">{{ packList.length }}</span>
          </div>
        </div>
      </div>

      <div class="search-list" v-if="packList && packList.length != 0">
        <div class="in-content">
          <van-swipe-cell v-for="(item, index) in packList" :key="index">
            <van-cell :border="false">
              <template #title>
                <div class="custom-title">{{ item.labelId }}</div>
                <div class="custom-title">{{ item.packId }}</div>
              </template>
              <template #label>
                <span class="custom-title">新库位{{ item.newLocationId }}</span>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp; &nbsp;
                &nbsp;&nbsp;
                <span class="custom-title">旧库位：{{ item.locationId }}</span>
              </template>
              <!--      <template #default>

              </template> -->
            </van-cell>
            <template #right>
              <van-button square type="danger" text="删除" @click="deleteListItem(index)" />
            </template>
          </van-swipe-cell>
        </div>
      </div>
      <div v-else></div>
    </div>
    <div class="mui-input-row3" v-show="isShowBottom">
      <button id="block_button" type="button" class="mui-btn3" @click="onConfirm">
        确&nbsp; &nbsp; &nbsp;&nbsp; 认
      </button>
      <button id="block_button" type="button" class="mui-btn3" @click="changeLocaltion" v-preventReClick="3000">
        倒&nbsp; &nbsp; &nbsp;&nbsp;库
      </button>
    </div>
    <van-dialog v-model="localtionIdShow" title="库位查询" @confirm="onConfirmItem" @cancel="onCancelItem"
      show-cancel-button>
      <div class="dialog-content">
        <div v-if="locationList && locationList.length > 0">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="searchLocation">
            <van-radio-group v-model="radio2">
              <van-cell-group>
                <van-cell v-for="(item, index) in locationList" :key="index" clickable @click="isChecked(index, item)">
                  <template #title>
                    <div class="ware-title">{{ item.locationId }}</div>
                  </template>
                  <template #label>
                    <div>
                      库位名称：{{ item.locationName }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="index"><template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </van-list>
        </div>
        <div v-else>
          <van-empty description="未查询到相应库位" class="all-font-size" />
        </div>
      </div>
    </van-dialog>
    <HmPopup v-if="showPop" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import {
  isEmpty
} from '@/utils/tools';
import {
  Dialog
} from "vant";
import HmPopup from "@/components/HmPopup.vue";
export default {
  data() {
    return {
      loading: false,
      finished: false,
      offset: 0,
      radio: "00",
      radio2: -1,
      showOverlay: false,
      localtionIdShow: false,
      locationList: [],
      scanList: [],
      testList: [],
      baleList: {},
      checkLocaltion: {},
      packList: [],
      show: false,
      showPop: false,
      packId: "",
      locationName: "",
      locationId: "",
      labelId: "",
      newLocationId: "",
      newLocaltionIdName: "",
      matInnerId: "",
      newLocationName: "",
      warehouseCode: "",
      warehouseName: "",
      searchWarehouseCode: "",
      searchWarehouseName: "",
      isShowBottom: true,
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      documentHeight: document.documentElement.clientHeight
    };
  },
  components: {
    HmPopup
  },
  created() {
    this.warehouseCode = localStorage.getItem("warehouseCode");
    this.warehouseName = localStorage.getItem("warehouseName");
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
    if (window.history && window.history.pushState) {
      // 往历史记录里面添加一条新的当前页面的url
      history.pushState(null, null, document.URL);
      // 给 popstate 绑定一个方法 监听页面刷新
      window.addEventListener("popstate", this.backChange, false); //false阻止默认事件
    }
  },
  watch: {
    newLocationId: {
      handler: function () {
        if (isEmpty(this.newLocationId)) {
          // 处理数据变化
          this.newLocationName = "";
        }
      }
    }
  },
  methods: {
    onClickLeft() {
      if (this.packList.length == 0) {
        this.$router.goBack();
        return;
      }
      Dialog.confirm({
        title: "提示",
        message: "清单里有数据未提交确认退出？"
      })
        .then(() => this.$router.push('/'))
        .catch(() => { });
    },
    backChange() {
      // 手机自带返回
      if (this.packList.length == 0) {
        this.$router.goBack();
        return;
      }
      Dialog.confirm({
        title: "提示",
        message: "清单里有数据未提交确认退出？"
      })
        .then(() => {
          this.$router.goBack();
        })
        .catch(() => {
          if (window.history && window.history.pushState) {
            // 手机点击了物理返回 然后又选择了取消 这时需要在添加一条记录
            history.pushState(null, null, document.URL);
          }
        });
    },

    showtest() {
      console.log("点击了关闭弹窗");
      this.showPop = !this.showPop;
    },
    isEmpty(str) {
      return (
        str == "null" ||
        str == null ||
        str == undefined ||
        str == "undefined" ||
        str == "" ||
        JSON.stringify(str) == "{}"
      );
    },
    getCheckInfo(val) {
      this.showPop = !this.showPop;
      this.scanList = val;
      this.baleList = {
        ...val[0]
      };
      this.locationName = this.baleList.locationName;
      this.packId = this.baleList.packId;
      this.locationId = this.baleList.locationId;
      this.matInnerId = this.baleList.matInnerId;
      this.labelId = this.baleList.labelId;
      this.searchWarehouseCode = this.baleList.warehouseCode;
      this.searchWarehouseName = this.baleList.warehouseName;
      if (this.isLocationEmpty()) {
        this.getScanInfo();
      }
    },

    getScanInfo() {
      const val = [{
        warehouseCode: this.searchWarehouseCode,
        warehouseName: this.searchWarehouseName,
        matInnerId: this.matInnerId,
        newLocationId: this.newLocationId,
        newLocationName: this.newLocationName,
        packId: this.baleList.packId,
        locationId: this.locationId,
        locationName: this.locationName,
        labelId: this.labelId,
        specDesc: this.baleList.specDesc,
        grossWeight: this.baleList.grossWeight,
        netWeight: this.baleList.netWeight,
        pieceNum: this.baleList.pieceNum,
        tradeCode: this.baleList.tradeCode,
        settleUserNum: this.baleList.settleUserNum,
        settleUserName: this.baleList.settleUserName,
        unitedPackId: this.baleList.unitedPackId,
      }];
      let flag = this.packList.some(item => {
        return item.packId === val[0].packId;
      });
      if (flag) {
        this.$toast("此捆包号已添加，请重新扫描其他捆包号");
        this.clearList();
      } else {
        this.packList = [...val, ...this.packList];
        this.clearList();
      }
    },

    isEmpty(str) {
      return (
        str == "null" ||
        str == null ||
        str == undefined ||
        str == "undefined" ||
        str == "" ||
        JSON.stringify(str) == "{}"
      );
    },

    clearList() {
      this.packId = "";
      this.locationName = "";
      this.locationId = "";
      this.searchWarehouseCode = "";
      this.matInnerId = "";
      this.labelId = "";
      this.baleList = {};
      this.scanList = [];
      this.testList = [];
      if (this.radio == "10") {
        this.newLocaltionIdName = "";
        this.newLocationId = "";
        this.newLocationName = "";
        this.$refs.locationIdRef.focus();
      } else {
        this.$nextTick(() => {
          this.$refs.labelthird.focus();
        });
      }
    },

    showScan() {
      this.show = true;
    },

    isChecked(index, item) {
      this.radio2 = index;
      this.checkLocaltion = item;
    },

    onCancelItem() {
      this.locationList = [];
      this.offset = 0;
    },

    onConfirmItem() {
      this.newLocationId = this.checkLocaltion.locationId;
      this.newLocationName = this.checkLocaltion.locationName;
      this.radio2 = -1;
      this.checkLocaltion = [];
      this.locationList = [];
      this.offset = 0;
      this.$nextTick(() => {
        this.$refs.labelthird.focus();
      });
      //  this.saveBale();
    },
    // searchLocation() {
    //   if (isEmpty(this.newLocationId)) {
    //     this.$toast("请输入库位参数查询");
    //   } else {
    //     this.searchLocationId()
    //   }
    // },
    async onConfirm() {
      // 南沙一体化找不到捆包,直接新增一条库位变更记录
      let isNanSha = ['KK000000', 'KJ000000', 'KM000000', 'KL000000'].includes(localStorage.getItem('segNo'));
      if (isNanSha) {
        if (!this.isLocationEmpty()) {
          return;
        }
        await this.getBaleByPackId();
        return;
      }

      if (this.scanList && this.scanList.length > 0) {
        if (this.isLocationEmpty()) {
          this.getScanInfo();
        }
      } else {
        if (this.isLocationEmpty()) {
          //this.getScanInfo();
          this.$toast("未查到相应捆包信息!");
        } else {
          this.$toast("新库位或库位名称不能为空!");
        }
      }
    },


    //查询库位
    async searchLocationId1() {
      this.localtionIdShow = true;
      const params = {
        serviceId: "S_UA_CM_201501",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        segCname: localStorage.getItem("segName"),
        locationId: this.newLocationId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              this.locationList = dataObj.result || [];
            } else {
              this.locationList = [];
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async searchLocation() {
      this.localtionIdShow = true;
      const params = {
        serviceId: "S_UA_CM_201501",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        segCname: localStorage.getItem("segName"),
        locationId: this.newLocationId,
        startSize: this.offset,
        length: 10
      };
      const data = await baseApi.baseService(params);
      if (!data || !data.data) {
        this.$toast("网络异常, 请联系管理员!");
        return;
      }
      if (data.data.__sys__.status == '-1') {
        this.$toast(data.data.__sys__.msg);
        return;
      }
      data.data.result.forEach(d => {
        this.locationList.push(d);
      });

      this.offset += 10;
      this.loading = false;
      this.finished = false;
      if (this.offset >= data.data.totalCount) {
        this.finished = true;
      }

    },
    //查询库位名称
    async searchLocationName() {
      const params = {
        serviceId: "S_UC_PR_230623",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        locationId: this.newLocationId
      };
      await baseApi
        .baseService(params)
        .then(res => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1
                }
              };
            }
            if (res.data && res.data.__sys__.status != -1) {
              console.log(dataObj.result);
              this.newLocationId = dataObj.result.locationId;
              this.newLocationName = dataObj.result.locationName;
              this.newLocaltionIdName = dataObj.result.locationName;
              this.$nextTick(() => {
                this.$refs.labelthird.focus();
              });
            } else {
              this.$toast(res.data.__sys__.msg);
              this.newLocationId = "";
              this.newLocationName = "";
              this.newLocaltionIdName = "";
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    async changeLocaltion() {
      const params = {
        serviceId: "S_UC_PR_230612",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        warehouseCode: this.warehouseCode,
        warehouseName: this.warehouseName,
        rowList: this.packList
      };
      await baseApi
        .baseService(params)
        .then(res => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1
                }
              };
            }
            if (dataObj) {
              if (res.data.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                this.packList = [];
                if (this.radio == "10") {
                  this.newLocaltionIdName = "";
                  this.newLocationId = "";
                  this.newLocationName = "";
                  this.$refs.locationIdRef.focus();
                }
              } else {
                this.newLocaltionIdName = "";
                this.newLocationId = "";
                this.newLocationName = "";
                this.$toast(res.data.__sys__.msg);
                //  this.newLocaltionIdName = "";
                //  this.newLocationId = "";
                //  this.newLocationName = "";
              }
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    async getBaleByPackId() {
      // 南沙一体化找不到捆包,直接新增一条库位变更记录
      let isNanSha = ['KK000000', 'KJ000000', 'KM000000', 'KL000000'].includes(localStorage.getItem('segNo'));
      if (!this.isLocationEmpty() && isNanSha) {
        return;
      }

      const params = {
        serviceId: "S_UC_PR_200006",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        packId: this.packId,
        newLocationId: this.newLocationId,
        newLocationName: this.newLocationName,
        warehouseCode: this.warehouseCode,
        warehouseName: this.warehouseName,
        userName: localStorage.getItem('userName'),
        invertedFlag:"1",
      };
      await baseApi
        .baseService(params)
        .then(res => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1
                }
              };
            }
            if (dataObj) {
              this.packId =  dataObj.packId;
              if (dataObj.result.length > 1) {
                this.showPop = !this.showPop;
                this.testList = dataObj.result;
                this.$refs.labelthird.blur();
              } else {

                let errorMsg = `没有查询到相应捆包信息！${isNanSha ? "已新增一条库位变更记录" : ""}`;
                if (dataObj.result.length == 0) {
                  this.$toast(errorMsg);
                  if (isNanSha) {
                    this.packId = "";
                  }
                } else {
                  this.scanList = dataObj.result;
                  this.baleList = {
                    ...dataObj.result[0]
                  };
                  this.locationName = this.baleList.locationName;
                  this.labelId = this.baleList.labelId;
                  this.locationId = this.baleList.locationId;
                  this.matInnerId = this.baleList.matInnerId;
                  this.netWeight = this.baleList.netWeight;
                  this.grossWeight = this.baleList.grossWeight;
                  this.pieceNum = this.baleList.pieceNum;
                  this.searchWarehouseCode = this.baleList.warehouseCode;
                  this.searchWarehouseName = this.baleList.warehouseName;
                  this.$refs.labelthird.blur();
                  if (this.isLocationEmpty()) {
                    this.getScanInfo();
                  }
                }
              }
            } else {
              this.packId =  dataObj.packId;
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    isLocationEmpty() {
      let flag = false;
      if (this.isEmpty(this.newLocationId)) {
        this.$toast("新库位不能为空!");
        flag = false;
      } else {
        if (this.isEmpty(this.newLocationName)) {
          this.$toast("新库位名称不能为空!");
          flag = false;
        } else {
          flag = true;
        }
      }
      return flag;
    },
    deleteListItem(index) {
      //删除数组中值
      this.$delete(this.packList, index);
    }
  },
  beforeDestroy() {
    window.removeEventListener("popstate", this.backChange);
  }
};
</script>

<style lang="less" scoped>
.download-des {
  font-size: 20px;
  font-family: Noto Sans SC;
  font-weight: 500;
  line-height: 22px;
  margin-top: 20px;
  text-align: center;
}

.title-add {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 25px;
}

.dialog-content {
  width: 100%;
  height: 200px;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 2px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}

.search-list {
  //width: 100%;
  // height: 200px;
  //margin-top: 6px;
  // margin: 8px;
  //border: 1px solid #dcdcdc;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 2px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}

.activeColor {
  color: #007aff;
}
</style>
