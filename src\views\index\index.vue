<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #left>
          <div class="" @click="show = true">
            <span class="iconfont icon-ren-copy" style="color: #fff"></span>
          </div>
        </template>
        <template #title>
          <div class="" style="display: flex">
            <img src="@/assets/logo/homeLogo.png" alt="" class="logo-img" />
            <div class="global-hfont">IMC-PDA</div>
          </div>
        </template>
        <template #right>
          <div class="" @click="layout">
            <span class="iconfont icon-tuichu" style="color: #fff"></span>
          </div>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="page-box">
      <van-grid :border="false" :column-num="3">
        <van-grid-item v-for="(item, index) in gridData" :key="index" @click="goItemUrl(item.url)">
          <img :src="require(`@/assets/grid/${item.img}.png`)" class="index-grid-img" />
          <div class="index-media-body">{{ item.text }}</div>
        </van-grid-item>
      </van-grid>
    </div>
    <van-popup v-model="show" position="left" class="pop-content">
      <div class="info-content">
        <div class="info-des" @click="goOrganization">
          <span>账 套: {{ segName }}</span>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>
        <div class="info-des">
          <span>工 号: {{ userId }}</span>
        </div>
        <div class="info-des" @click="goGroup">
          <span>班 组: {{ teamName }}</span>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>
        <div class="info-des">
          <span>班 次: {{ shiftName }}</span>
        </div>
        <div class="info-flex" @click="chooseWarehouse">
          <div class="des">仓 库:</div>
          <div class="com-name" v-if="storeName">{{ storeName }}</div>
          <div class="com-name" v-else>请选择仓库</div>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>

        <div class="info-des" @click="getVersion">
          <span>检查新版本</span>
          <span class="iconfont icon-youjiantou ic-color"></span>
        </div>
        <div class="info-des">
          <span>版本信息</span>
          <div class="com-name" v-if="thisVerion">{{ thisVerion }}</div>
        </div>
      </div>
    </van-popup>

  <!-- <button @click="justTest">测试下载</button> -->
  </div>
</template>

<script>
  import {
    Dialog
  } from "vant";
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty,
    compare
  } from '@/utils/tools';

  export default {
    data() {
      return {
        show: false,
        curVersion: "",
        newVersion: "",
        newUrl: "",
        userId: "",
        segName: "",
        teamName: "",
        shiftName: "",
        storeName: "",
        segNo: "",
        userName: "",
        thisVerion: "",
        warehouseCode: "",
        gridData: [{
            img: "instorage",
            text: "入库",
            url: "/selectCar",
          },
          {
            img: "outstorage",
            text: "出库",
            url: "/selectOption",
          },
          {
            img: "load",
            text: "精细化",
            url: "/load",
          },
          {
            img: "inquire",
            text: "查询",
            url: "/searchList",
          },
          {
            img: "invertedStorehouse",
            text: "倒库",
            url: "/invertedStorehouse",
          },
          {
            img: "panku",
            text: "盘库",
            url: "/checkStorage",
            // url: "/checkList",
          },
          {
            img: "print",
            text: "打印",
            url: "/printLabel",
          },
          {
            img: "uninstall",
            text: "卸载",
            url: "/uninstall",
          },
          {
            img: "merge",
            text: "并包",
            url: "/prallelPackage",
          },
          // {
          //   img: "sign",
          //   text: "签名",
          //   url: "/signature",
          // },
          {
            img: "quality",
            text: "质量",
            url: "/imc-mc/#/mdqa0100",
          },

          {
            img: "facility",
            text: "设备",
            url: "/imc-mc/#/device",
          },
          {
            img: "production",
            text: "生产",
            url: "/imc-mc/#/production",
          },
          {
            img: "bracket",
            text: "料架",
            url: "/imc-mc/#/mdbm0100",
          },
          {
            img: "capital",
            text: "资材",
            url: "/imc-mc/#/capital-material",
          },
          {
            img: "production-board",
            text: "排产",
            url: "/imc-mc/#/production-board",
          },
        ],
      };
    },
    created() {
      this.checkToken();
      this.userId = localStorage.getItem("userId");
      this.segName = localStorage.getItem("segName");
      this.teamName = localStorage.getItem("teamName");
      this.shiftName = localStorage.getItem("shiftName");
      this.storeName = localStorage.getItem("warehouseName");
      this.segNo = localStorage.getItem("segNo");
      this.warehouseCode = localStorage.getItem("warehouseCode");
      this.userName = localStorage.getItem("userName");
      this.thisVerion = localStorage.getItem("thisVerion") || "";
    },
    methods: {

      async getVersion() {
        var that = this;
        var wgtVer;
        // 获取本地应用资源版本号
        plus.runtime.getProperty(plus.runtime.appid, function(inf) {
          console.log("当前版本号", inf.version);
          wgtVer = inf.version; // 当前版本号
          localStorage.setItem("thisVerion", inf.version);
        });
        that.thisVerion = localStorage.getItem("thisVerion") || "";
        const params = {
          serviceId: "S_UC_PR_090202"
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data.__sys__.status != -1) {
              let newUrl = res.data.result.versionAddress;
              let newVersion = res.data.result.versionNo;
              localStorage.setItem("newVerion", newVersion);
              let flag = compare(newVersion, wgtVer)
              console.log("falg判断", flag);
              if (flag == 1) {
                plus.nativeUI.confirm(
                  `检测到新版本(${newVersion})，是否更新`,
                  function(e) {
                    if (e.index == 0) {
                      var wgtUrl = newUrl;
                      var downloadTask = plus.downloader.createDownload(wgtUrl, { //拿到下载任务的对象
                        filename: '_doc/update/'
                      }, function(d, status) {
                        plus.nativeUI.closeWaiting();
                        if (status == 200) { //在回调中根据状态 进行操作
                          var path = d.filename; //下载apk
                          plus.runtime.install(
                            path, {},
                            function() {
                              plus.nativeUI.alert(
                                "更新完成",
                                function() {
                                  plus.runtime.restart();
                                }
                              );
                            },
                            function(e) {
                              console.log(
                                "安装wgt文件失败[" +
                                e.code +
                                "]：" +
                                e.message
                              );
                              plus.nativeUI.alert(
                                "版本更新失败:" +
                                "[" +
                                e.code +
                                "]：" +
                                e.message
                              );
                            }
                          ); // 自动安装apk文件

                        } else {
                          console.log("下载更新失败！");
                          plus.nativeUI.toast("下载更新失败！");
                        }
                      });
                      // 开始下载
                      downloadTask.start()
                      //显示下载状态显示框
                      var waiting = plus.nativeUI.showWaiting("正在下载 - 0%", {
                        back: "none"
                      });
                      var pre_percent = 0;
                      //监听下载
                      downloadTask.addEventListener('statechanged', function(download, status) {
                        //显示loading和进度
                        switch (download.state) {
                          case 0:
                            //下载任务处于可调度下载状态
                            break;
                          case 1:
                            //下载任务建立网络连接，发送请求到服务器并等待服务器的响应
                            break;
                          case 2:
                            // 下载任务网络连接已建立，服务器返回响应，准备传输数据内容
                            break;
                          case 3:
                            // 下载任务接收数据，计算下载进度
                            let percent = parseInt(parseFloat(download.downloadedSize) / parseFloat(download
                              .totalSize) * 100)
                            //取整的情况下会出现同一个百分比出现多次的情况，每次都会执行waiting.setTitle()
                            //有时会出现percent无法正常显示的情况，可能是因为频繁执行waiting.setTitle()导致堆栈内存溢出的问题
                            //增加判断，当percent变化时才执行waiting.setTitle()，以减少函数执行次数，目测有效果
                            if (percent > pre_percent) {
                              waiting.setTitle("正在下载 - " + percent + "%");
                              pre_percent = percent
                            }
                            //经测试，并没有返回状态4，所以自行执行关闭弹窗代码
                            //当已经下载的文件大小等于总文件大小时，执行关闭
                            if (download.downloadedSize == download.totalSize) {
                              plus.nativeUI.closeWaiting();
                            }
                            break;
                          case 4:
                            // 下载任务已完成
                            plus.nativeUI.closeWaiting();
                            break;
                        }
                      })
                    } else {
                      // console.log('取消1');
                    }
                  },
                  "检测到新版本",
                  ["确定", "取消"]
                );
              } else {
                that.$toast("当前为最新版本");
                console.log("已经是最新版本");
              }
            } else {
              plus.nativeUI.toast(res.data.__sys__.msg);
            }
          })
          .catch((error) => {
            console.log(error);
            //  plus.nativeUI.toast("更新失败，请重试！");
          });
      },
      justTest() {
this.$router.togo("/endLoad?carNumber=" + this.carNumber);
      },
      async checkVersion() {
        let that = this
        const params = {
          serviceId: "S_UC_PR_090202"
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                that.newUrl = dataObj.result.versionAddress;
                that.newVersion = dataObj.result.versionNo
                plus.runtime.getProperty(plus.runtime.appid, function(inf) {
                  that.curVersion = inf.version;
                  console.log("当前应用版本：" + inf.version);
                  let flag = compare(that.newVersion, inf.version)
                  console.log("falg判断", flag);
                  //
                  if (flag == 1) {
                    that.downWgt();
                  } else {
                    that.$toast("当前为最新版本");
                  }
                  //检测更新如果有更新则下载安装包进行更新动作
                });
              } else {
                that.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      downWgt() {
        let that = this
        const wgtUrl = this.newUrl;
        plus.nativeUI.showWaiting("正在下载更新中，请稍等...");
        plus.downloader.createDownload(wgtUrl, {
          filename: "_doc/update/"
        }, function(d, status) {
          if (status == 200) {
            console.log("下载更新成功：" + d.filename);
            that.installWgt(d.filename); // 安装wgt资源包
          } else {
            console.log("下载更新失败！");
            plus.nativeUI.toast("下载更新失败！");
          }
          plus.nativeUI.closeWaiting();
        }).start();
      },
      installWgt1(path) {
        plus.runtime.install(path, {}, function() {
          plus.nativeUI.alert("更新完成！", function() {
            //  更新完成后重启应用
            plus.runtime.restart();
          });
        }, function(e) {
          plus.nativeUI.closeWaiting();
          console.log("安装更新失败！[" + e.code + "]：" + e.message);
          plus.nativeUI.toast("安装更新失败！");
        });
      },
      installWgt(path) {
        plus.nativeUI.showWaiting("安装更新包文件...");
        plus.runtime.install(path, {
          force: true
        }, function() {
          plus.nativeUI.closeWaiting();
          plus.nativeUI.alert("更新完成！", function() {
            //  更新完成后重启应用
            plus.runtime.restart();
          });
          console.log("安装wgt文件成功！");
          //TODO 删除下载文件
          //		delAllDirectory("_doc/update");
        }, function(e) {
          plus.nativeUI.closeWaiting();
          console.log("安装更新包文件失败[" + e.code + "]：" + e.message);
          plus.nativeUI.alert("安装更新包文件失败[" + e.code + "]：" + e.message);
          //删除下载文件
          this.delAllDirectory("_doc/update");
        });
      },
      delAllDirectory(path) {
        plus.io.resolveLocalFileSystemURL(path, function(entry) {
          var directoryReader = entry.createReader();
          directoryReader.readEntries(function(entries) {
            var i;
            for (i = 0; i < entries.length; i++) {
              //console.log(entries[i].name);
              entries[i].remove();
            }
          }, function(e) {
            console.log("查找该目录下文件失败" + path)
          });
        });
      },
      goItemUrl(item) {
        if (localStorage.getItem("factoryArea")) {
          if (localStorage.getItem("teamName") == null) {
            Dialog.alert({
              title: "提示",
              message: "您还未选择班组班次",
            }).then(() => {
              this.$router.togo("/group");
            });
          } else {
            if (item.indexOf("/imc-mc")) {
              this.$router.togo(item);
            } else {
               var preUrl;
              if (process.env.NODE_ENV == "production") {
                preUrl = 'http://imc.baogang.info';
              } else{
                preUrl = 'http://imctest.baogang.info';
              }
              var SplicingUrl = preUrl+item;
              window.location.href =
                `${SplicingUrl}?userId=${this.userId}&segNo=${this.segNo}&segName=${this.segName}&userName=${this.userName}&teamName=${this.teamName}&shiftName=${this.shiftName}&storeName=${this.storeName}&warehouseCode=${this.warehouseCode}&factoryArea=${ localStorage.getItem("factoryArea")}`;
              // window.location.href =item+'/?access_token='+
              //   "eTNIZ0dzaGNLVzQ0R0dFTjVoYTZCRXNERkVaTUNMalJvN3pxVEtxOWVmWT0=";
            }
          }

        } else {
          Dialog.alert({
            title: "提示",
            message: "您还未选择厂区仓库",
          }).then(() => {
            this.$router.togo("/factory");
          });
        }

      },
      onClickLeft() {
        this.show = true;
      },
      async checkToken() {
        const params = {
          serviceId: "S_UC_PR_200000",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status == -1) {
                Dialog.alert({
                  title: "提示",
                  message: "登录信息失效，请重新登录",
                }).then(() => {
                  localStorage.clear();
                  this.$router.toReplace("/login");
                });
              } else {
                // console.log(res.data.__sys__.msg);
                //  this.$toast(res.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      goGroup() {
        this.$router.togo("/group");
      },
      chooseWarehouse() {
        this.$router.togo("/factory");
      },
      goOrganization() {
        this.$router.togo("/organization");
      },
      layout() {
        Dialog.confirm({
            title: "提示",
            message: "是否要退出",
          })
          .then(() => {
            localStorage.clear();
            sessionStorage.clear();
            this.$router.togo("/login");
          })
          .catch(() => {
            // on cancel
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  .page-box {
    background-color: #fff;
    //height: 100vh; //若页面占不满时加
  }

  .ic-color {
    color: #696969;
  }

  .global_left_icon {
    position: absolute;
    left: 16px;
  }

  .logo-img {
    width: 26px;
    height: 26px;
    margin-right: 8px;
  }

  .index-content {
    margin-top: 32px;
    margin-left: 28px;
    margin-right: 28px;
  }

  .index-grid-img {
    width: 68px;
    height: 68px;
    // background: #1580f4;
    box-shadow: 1px 5px 9px -1px rgba(69, 93, 222, 0.26);
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
  }

  .index-media-body {
    font-size: 18px;
    //font-family: Noto Sans SC;
    font-weight: 550;
    color: #3d3d3d;
    line-height: 25px;
    letter-spacing: 7px;
  }

  // 弹出层
  .pop-content {
    width: 80%;
    height: 100%;
  }

  .info-content {
    margin: 20px;
  }

  .info-des {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    //font-family: Noto Sans SC;
    font-weight: 550;
    color: #3d3d3d;
    line-height: 35px;
    margin-bottom: 8px;
    border-bottom: 1px solid #dcdcdc;
  }

  .info-right {
    margin-top: 8px;
    //float: right;
    margin-left: 120px;
  }

  .info-flex {
    font-size: 15px;
    // font-family: Noto Sans SC;
    font-weight: 550;
    color: #000;
    line-height: 30px;
    margin-bottom: 8px;
    border-bottom: 1px solid #dcdcdc;
    height: 60px;
    display: flex;
    align-items: center;

    .des {
      color: #3d3d3d;
      width: 60px;
    }
  }


</style>
