
<template>
  <div style="width: 99%" class="hm-content-input">
    <!-- type：先判断是否有传入显示密码，控制输入框类型是文本/密码，然后是type传入的值 -->
    <input
      :type="showPassword ? (passwordVisiable ? 'text' : 'password') : type"
      class="hm_input"
      :placeholder="placeholder"
      :name="name"
      :disabled="disabled"
      :class="{ 'is-disabled': disabled }"
      :value="value"
      ref="searchInput"
      @input="$emit('input', $event.target.value)"
    />
    <div
      class="iconfont icon-qingchu"
      style="color: #c0c4cc;padding-right:10px"
      v-if="value != ''"
      @click="onClear"
    ></div>

    <!-- <span class="cat-input__suffix" v-if="showSuffix">
      <i
        class="cat-input__icon cat-icon-circle-close"
        v-if="clearable && value"
        @click="clear"
      ></i>
      <i
        class="cat-input__icon cat-icon-view"
        :class="{ 'cat-icon-view-active': passwordVisiable }"
        v-if="showPassword"
        @click="handlepwd"
      ></i>
    </span> -->
  </div>
</template>
 
<script>
export default {
  name: "hmInput",
  props: {
    placeholder: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "text",
    },
    name: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: "",
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    showPassword: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      passwordVisiable: false, //控制是否显示密码
      code: "",
      lastTime: "",
      nextTime: "",
      lastCode: "",
      nextCode: "",
      dtmainId: "",
    };
  },
  created() {
    // 自动获取input输入框焦点
    // this.$nextTick(() => {
    //   this.$refs.searchInput.focus();
    // });
    // 模拟用户按下键盘按钮事件，触发扫描方法
    window.document.onkeydown = (e) => {
      if (window.event) {
        // IE
        this.nextCode = e.keyCode;
      } else if (e.which) {
        // Netscape/Firefox/Opera
        this.nextCode = e.which;
      }

      if (e.which === 229) {
        // 键盘回车事件
        if (this.code.length < 3) return; // 扫码枪的速度很快，手动输入的时间不会让code的长度大于2，所以这里不会对扫码枪有效
        console.log("扫码结束。");
        console.log("条形码：", this.code);
        this.parseQRCode(this.code); // 获取到扫码枪输入的内容，做别的操作
        this.lastCode = "";
        this.lastTime = "";
        return;
      }

      this.nextTime = new Date().getTime();
      if (!this.lastTime && !this.lastCode) {
        this.code = ""; // 清空上次的条形码
        this.code += e.key;
        // this.$toast('扫码开始');
        //console.log("扫码开始---", this.code);
      }
      if (
        this.lastCode &&
        this.lastTime &&
        this.nextTime - this.lastTime > 500
      ) {
        // 当扫码前有keypress事件时,防止首字缺失
        this.code = e.key;
        // console.log("防止首字缺失。。。", this.code);
      } else if (this.lastCode && this.lastTime) {
        this.code += e.key;
        // console.log("扫码中。。。", this.code);
      }
      this.lastCode = this.nextCode;
      this.lastTime = this.nextTime;
    };
  },
  methods: {
    onClear(event) {
      this.$emit("input", "");
    },
    parseQRCode(code) {
      console.log("code.length", code.length);
      if (code.length === 13) {
        // 处理自己的逻辑
        console.log("barCode", code);
        // this.$emit("barCode", code); //通知父组件
      } else if (code.length === 23) {
        console.log("B类条码:" + code);
      } else if (code.length === 0) {
        console.log("请输入条码！");
      } else {
       // alert("条码不合法：" + code);
      }
      this.value = code;
    },
    handleinput(event) {
      //父组件在绑定v-model时，其实就绑定的input事件，因此父组件不需要再声明事件了
      this.$emit("input", event.target.value);
    },
    clear() {
      this.$emit("input", "");
    },
    handlepwd() {
      this.passwordVisiable = !this.passwordVisiable;
    },
  },
  computed: {
    //有清空/显示密码，添加类名、显示span
    showSuffix() {
      return this.clearable || this.showPassword;
    },
  },
  beforeDestroy() {
    window.document.onkeydown = "";
  },
};
</script>
<style lang="less" scoped>
.hm-content-input {
  display: flex;
  flex: 1;
  align-items: center;
  height: 40px;
  // height: 25px;
  // line-height: 22px;
}

.hm_input {
  border: #ffffff;
  margin-left: 28px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 400;
  line-height: 22px;
  width: 80%;
}
</style>