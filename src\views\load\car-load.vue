<template>
  <div>
    <van-nav-bar>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
      <template #title>
        <div class="global-hfont">车辆装卸货管理</div>
      </template>
      <template #right>
        <div class="" @click="goChooseLoad">
          <span class="iconfont icon-a-18zhuangxiehuodidian"></span> <span class="load-point-name">装卸点</span>
        </div>
      </template>
    </van-nav-bar>
    <div class="in-content">
      <div class="detail_row" style="background-color: #e7e7e7">
        <div class="fourtext">装卸点</div>
        <span class="iconfont icon-shuxian icon-line" style="color: #d0d0d0"></span>
        <input class="detail_input" type="text" :value="loadName" readonly />
      </div>
      <div class="detail_row">
        <div class="fourtext" style="width: 20%">车牌号</div>
        <span class="iconfont icon-shuxian icon-line"></span>
        <input class="detail_input" type="text" readonly value="" v-model="vehicleNo" placeholder="请获取查询车牌号"
          style="width: 70%" />
        <span class="iconfont icon-sousuo" style="width: 10%; color: #d0d0d0" @click="onFocus"></span>
      </div>
      <van-notice-bar wrapable :scrollable="false" v-if="tipStr" color="#000000" background="#ecf9ff" :text="tipStr">
        <template #default>
          <div class="notice-title">{{ tipStr }}</div>
        </template>
      </van-notice-bar>
      <van-dialog v-model="show" :show-confirm-button="showBtn">
        <div>
          <van-search v-model="value" shape="round" background="#007aff" placeholder="请输入车牌号" @search="getVehicleList"
            left-icon="" :clearable="false">
            <template #right-icon>
              <div class="iconfont icon-sousuo" @click="getVehicleList" style="color: #999999"></div>
            </template>
          </van-search>
        </div>

        <div class="search-list">
          <div v-if="vehicleList && vehicleList.length > 0">
            <van-radio-group v-model="value">
              <van-cell-group>
                <van-cell v-for="(item, index) in vehicleList" :key="index" clickable @click="onCheck(index, item)">
                  <template #title>
                    <div class="seg-title">{{ item }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item">
                      <template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </div>

          <div class="tips-content" v-else>
            <div class="title">提示</div>
            <div class="msg">未查询到可选择的车牌！</div>
          </div>
        </div>
        <button class="search-btn" @click="onConfrimCarNumber">确认</button>
      </van-dialog>
    </div>
    <div class="btn-group">
      <button class="mui-btn" @click="onOutStorage">
        卸&nbsp; &nbsp; &nbsp;&nbsp;货
      </button>
      <button class="mui-btn" @click="onInStorage">
        装&nbsp; &nbsp; &nbsp;&nbsp;货
      </button>
      <button class="mui-btn" @click="onEndLoad">
        卸载完成
      </button>
      <button class="mui-btn" @click="onNextLoad">
        下一目标
      </button>
    </div>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty
  } from '@/utils/tools';
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        value: "",
        remark: "",
        tipStr: "",
        show: false,
        vehicleNo: "",
        showBtn: false,
        loadName: "",
        searchList: [],
        vehicleList: [],
        showMsg: false,
        currentIndex: -1,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
      };
    },
    created() {
      if (isEmpty(sessionStorage.getItem("pdaLoadName"))) {
        Dialog.alert({
          title: "提示",
          message: "您还未选择装卸点",
        }).then(() => {
          this.$router.togo("/choose");
        });
      } else {
        this.loadName = sessionStorage.getItem("pdaLoadName");
        this.getVehicleList();
      }

    },
    methods: {
      onFocus() {
        this.show = true;
      },
      onClickLeft() {
        // this.$router.goBack();
        this.$router.togo("/");
      },
      goChooseLoad() {
        this.$router.togo("/choose");
      },
      getVehicleList() {
        if (isEmpty(this.loadName)) {
          Dialog.alert({
            title: "提示",
            message: "您还未选择装卸点",
          }).then(() => {
            this.$router.togo("/choose");
          });
        } else {
          this.getVehicleList2();
        }
      },
      //查询待装卸车牌号列表
      async getVehicleList2() {
        const params = {
          serviceId: "S_UC_PR_230103",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNoName: this.value,
          loadingPointNo: sessionStorage.getItem("pdaLoadNo"),
          type: "normal",
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.vehicleList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getScanPackCount() {
        const params = {
          serviceId: "S_UC_PR_210106",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          vehicleNo: this.vehicleNo,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.tipStr = dataObj.result.packInfo;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onInStorage() {
        if (isEmpty(this.vehicleNo)) {
          this.$toast("请选择车牌号");
        } else if (isEmpty(sessionStorage.getItem("pdaLoadName"))) {
          Dialog.alert({
            title: "提示",
            message: "您还未选择装卸点",
          }).then(() => {
            this.$router.togo("/choose");
          });
        } else {
          this.onInStorage1();
        }

      },
      onEndLoad() {
        if (isEmpty(this.vehicleNo)) {
          this.$toast("请选择车牌号");
        } else if (isEmpty(sessionStorage.getItem("pdaLoadName"))) {
          Dialog.alert({
            title: "提示",
            message: "您还未选择装卸点",
          }).then(() => {
            this.$router.togo("/choose");
          });
        } else {
          this.$router.togo({
            name: "endLoad",
            params: {
              carNumber: this.vehicleNo
            },
          });
        }
      },
      onNextLoad() {
        console.log(isEmpty(sessionStorage.getItem("pdaLoadName")));
        if (isEmpty(sessionStorage.getItem("pdaLoadName"))) {
           this.$toast("请选择装卸点");
        } else {
          this.$router.togo({
            name: "loadList",
            params: {
              carNumber: this.vehicleNo
            },
          });
        }
        // if (isEmpty(sessionStorage.getItem("pdaLoadName"))) {
        //    Dialog.alert({
        //      title: "提示",
        //      message: "您还未选择装卸点",
        //    }).then(() => {
        //      this.$router.togo("/choose");
        //    });
        //  } else {
        //    this.$router.togo({
        //      name: "loadList",
        //      params: {
        //        carNumber: this.vehicleNo
        //      },
        //    });
        //  }


        //  this.$router.togo("/loadList");
      },
      onOutStorage() {
        if (isEmpty(this.vehicleNo)) {
          this.$toast("请选择车牌号");
        } else if (isEmpty(sessionStorage.getItem("pdaLoadName"))) {
          Dialog.alert({
            title: "提示",
            message: "您还未选择装卸点",
          }).then(() => {
            this.$router.togo("/choose");
          });
        } else {
          this.onOutStorage1();
        }
      },
      //开始装货
      async onInStorage1() {
        const params = {
          serviceId: "S_UC_PR_230501",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNo: this.vehicleNo,
          loadingPointNo: sessionStorage.getItem("pdaLoadNo"),
          loadingPointName: sessionStorage.getItem("pdaLoadName"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                //添加调用接口获取提单号
                this.searchLodingList();

              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询提单号
      async searchLodingList() {
        const params = {
          serviceId: "S_UC_PR_230643",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNo: this.vehicleNo,
          loadingPointNo: sessionStorage.getItem("pdaLoadNo"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //this.$toast(res.data.__sys__.msg);
                //添加调用接口获取提单号不为空跳转出库页面 为空跳转到提单页面
                // if (res.data.result.length > 0) {
                //   this.$router.togo({
                //     name: "outlist",
                //     params: {
                //       carNumber: this.vehicleNo,
                //       ladingBillIdList: res.data.result,
                //       remark: this.remark,
                //     },
                //   });
                // } else {
                this.$router.togo({
                  name: "lodingBill",
                  params: {
                    fromList: res.data.result,
                    carNumber: this.vehicleNo,
                    remark: this.remark,
                  },
                });

                //}

              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //开始卸车
      async onOutStorage1() {
        const params = {
          serviceId: "S_UC_PR_230502",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          vehicleNo: this.vehicleNo,
          loadingPointNo: sessionStorage.getItem("pdaLoadNo"),
          loadingPointName: sessionStorage.getItem("pdaLoadName"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                this.$router.togo("/inlist?carNumber=" + this.vehicleNo);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onConfrimCarNumber() {
        this.show = false;
        this.value = "";
        this.getScanPackCount();
      },
      onConfrimCar() {
        this.show = false;
        Dialog.alert({
          title: "提示",
          message: "出库成功",
          confirmButtonColor: "	#1E90FF",
        }).then(() => {
          // on close
        });
      },
      onCheck(index, item) {
        this.value = item;
        this.currentIndex = index;
        this.vehicleNo = item;
      },
    },
  };
</script>

<style lang="less" scoped>
  .search-list {
    height: 150px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
    overflow-y: auto;
    margin-bottom: 80px;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  // /deep/ .van-dialog {
  //   position: fixed;
  //   top: 60%;
  //   left: 50%;
  //   width: 90%;
  //   overflow: hidden;
  //   font-size: 16px;
  //   background-color: #fff;
  //   border-radius: 16px;
  //   -webkit-transform: translate3d(-50%, -50%, 0);
  //   transform: translate3d(-50%, -50%, 0);
  //   -webkit-backface-visibility: hidden;
  //   backface-visibility: hidden;
  //   -webkit-transition: 0.3s;
  //   transition: 0.3s;
  //   -webkit-transition-property: opacity, -webkit-transform;
  //   transition-property: opacity, -webkit-transform;
  //   transition-property: transform, opacity;
  //   transition-property: transform, opacity, -webkit-transform;
  // }

  .search-btn {
    position: absolute;
    bottom: 0px;
    width: 100%;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    color: #fff;
  }

  .load-point-name {
    color: #fff;
    font-size: 15px;
  }

  .car-list {
    height: 30px;
    margin: 5px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
  }

  .tips-content {
    height: 110px;
    margin: 15px;
    background-color: #fff;
    font-size: 14px;
    padding: 10px;
    border-radius: 10px;

    div {
      margin: 10px;
    }

    .title {
      text-align: center;
    }

    .msg {
      margin-top: 10px;
    }
  }

  .btn-group {
    margin: 20px 20px 8px 20px;
  }

  .activeColor {
    color: #007aff;
  }

  .notice-title {
    font-size: 18px;
  }
</style>
