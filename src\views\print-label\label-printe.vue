<script>
import * as TransferService from "../../api/transfer-receipt-api";
import { Dialog } from "vant";

var elitetyc = require("../../../static/js/plugin.js");
export default {
    data() {
        return {
            contractSubNum: '',
            item: '',
            isShowOverlay: false,
            loading: false,
            plugins: elitetyc,
        };
    },
    methods: {
        // 确认打印
        confirmPrint() {
            if (!this.item) {
                Dialog.alert({ title: '提示', message: '请先查询数据' });
                return;
            }
            Dialog.alert({
                title: "提示",
                message: "正在调用蓝牙打印",
            }).then(() => {
                // on close
                let objArr = Object.values(this.item);
                let subid = objArr[2];
                let spec = objArr[0];
                objArr[0] = subid;
                objArr[2] = spec;
                const str = objArr.join(' ');
                let params = {
                    packId: str,
                    printFlag:"二维码",
                    printCount:1,
                };
                const that = this;
                //点击事件内部调用了plugin.js中暴露的方法
                this.plugins.CalcNameAddNumFunction(params, function (result) {
                    // Toast(JSON.stringify(result));
                    console.log(JSON.stringify(result));
                    that.item = '';

                });
            });
        },
        onClickLeft() {
            this.$router.goBack();
        },
        getContractSub: async function () {
            // 根据合同子项号查询资材信息
            if (!this.contractSubNum) {
                Dialog.alert({ message: "请填写合同子项号" });
                return;
            }

            this.contractSubNum = this.contractSubNum.split(' ')[0];
            this.isShowOverlay = true;
            const data = await TransferService.queryBlueLabelSub(this.contractSubNum);
            if (data) {
                this.item = data[0];
            }
            console.log(this.item)
            this.isShowOverlay = false;
        },
    },
};
</script>

<template>
    <div class="backgroud-div">
        <van-nav-bar>
            <template #title>
                <div class="global-hfont">资材打印</div>
            </template>
            <template #left>
                <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
            </template>
        </van-nav-bar>
        <div class="div-main">
            <van-search v-model="contractSubNum" placeholder="请输入合同子项号" @search="getContractSub();" />

            <h2 class="van-doc-demo-block__title">详情</h2>
            <van-cell-group inset class="child-cell-data van-group">
                <van-cell title="采购合同子项号" v-model:value="item.stuffContractSubid" />
                <van-cell title="资材代码" v-model:value="item.stuffCode" />
                <van-cell title="资材名称" v-model:value="item.sparePartsName" />
                <van-cell title="规格" v-model:value="item.specDesc" />
                <van-cell title="供应商代码" v-model:value="item.providerCode" />
                <van-cell title="供应商名称" v-model:value="item.providerCname" />
                <van-cell title="计量单位" v-model:value="item.itemCname" />

            </van-cell-group>
        </div>

        <div class="foot-sticky">
            <van-button type="info" class="foot-sticky-btn" :loading="loading" :loading-text="`打印中...`"
                @click="confirmPrint();">确认打印</van-button>
        </div>

        <!-- <van-overlay :show="isShowOverlay">
            <div class="wrapper">
                <div class="block">
                    <van-loading color="#1989fa" />
                </div>
            </div>
        </van-overlay> -->
    </div>
</template>

<style lang="less" scoped>
.btn-text-icon {
    margin-left: 2em;
}

div.main-content {
    height: 90%;
}

.vant-btn {
    width: 90%;
}

.backgroud-div {
    background-color: rgba(243, 243, 243, 1);
}
</style>
