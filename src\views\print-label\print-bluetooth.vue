<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">标签蓝牙打印</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>

    <!-- 进入页面光标停留输入框，扫描后自动跳转到捆包 -->
    <div class="">
     <!-- <van-field v-model="bale" label="捆包" class="all-font-size" clearable placeholder="请输入或扫描捆包号"
        :rules="[{ required: true, message: '请输入或扫描捆包号' }]" @keyup.enter.native="handerSearch" /> -->
        <van-field label="捆包" @keyup.enter.native="getBaleByPackId" class="all-font-size"  :rules="[{ required: true, message: '请输入或扫描捆包号' }]">
          <template #input>
            <input type="search" class="new-field" placeholder="请输入或扫描捆包号" ref="baleref" v-model="bale" />
          </template>
        </van-field>
      <van-field label="规 格" class="all-font-size" v-model="baleList.specDesc" placeholder="规格" readonly />
      <van-field label="牌 号" class="all-font-size" v-model="baleList.shopsign" placeholder="牌号" readonly />
      <van-field label="客 户" class="all-font-size" v-model="baleList.customerName" placeholder="客户" readonly />

      <!-- 扫描捆包条码信息，如果匹配直接添加捆包到一扫描捆包列表中 -->
      <div class="detail_row">
        <div class="fourtext">重/件</div>
        <div style="width: 80%">
          <input class="weight_input" type="text" v-model="baleList.netWeight" readonly />
          <input class="weight_input" type="text" v-model="baleList.pieceNum" readonly />
        </div>
      </div>
       <van-field label="打印张数" class="all-font-size" v-model="printCount" />
    </div>
    <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" @click="saveBale">
      <button id="block_button" type="button" class="mui-btn" v-preventReClick="3000">蓝牙打印</button>
    </div>
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import * as alternateUnits from "@/utils/alternate-units";
  import HmInput from "@/components/input.vue";
  import HmPopup from "@/components/HmPopup.vue";
  import {
    Dialog
  } from "vant";

  var elitetyc = require("../../../static/js/plugin.js");
  export default {
    data() {
      return {
        bale: "",
        storeNumber: "",
        storeName: "",
        show: "",
        matInnerId: "",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        baleList: {},
        location: "", //库位
        chooseList: [],
        testList: [],
        plugins: elitetyc,
        bluetoothList: {},
        settleUserNum: "",
        printCount:1,
      };
    },
    components: {
      HmInput,
      HmPopup,
    },
    created() {
      this.storeName = localStorage.getItem("warehouseName");
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      getCheckInfo(val) {
		   this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.matInnerId = this.baleList.matInnerId;
        this.bale = this.baleList.packId;
        this.getBaleByMatInnerId();
      },
      showtest() {
        console.log("点击了关闭弹窗");
        this.show = !this.show;
      },
      handerSearch() {
        this.getBaleByPackId();
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_200006",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          packId: this.bale,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.packId =  dataObj.packId;
                //console.log(dataObj.result);
                if (dataObj.result.length > 1) {
                  this.show = !this.show;
                  this.testList = dataObj.result;
                } else {
                  this.chooseList = dataObj.result;
                  this.baleList = {
                    ...dataObj.result[0]
                  };
                  this.location = this.baleList.locationId;
                  this.matInnerId = this.baleList.matInnerId;
                  if (this.chooseList.length > 0) {
                    this.getBaleByMatInnerId();
                  } else {
                    this.$toast("未查询到该捆包信息");
                  }
                }
              } else {
                this.packId =  dataObj.packId;
                this.baleList = {};
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByMatInnerId() {
        const params = {
          serviceId: "S_UC_PR_200005",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          matInnerId: this.matInnerId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.bluetoothList = {
                  ...dataObj.result[0]
                };
                console.log(this.bluetoothList);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      printBale() {
        let that = this;
        if (window.plus) {
          plus.nativeUI.confirm("正在调用蓝牙打印", async function(e) {
            if (e.index == 0) {
               that.printBale2();
              console.log("You clicked OK!");
            }
          }, "提示", ["确认"]);
        } else {
         Dialog.alert({
           title: "提示",
           message: "正在调用蓝牙打印",
         }).then(() => {
           // on close
           that.printBale2();
         });
        }
        
        // Dialog.alert({
        //   title: "提示",
        //   message: "正在调用蓝牙打印",
        // }).then(() => {
        //   // on close
        //   this.printBale2();
        // });
      },
      async printBale2() {
        let params = {
          ...this.bluetoothList,
            //netWeight: this.bluetoothList.netWeight ? (alternateUnits.GetNumByUnit(Number(this.bluetoothList.netWeight), "t", "kg")) : 0,
          netWeight:  this.bluetoothList.netWeight ? ((Number( this.bluetoothList.netWeight) * 1000)).toFixed(2) : 0,
          grossWeight:  this.bluetoothList.grossWeight ? ((Number( this.bluetoothList.grossWeight) * 1000)).toFixed(2) : 0,
          printCount: this.printCount ? (Number(this.printCount)) : 1,
          segNo: localStorage.getItem("segNo"),
        };
        console.log("pa",params);
        const that = this;
        //点击事件内部调用了plugin.js中暴露的方法
        this.plugins.CalcNameAddNumFunction(params, function(result) {
          // Toast(JSON.stringify(result));
          console.log(JSON.stringify(result));
          that.bluetoothList = {};
          that.baleList = {};
          that.chooseList = [];
          that.bale = "";

        });
      },
      saveBale() {
        if (this.chooseList && this.chooseList.length > 0) {
          this.printBale();
        } else {
          this.$toast("未查到捆包信息!");
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .store-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }
</style>
