<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">装卸结束</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
      <div class="in-content">
        <div class="detail_row" style="background-color: #e7e7e7">
          <div class="fourtext2">当前车辆</div>
          <div class="iconfont icon-shuxian" style="color: #d0d0d0"></div>
          <div class="fourtext1">
            <input class="detail_input" type="text" v-model="vehicleNo" readonly />
          </div>
        </div>
        <van-dropdown-menu class="select-menu" active-color="#0000ff">
          <van-dropdown-item :title="nextWarehouse"  ref="item">
            <div v-if="warehouseOption && warehouseOption.length > 0">
              <van-radio-group v-model="nextWarehouseCode">
                <van-cell-group>
                  <van-cell v-for="(item, index) in warehouseOption" :key="index" clickable @click="isChecked(item)">
                    <template #title>
                      <div class="ware-title">{{ item.warehouseCode }}</div>
                    </template>
                    <template #label>
                      <div class="ware-name">{{ item.warehouseName }}</div>
                    </template>
                    <template #right-icon>
                      <van-radio :name="item.warehouseCode"><template #icon="props">
                          <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                        </template>
                      </van-radio>
                    </template>
                  </van-cell>
                </van-cell-group>
              </van-radio-group>
            </div>
            <div v-else>
              <van-empty description="暂未查到相关列表" />
            </div>
          </van-dropdown-item>
        </van-dropdown-menu>
        <div style="height: 12px"></div>
        <van-dropdown-menu class="select-menu" active-color="#0000ff">
          <van-dropdown-item :title="nextLoadingPoint" @open="openItem"  ref="item2">
            <div v-if="loadList && loadList.length > 0">
              <van-radio-group v-model="nextLoadingPointNo">
                <van-cell-group>
                  <van-cell v-for="(item, index) in loadList" :key="index" clickable @click="isChecked2(item)">
                    <template #title>
                      <div class="ware-title">{{ item.loadingPointName }}</div>
                    </template>
                    <template #label>
                      <div class="ware-name">{{ item.loadingPointNo }}</div>
                    </template>
                    <template #right-icon>
                      <van-radio :name="item.loadingPointNo"><template #icon="props">
                          <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                        </template>
                      </van-radio>
                    </template>
                  </van-cell>
                </van-cell-group>
              </van-radio-group>
            </div>
            <div v-else>
              <van-empty description="暂未查到相关列表" />
            </div>
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </van-pull-refresh>
    <div class="mui-input-row3" v-show="isShowBottom">
      <button  type="button" class="mui-btn3" @click="clearCheck">
         清&nbsp; &nbsp; &nbsp;&nbsp; 空
      </button>
      <button  type="button" class="mui-btn3" @click="onConfirm" v-preventReClick="3000">
         确&nbsp; &nbsp; &nbsp;&nbsp; 认
      </button>
    </div>
   <!-- <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" v-preventReClick="3000" @click="onConfirm">
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;认
      </button>
    </div> -->
    <!-- 司机签名 -->
    <van-popup :style="{ height: '100%' }" v-model="signShow2" get-container="body" position="bottom">
      <signCanvas @close="closeDialog2" :d_h="h" :d_w="w" :title="title1"></signCanvas>
      <!--  <div class="canvans-tip">提示模块</div> -->
    </van-popup>
     <!-- 业务签名 -->
    <van-popup :style="{ height: '100%' }" v-model="signShow" get-container="body" position="bottom">
      <signCanvas @close="closeDialog" :d_h="h" :d_w="w" :title="title2"></signCanvas>
      <!--  <div class="canvans-tip">提示模块</div> -->
    </van-popup>
    <!-- <van-dialog v-model="signShow" title="电子签名" :show-confirm-button="false">
      <div class="page-content">

        <div class="content">
          <vue-esign ref="esign"  :d_h="h" :d_w="w" :line-width="4" line-color="#000" bg-color="#FFF" />
        </div>
        <div class="sign-btn">
          <van-button type="danger" @click="handleReset">重签</van-button>
          <van-button type="info" @click="preview">预览</van-button>
          <van-button type="primary" @click="handleGenerate">确认</van-button>
        </div>
      </div>

    </van-dialog> -->
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import vueEsign from 'vue-esign'
  import {
    isEmpty
  } from '@/utils/tools';
  import {
    ImagePreview,
    Dialog
  } from "vant";
  import signCanvas from '@/components/signature.vue'
  export default {
    data() {
      return {
        signShow: false,
        signShow2: false,
        isLoading: false,
        resultImg: "",
        resultImg2: "",
        title1:"司机签名",
        title2:"业务签名",
        warningMsg:"",
        putoutIdList: [],
        putinIdList: [],
        noPlanList: [],
        vehicleNo: "",
        nextWarehouseCode: "",
        nextWarehouse: "请选择下个目标",
        nextWarehouseName: "",
        nextLoadingPointNo: "",
        nextLoadingPoint: "请选择下个装卸点",
        nextLoadingPointName: "",
        warehouseOption: [],
        loadList: [],
        w: document.documentElement.clientWidth, //实时屏幕宽度
        h: document.documentElement.clientHeight, //实时屏幕高度
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
      };
    },
    components: {
      vueEsign,
      signCanvas
    },
    created() {
      this.getStoreList();
      this.vehicleNo = this.$route.params.carNumber;
      this.putoutIdList = this.$route.params.putoutIdList || [];
      this.putinIdList = this.$route.params.putinIdList || [];
      this.noPlanList = this.$route.params.noPlanList || [];
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      closeDialog(e) {
        // 签名赋值 这里的e就是base64了，直接使用
        // 关闭签名框,这里要调用上传图片接口不要直接确认

        if (!isEmpty(e)) {
          this.resultImg = e;
          this.getWarningMsg();
          //this.signShow2 = true;
          //this.isCustomerSign();
        }
        this.signShow = false;

      },
      closeDialog2(e) {
        // 签名赋值 这里的e就是base64了，直接使用
        // 关闭签名框,这里要调用上传图片接口不要直接确认

        if (!isEmpty(e)) {
          this.resultImg2 = e;
          this.toConfirm();
        }
        this.signShow2 = false;

      },
      onClickLeft() {
        this.$router.goBack();
      },
      onRefresh() {
        this.getStoreList();
      },
      handleReset() {
        this.$refs["esign"].reset(); // 清空画布
      },

      preview() {
        this.$refs["esign"]
          .generate()
          .then((res) => {
            this.resultImg = res; // 得到了签字生成的base64图片
            ImagePreview([res]);
          })
          .catch(() => {
            // 没有签名，点击生成图片时调用
            this.$toast("未签名!");
          });
      },
      handleGenerate() {
        this.$refs["esign"]
          .generate()
          .then((res) => {
            this.resultImg = res; // 得到了签字生成的base64图片
            this.signShow = false;
            this.toConfirm();
          })
          .catch(() => {
            // 没有签名，点击生成图片时调用
            this.$toast("未签名!");
          });
      },
      isChecked(item) {
        this.nextWarehouse = item.warehouseName;
        this.nextWarehouseName = item.warehouseName;
        this.nextWarehouseCode = item.warehouseCode;
        this.getLoadList();
        this.$refs.item.toggle();
      },
      isChecked2(item) {
        this.nextLoadingPoint = item.loadingPointName;
        this.nextLoadingPointName = item.loadingPointName;
        this.nextLoadingPointNo = item.loadingPointNo;
        this.$refs.item2.toggle();
      },
      clearCheck(){
        this.nextWarehouse = "请选择下个目标";
        this.nextWarehouseName = "";
        this.nextWarehouseCode = "";
        this.nextLoadingPoint =  "请选择下个装卸点";
        this.nextLoadingPointName =  "";
        this.nextLoadingPointNo =  "";
        this.loadList = [];
      },
      openItem() {
        if (this.nextWarehouseCode.length == 0) {
          this.$toast("请先选择仓库");
        }
      },
      //判断是否要业务客户签字
      async isCustomerSign() {
        const params = {
          serviceId: "S_UC_PR_230646",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                if(dataObj.result == 1){
                  //再调用提示方法
                  this.signShow = true;
                }else{
                  this.getWarningMsg();
                   // this.signShow2 = true; //司机
                  //调用完成方法
                //  this.toConfirm();
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getWarningMsg() {
        let that = this;
        const params = {
          serviceId: "S_UC_PR_230510",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          vehicleNo: that.vehicleNo,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                that.warningMsg = dataObj.result.warningMsg;
                if(!isEmpty(that.warningMsg)){
                if (window.plus) {
                  plus.nativeUI.confirm(that.warningMsg, function(e) {
                    if (e.index == 0) {
                      //that.isCustomerSign();
                      that.signShow2 = true;
                    } else {
                      console.log("You clicked Cancel!");
                    }
                  }, "提示", ["确认", "取消"]);
                } else {
                  Dialog.confirm({
                    title: '提示',
                    message: that.warningMsg,
                  }).then(() => {
                    //that.isCustomerSign();
                    that.signShow2 = true;
                    // on close
                  }).catch(() => {
                  // on cancel
                  });
                }
                }else{
                  // that.isCustomerSign();
                   that.signShow2 = true;
                }
              } else {
                that.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      // 下个目标查询仓库列表
      async getStoreList() {
        const params = {
          serviceId: "S_UC_PR_200002",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.warehouseOption = dataObj.result;
                this.isLoading = false;
              } else {
                this.$toast("登录失败");
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询装卸点  先选择仓库再选择装卸点
      async getLoadList() {
        const params = {
          serviceId: "S_UC_PR_200301",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          warehouseCode: this.nextWarehouseCode,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                this.loadList = dataObj.result;
              } else {
                this.$toast("调用失败!");
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onConfirm() {

        if (isEmpty(this.nextWarehouseCode) && isEmpty(this.nextLoadingPointNo)) {
          //加个判断如果已经签名了 就不要再弹信息了 直接调用上传确认
           // this.getWarningMsg();
           this.isCustomerSign();
          //  this.signShow = true;
        } else {
          if (isEmpty(this.nextLoadingPointNo)) {
            this.$toast("请选择下个装卸点!");
          } else {
            this.toConfirm();
          }

        }

      },
      //装卸完成  可以不选下个内容
      async toConfirm() {
        let that = this;
        const params = {
          serviceId: "S_UC_PR_230503",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          vehicleNo: that.vehicleNo,
          nextWarehouseCode: that.nextWarehouseCode,
          nextWarehouseName: that.nextWarehouseName,
          nextLoadingPointNo: that.nextLoadingPointNo,
          nextLoadingPointName: that.nextLoadingPointName,
          putinIdList: that.putinIdList,
          noPlanList: that.noPlanList,
          putoutIdList: that.putoutIdList,
          picture: that.resultImg2,//添加picture2司机签名
          picture2:that.resultImg,
          loadingPointNo: sessionStorage.getItem("pdaLoadNo") || "",
          loadingPointName: sessionStorage.getItem("pdaLoadName") || ""
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //this.$toast(res.data.__sys__.msg);
                if (window.plus) {
                  plus.nativeUI.confirm(`${dataObj.__sys__.msg} 回到车辆装卸货管理`, function(e) {
                    if (e.index == 0) {
                      if (!isEmpty(that.nextLoadingPointName)) {
                        sessionStorage.setItem("pdaLoadName", that.nextLoadingPointName);
                        sessionStorage.setItem("pdaLoadNo", that.nextLoadingPointNo);
                      }

                      that.$router.replace('/carLoad');
                    } else {
                      console.log("You clicked Cancel!");
                    }
                  }, "提示", ["确认", "取消"]);
                } else {
                  Dialog.confirm({
                    title: '提示',
                    message: `${dataObj.__sys__.msg} 回到车辆装卸货管理`,
                  }).then(() => {
                    if (!isEmpty(this.nextLoadingPointName)) {
                      sessionStorage.setItem("pdaLoadName", that.nextLoadingPointName);
                      sessionStorage.setItem("pdaLoadNo", that.nextLoadingPointNo);
                    }

                    that.$router.replace('/carLoad');
                    // on close
                  }) .catch(() => {
                  // on cancel
                  });
                }

              } else {
                that.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  /deep/ .van-dropdown-menu__item {
    display: flex;
    justify-content: flex-start;
    padding-left: 10px;
  }

  .factory-body {
    height: 100vh;
    background-color: #f3f3f3;
  }

  .ware-title {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #007aff;
    line-height: 20px;
  }

  .ware-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #333333;
    line-height: 22px;
  }

  .activeColor {
    color: #007aff;
  }

  .canvans-tip {
    width: 100px;
    font-size: 15px;
    z-index: 10;
    left: 200px;
    position: fixed;
    bottom: 100px;
    transform: rotate(90deg);
  }

  .page-content {
    padding: 10px;
    background-color: #f1f1f1;

    .content {
      border: 1px solid #f1f1f1;
    }

    .sign-btn {
      margin: 10px;
      display: flex;
      justify-content: space-around;
      align-content: center;
    }
  }
</style>
