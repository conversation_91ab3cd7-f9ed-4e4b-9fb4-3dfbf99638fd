<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">捆包打印</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>

    <!-- 进入页面光标停留输入框，扫描后自动跳转到捆包 -->
    <div class="">
      <van-field v-model="bale" class="all-font-size" label="捆包" clearable placeholder="请输入或扫描捆包号"
        :rules="[{ required: true, message: '请输入或扫描捆包号' }]" @keyup.enter.native="handerSearch" />
      <van-field label="规 格" class="all-font-size" v-model="baleList.specDesc" placeholder="规格" readonly />
      <van-field label="牌 号" class="all-font-size" v-model="baleList.shopsign" placeholder="牌号" readonly />
      <van-field label="客 户" class="all-font-size" v-model="baleList.customerName" placeholder="客户" readonly />
      <!-- 扫描捆包条码信息，如果匹配直接添加捆包到一扫描捆包列表中 -->
      <div class="detail_row">
        <div class="fourtext">重/件</div>
        <div style="width: 80%">
          <input class="weight_input" type="text" v-model="baleList.netWeight" readonly />
          <input class="weight_input" type="text" v-model="baleList.pieceNum" readonly />
        </div>
      </div>
    </div>
    <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" @click="saveBale">
      <button id="block_button" type="button" class="mui-btn" v-preventReClick="3000">远程打印</button>
    </div>
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import HmInput from "@/components/input.vue";
  import HmPopup from "@/components/HmPopup.vue";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        bale: "",
        storeNumber: "",
        storeName: "",
        show: "",
        matInnerId: "",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        baleList: {},
        location: "", //库位
        chooseList: [],
        testList: [],
      };
    },
    components: {
      HmInput,
      HmPopup,
    },
    created() {
      this.storeName = localStorage.getItem("warehouseName");
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      getCheckInfo(val) {
		this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.matInnerId = this.baleList.matInnerId;
        this.bale = this.baleList.packId;
      },
      showtest() {
        console.log("点击了关闭弹窗");
        this.show = !this.show;
      },
      handerSearch() {
        this.getBaleByPackId();
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_200004",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          packId: this.bale,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                //console.log(dataObj.result);
                if (dataObj.result.length > 1) {
                  this.show = !this.show;
                  this.testList = dataObj.result;
                } else {
                  if (res.data.result.length == 0) {
                    this.$toast("未查询到捆包信息");
                  } else {
                    this.chooseList = dataObj.result;
                    this.baleList = {
                      ...dataObj.result[0]
                    };
                    this.location = this.baleList.locationId;
                    this.matInnerId = this.baleList.matInnerId;
                  }
                }
              } else {
                this.baleList = {};
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async printBale() {
        const params = {
          serviceId: "S_UC_PR_230616",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          matInnerId: this.matInnerId,
          packId: this.baleList.packId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data) {
                //console.log(dataObj.result);
                this.$toast(res.data.__sys__.msg);
                this.baleList = {};
                this.bale = "";
                this.chooseList = [];
              } else {
                this.baleList = {};
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      saveBale() {
        if (this.chooseList && this.chooseList.length > 0) {
          // Dialog.alert({
          //   title: "提示",
          //   message: "正在调用远程打印中",
          // }).then(() => {
          //   // on close
          //   this.printBale();
          // });
          this.printBale();
        } else {
          this.$toast("未查到捆包信息!");
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .store-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }
</style>
