<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">{{ titleName }}</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div style="text-align: left" class="detail-content">
      <div
        class="detail_textarea"
        v-for="(item, index) in searchList"
        :key="index"
      >
        <div class="border_top">
          <div class="content_div">
            <div class="big-font">捆包号：</div>
            <div class="big-font">{{ item.packId }}</div>
          </div>
          <div class="content_div4">
            <div class="div-flex">
              <div class="check-spec">毛重/净重：</div>
              <div class="check-val">
                {{ item.grossWeight }}/{{ item.netWeight }}
              </div>
            </div>
            <div class="div-flex">
              <div class="check-spec">件数：</div>
              <div class="check-val">{{ item.pieceNum }}</div>
            </div>
          </div>
          <div class="content_div">
            <div class="check-spec">库位：</div>
            <div class="check-val">{{ item.locationId }}</div>
          </div>
          <div class="content_div">
            <div class="check-spec">规格：</div>
            <div class="check-val">{{ item.specsDesc }}</div>
          </div>

        </div>
        <div class="gap-class"></div>
      </div>

    </div>
    <div class="mui-input-row" style="margin: 0" @click="printIt">
      <button type="button" class="mui-btn" v-preventReClick="3000">
        远程打印
      </button>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      putinId: "",
      putoutId: "",
      searchList: [],
      titleName: "",
    };
  },
  created() {
    this.putinId = this.$route.query.putinId;
    this.putoutId = this.$route.query.putoutId;
    if (this.$route.query.putinId != null) {
      this.getSearchInList();
      this.titleName = "入库单打印";
    }
    if (this.$route.query.putoutId != null) {
      this.getSearchOutList();
      this.titleName = "出库单打印";
    }
  },
  beforeRouteLeave(to, from, next) {
    to.meta.keepAlive = true;
    next(0);
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    //查询入库单明细
    async getSearchInList() {
      const params = {
        serviceId: "S_UC_PR_230608",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putinId: this.putinId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.searchList = dataObj.result;
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //查询入库单明细
    async getSearchOutList() {
      const params = {
        serviceId: "S_UC_PR_230610",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putoutId: this.putoutId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.searchList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //入库远程打印
    async printIn() {
      const params = {
        serviceId: "S_UC_PR_230618",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putinStackingRecNum: this.putinId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data) {
              this.$toast(res.data.__sys__.msg);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //打印出库单
    async printOut() {
      const params = {
        serviceId: "S_UC_PR_230622",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putoutStackingRecNum: this.putoutId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          console.log(res);
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
              this.$toast(res.data.__sys__.msg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    printIt() {
      if (this.putinId != null) {
        //this.printIn();
        this.$toast("入库单打印");
      }
      if (this.putoutId != null) {
        this.printOut();
        // this.$toast("出库单打印");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.big-font {
  font-size: 16px;
  color: #007aff;
  font-family: Noto Sans SC;
  font-weight: 500;
  line-height: 22px;
}
.check-spec {
  font-size: 15px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 21px;
}
.gap-class {
  background-color: rgb(243, 243, 243);
  height: 12px;
  margin-top: 0px;
  margin-bottom: 0px;
}
.check-val {
  font-size: 16px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}
.div-flex {
  display: flex;
  justify-content: space-between;
}
.detail-content {
  padding-bottom: 120px;
}
</style>
