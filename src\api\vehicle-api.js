/**
 * 引入fetch
 * @param params
 * @returns {*}
 */
import { fetch2, baseUrl } from 'config/service'
// 查询配车单列表
export function vehicleList(params) {
  return fetch2(params)
}
// 查询待入厂车牌号列表
export function inFactoryCarNumber(params) {
  return fetch2(params)
}
// 查询待出厂车牌号列表
export function outFactoryCarNumber(params) {
  return fetch2(params)
}
// 查询出库确认配车单列表
export function getVehicleListReady(params) {
  return fetch2(params)
}
// 验货完成
export function finishInspection(params) {
  return fetch2(params)
}
// 捆包查询
export function getBaleByPackId(params) {
  return fetch2(params)
}
// 入厂
export function updateInFactory(params) {
  return fetch2(params)
}
// 出厂
export function updateOutFactory(params) {
  return fetch2(params)
}