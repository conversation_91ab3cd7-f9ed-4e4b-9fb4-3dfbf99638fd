<template>
  <div>
    <van-nav-bar>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
      <template #title>
        <div class="global-hfont">装卸菜单</div>
      </template>
      <template #right>
        <div class="" @click="goChooseLoad">
          <span class="iconfont icon-a-18zhuangxiehuodidian"></span>
        </div>
      </template>
    </van-nav-bar>
    <div v-if="loadList && loadList.length > 0">
      <div class="in-content">
        <div class="factory-load">
          <div class="factory-title">
            <div>装卸点名称</div>
            <div>容纳车辆数</div>
            <div>启用/停用</div>
          </div>
        </div>
        <div
          class="factory-load-content"
          v-for="(item, index) in loadList"
          :key="index"
        >
          <div class="factory-content">
            <div>{{ item.loadingPointName }}</div>
            <div>
              <van-stepper v-model="item.vehicleWorkingMaxNum" integer />
            </div>
            <div><van-switch v-model="item.enabled" /></div>
          </div>
        </div>
      </div>

      <div class="mui-input-row" style="margin: 0">
        <button
          id="block_button"
          type="button"
          class="mui-btn"
          @click="updateLoadList"
        >
          确&nbsp; &nbsp; &nbsp;&nbsp;定
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂未查到相关列表" />
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      checked: true,
      value: 1,
      loadList: [],
    };
  },
  created() {
    this.getLoadList();
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goChooseLoad() {
      this.$router.togo("/choose");
    },
    async getLoadList() {
      const params = {
        serviceId: "S_UC_PR_200301",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        warehouseCode: localStorage.getItem("warehouseCode"),
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              //console.log("---", dataObj.result);
              this.loadList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async updateLoadList() {
      const chekVal = this.loadList.flat().map((detail2) => {
        return {
          uuid: detail2.uuid,
          vehicleWorkingMaxNum: detail2.vehicleWorkingMaxNum,
          enabled: detail2.enabled,
          loadingPointNo: detail2.loadingPointNo,
          loadingPointName: detail2.loadingPointName
        };
      });
      const params = {
        serviceId: "S_UC_PR_200302",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        rowList: chekVal,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              //console.log("---", dataObj.result);
              this.$toast(dataObj.__sys__.msg);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.factory-load {
  background-color: #fff;
  border-bottom: 1px solid #dcdcdc;
}
.factory-load-content {
  margin-top: 4px;
  background-color: #fff;
  border-bottom: 1px solid #dcdcdc;
}
.factory-title {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 400;
  line-height: 22px;
}
.factory-content {
  padding: 10px;
  display: flex;
  font-size: 15px;
  align-items: center;
  justify-content: space-between;
}
</style>
