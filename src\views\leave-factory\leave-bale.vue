<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">待出厂配车单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <van-search
        v-model="vehicleNo"
        shape="round"
        background="#007aff"
        placeholder="车牌号"
        @search="getVehicleList"
        left-icon=""
      >
        <template #right-icon>
          <div
            class="iconfont icon-sousuo"
            @click="getVehicleList"
            style="color: #999999"
          ></div>
        </template>
      </van-search>
    </van-sticky>

      <div class="load-content" v-if="vehicleList && vehicleList.length > 0">
      <van-radio-group v-model="this.allocateVehicleNo">
        <van-cell-group>
          <van-cell
            v-for="(item, index) in vehicleList"
            :key="index"
            clickable
            @click="isChecked(index, item)"
          >
            <template #title>
              <div class="load-number">{{ item.allocateVehicleNo }}</div>
            </template>
            <template #label>
              <div class="load-name">{{ item.vehicleNo }}</div>
              <div class="load-name">{{ item.tproviderName }}</div>
            </template>
            <template #right-icon>
              <span
                class="iconfont icon-youjiantou"
                style="color: #9f9f9f"
              ></span>
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
    <div v-else>
      <van-empty description="暂未查询到配车单" />
    </div>

    <!-- <div
      class="mui-input-row"
      style="margin: 0"
      @click="onConfirm"
      v-show="isShowBottom"
    >
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;定
      </button>
    </div> -->
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import { Dialog } from "vant";
export default {
  data() {
    return {
      vehicleList: [],
      currentIndex: -1,
      value: "",
      allocateVehicleNo: "",
      vehicleNo: "",
      check: false,
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  created() {
    this.getVehicleList();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.currentIndex = index;
      this.vehicleNo = item.vehicleNo;
      this.allocateVehicleNo = item.allocateVehicleNo;
      //this.check = true;
      this.$router.togo("/baleInfo?vehicleNo=" + this.vehicleNo);
    },
    //查询配车单
    async getVehicleList() {
      const params = {
        serviceId: "S_UC_PR_210104",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        vehicleNo: this.vehicleNo,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              //console.log("---", dataObj.result);
              this.vehicleList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onConfirm() {
      if (this.check) {
        this.$router.togo("/baleInfo?vehicleNo=" + this.vehicleNo);
      } else {
        this.$toast("请选择配送单！");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  padding-bottom: 100px;
}

.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
}

.activeColor {
  color: #007aff;
}
</style>
