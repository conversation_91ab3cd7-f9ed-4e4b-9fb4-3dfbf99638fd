<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择出库单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
        <!-- <template #right>
          <div class="nav-bar-scan all-font-size" @click="showOpen">扫描</div>
        </template> -->

      </van-nav-bar>
      <van-field v-model="packId" label="出库单" placeholder="请扫描或输入出库单号" @keyup.enter.native="getSearchList"
        :rules="[{ required: true, message: '请扫描或输入出库单号' }]" />
    </van-sticky>
    <div class="load-content">
      <div v-if="LadingBillList && LadingBillList.length > 0">
        <div class="baletext2" style="margin-left: 10px; margin-top: 14px">
          已扫出库单合计 :
          <span class="span-count">{{ LadingBillList.length }}</span>
        </div>
        <van-swipe-cell v-for="(item, index) in LadingBillList" :key="index">
          <van-cell>
            <template #title>
              <div class="load-number">{{ item }}</div>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" text="删除" class="delete-bale-btn" @click="deleteItem(index)" />
          </template>
        </van-swipe-cell>
        <div class="mui-input-row" style="margin: 0" @click="onConfim">
          <button type="button" class="mui-btn">
            确&nbsp; &nbsp; &nbsp;&nbsp;定
          </button>
        </div>
      </div>
      <div v-else>
        <van-empty description="暂未扫描出库单号列表" />
      </div>
    </div>

    <van-dialog v-model="show" title="扫描出库单号" :beforeClose="beforeClose" show-cancel-button :width="340">

      <div class="search-list" v-if="searchList && searchList.length > 0">
        <van-checkbox-group v-model="result">
          <van-cell-group>
            <van-cell v-for="(item, index) in searchList" clickable :key="item.putoutId" @click="toggle(item)">
              <template #title>
                <div class="load-number">{{ item.putoutId }}</div>
                <div>仓库编号: {{ item.warehouseCode }}</div>
              </template>
              <template #right-icon>
                <van-checkbox :name="item.putoutId" ref="checkboxes">
                  <template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
      <div v-else>
        <div class="search-list">
          <div class="empty-des">暂未查询到出库单号列表</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        show: false,
        packId: "",
        LadingBillList: [],
        searchList: [],
        carList: [],
        result: [],
        putoutIdList: [],
        ladingBillId: "",
        logisticPlanId: "",
        currentIndex: -1,
        value: "",
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    created() {
      //  this.getVehicleList();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },

      showOpen() {
        this.show = true;
      },
      topSearchFunc() {
        // 之前如果数据全部加载完毕(searchFinshed为true)，本次又点击了搜索，必须要重新将searchFinshed置为false
        // 否则会出现，加载完第一页的数据之后，第二页就部进行数据请求了
        this.show = true;
      },
      //查询出库单列表
      async getSearchList() {
        const params = {
          serviceId: "S_UC_PR_230609",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          putoutId: this.packId,
          createTimeStart: "",
          createTimeEnd: "",
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {

                if (dataObj.result.length > 1) {
                  this.show = true;
                  this.searchList = dataObj.result;
                } else {
                  if(dataObj.result.length == 1){
                    this.carList = this.LadingBillList.concat(dataObj.result[0].putoutId);
                    this.LadingBillList = this.handleArr(this.carList);
                    this.packId = "";
                  }else{
                    this.$toast("未查询到相应出库单");
                  }

                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      onConfim() {
        this.$router.togo({
          name: "checkOutlist",
          params: {
            putoutIdList: this.LadingBillList,
          },
        });
       // this.$router.togo("/checkOutlist");
      },
      handleArr(arr) {
        let brr = [];
        for (let i = 0; i < arr.length; i++) {
          if (!brr.includes(arr[i])) {
            brr.push(arr[i]);
          } else {
            this.$toast("此提单号已添加，请重新扫描其他捆包号");
          }
        }
        return brr;
      },


      isChecked(index, item) {
        this.currentIndex = index;
      },

      deleteItem(index) {
        //删除数组中值
        this.$delete(this.LadingBillList, index);
      },

      async beforeClose(action, done) {
        if (action === "confirm") {
          // 判断验证码是否正确
          this.carList = this.LadingBillList.concat(this.result);
          this.LadingBillList = this.handleArr(this.carList);
          this.packId = "";
          done();
        } else {
          this.result = [];
          done();
        }
      },

      // 选择提单
      async toggle(item) {
        let id = item.putoutId;
        if (this.result.includes(id)) {
          let index = this.result.findIndex(item => item == id);
          this.result.splice(index, 1);
        } else {
          this.result.push(id);
        }
      },

    },
  }
</script>

<style lang="less" scoped>
  .load-content {
    padding-bottom: 120px;
  }

  .activeColor {
    color: #007aff;
  }

  .nav-bar-scan {
    color: #fff;
  }

  .span-count {
    margin-left: 10px;
    color: #0000ff;
  }

  .info-btn {
    overflow: hidden;
    position: fixed;
    bottom: 0;
  }

  .load-cell {
    //margin: 8px;
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // border-radius: 8px;
    border-bottom: 1px solid #dcdcdc;
    line-height: 35px;
  }

  .empty-des {
    text-align: center;
    padding-top: 30px;
    color: #BEBEBE;
  }

  .load-number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .load-name {
    font-size: 14px;
    font-family: Noto Sans SC;
    font-weight: 400;
  }

  .search-list {
    height: 180px;
    border: 1px solid #f2f2f2;
    background-color: #f2f2f2;
    overflow-y: auto;

    // margin-bottom: 80px;
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  /** 遮挡罩 加载动画定位 */
  div.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    div.block {
      width: 100px;
      height: 100px;
      background-color: #fff;
      text-align: center;
      line-height: 100px;
      border-radius: 10px;
    }

  }
</style>
