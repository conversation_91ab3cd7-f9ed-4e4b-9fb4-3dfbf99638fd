<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">装货清单</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="in-content">
      <van-search
        v-model="value"
        shape="round"
        background="#007aff"
        placeholder="配车单"
        @search="onFocusInput"
         left-icon=""
      >
        <template #right-icon>
          <div class="iconfont icon-sousuo" @click="onFocusInput"  style="color:#999999;"></div>
        </template>
      </van-search>
      <div class="inventory-content">
        <div
          v-if="
            loadList !== undefined && loadList != null && loadList.length > 0
          "
        >
          <div class="load-content">
            <div class="load-cell">
              <div class="load-number">PDA000000000000000</div>
              <div class="load-name">上海宝钢新事业发展总公司</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="mui-input-row"
      style="margin: 0"
      @click="goOutStorage"
      v-show="isShowBottom"
    >
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;认
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: "",
      loadList: [],
      previousRouterName: "",
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name === "choose-inventory") {
        vm.loadList = from.params.loadList;
      }
    });
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    onFocusInput() {
      this.$router.togo("/chooseInventory");
    },
    goOutStorage() {
      this.$router.togo("/outstorage");
    },
  },
};
</script>

<style lang="less" scoped>
.inventory-content {
  width: 100%;
  height: 240px;
  margin-top: 6px;
  // margin: 8px;
  border: 1px solid #dcdcdc;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}
.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  margin-top: 8px;
}
</style>