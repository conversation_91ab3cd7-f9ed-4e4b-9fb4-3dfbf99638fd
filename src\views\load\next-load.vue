<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">下一步</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON><PERSON><PERSON>ou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="load-content">
      <button class="mui-btn" @click="goInventory">装 货</button>
      <button class="mui-btn" @click="goInstorage">卸 货</button>
      <button class="mui-btn" @click="goEndLoad">装卸结束</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      carNumber: "",
    };
  },
  created() {
    this.carNumber = this.$route.query.carNumber;
  },
  methods: {
    onClickLeft() {
     // this.$router.goBack();
       this.$router.toReplace("/");
    },
    goEndLoad() {
      this.$router.togo("/endLoad?carNumber=" + this.carNumber);
    },
    goInventory() {
      this.$router.togo("/selectOption");
    },
    goInstorage() {
      this.$router.togo("/selectCar");
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  margin: 20px 20px 8px 20px;
}
</style>