<template>
  <div class="factory-body">
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">选择厂区/仓库</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="deep-van">
      <van-dropdown-menu active-color="#0000ff">
        <van-dropdown-item  ref="item">
          <template #title>
            <div>
              {{ checkFactoryName }}
            </div>
          </template>
          <div v-if="factoryOption && factoryOption.length > 0">
            <van-radio-group v-model="factoryArea">
              <van-cell-group>
                <van-cell
                  v-for="(item, index) in factoryOption"
                  :key="index"
                  clickable
                  @click="isChecked2(index, item)"
                >
                  <template #title>
                    <div class="seg-title">{{ item.factoryAreaName }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item.factoryArea">
                      <template #icon="props">
                        <span
                          class="iconfont"
                          :class="props.checked ? activeIcon : inactiveIcon"
                        ></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </div>
          <div v-else>
            <van-empty description="暂未查到厂区列表" />
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
      <div style="height: 12px"></div>
      <van-dropdown-menu class="select-menu" active-color="#0000ff">
        <van-dropdown-item :title="checkWareName" @open="openItem" ref="item2" >
          <div v-if="warehouseOption && warehouseOption.length > 0">
            <van-radio-group v-model="radio2">
              <van-cell-group>
                <van-cell
                  v-for="(item, index) in warehouseOption"
                  :key="index"
                  clickable
                  @click="isChecked(index, item)"
                >
                  <template #title>
                    <div class="ware-title">{{ item.warehouseCode }}</div>
                  </template>
                  <template #label>
                    <div class="ware-name">{{ item.warehouseName }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="item.warehouseCode"
                      ><template #icon="props">
                        <span
                          class="iconfont"
                          :class="props.checked ? activeIcon : inactiveIcon"
                        ></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </div>
          <div v-else>
            <van-empty description="暂未查到仓库列表" />
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>
    <div class="mui-input-row" style="margin: 0" @click="onClick">
      <button type="button" class="mui-btn">
        确&nbsp; &nbsp; &nbsp;&nbsp;定
      </button>
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      radio2: "",
      show: false,
      checkFactoryName: "选择厂区",
      checkWareName: "选择仓库",
      factoryArea: "",
      factoryName: "",
      wareName: "",
      factoryOption: [],
      warehouseOption: [],
      testclass: "",
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.pathUrl = from.fullPath;
    });
  },
  created() {
    this.getFactoryList();
  },
  methods: {
    //查询厂区列表
    async getFactoryList() {
      // const query = {
      //   serviceId: "S_UC_EP_0168",
      //   empCode: localStorage.getItem("userId"),
      //   accessToken: localStorage.getItem("accessToken"),
      //   segNo: localStorage.getItem("segNo"),
      //   segCname: localStorage.getItem("segName"),
      // };
      // let aaa = await baseApi.baseService(query);
      // console.log(aaa, 'aaaaaa');

//       data: {
//   "__sys__": {
//     "msg": "",
//     "traceId": "144b6a66175438448701200000504",
//     "detailMsg": "",
//     "msgKey": "",
//     "status": 0
//   },
//   "isChangeFlag": "0",
//   "locationId": "A301011",
//   "__version__": "2.0",
//   "warehouseCode": "115740003",
//   "__blocks__": {}
// }
      


      const params = {
        serviceId: "S_UC_PR_200501",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              this.factoryOption = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //查询仓库列表
    async getStoreList() {
      const params = {
        serviceId: "S_UC_PR_200002",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: this.factoryArea,
        factoryAreaName: this.factoryName,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              this.warehouseOption = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.checkWareName = item.warehouseName;
      this.wareName = item.warehouseName;
      this.radio2 = item.warehouseCode;
      this.onClick();
        this.$refs.item2.toggle();
    },
    openItem() {
      if (this.factoryName.length == 0) {
        this.$toast("请先选择厂区");
      }
    },
    isChecked2(index, item) {
      this.checkFactoryName = item.factoryAreaName;
      this.factoryName = item.factoryAreaName;
      this.factoryArea = item.factoryArea;
      this.getStoreList();
      this.warehouseOption = [];
      this.checkWareName = '选择仓库';
      this.wareName = '';
      this.radio2 = '';
      this.$refs.item.toggle();
    },
    isEmptyStr(s) {
      if (s == null || s === "") {
        return true;
      }
      return false;
    },
    onClick() {
      if (this.wareName.length == 0) {
        this.$toast("请选择厂区仓库");
      } else {
        localStorage.setItem("factoryName", this.factoryName);
        localStorage.setItem("factoryArea", this.factoryArea);
        localStorage.setItem("warehouseCode", this.radio2);
        localStorage.setItem("warehouseName", this.wareName);
        sessionStorage.removeItem("pdaLoadName");
        sessionStorage.removeItem("pdaLoadNo");
        if (this.pathUrl === "/") {
          this.$router.goBack();
        } else {
          //如果不是新标签页打开的则直接返回
          this.$router.toReplace("/group");
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-dropdown-menu__item {
  display: flex;
  justify-content: flex-start;
  padding-left: 10px;
}
.factory-body {
  height: 100vh;
  background-color: #f3f3f3;
}
.ware-title {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #007aff;
  line-height: 20px;
}
.ware-name {
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
}
.activeColor {
  color: #007aff;
}
</style>
