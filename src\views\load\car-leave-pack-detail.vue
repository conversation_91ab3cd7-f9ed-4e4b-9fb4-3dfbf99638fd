<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">出库捆包明细</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>

    <!-- 车辆信息 -->
    <van-cell-group title="车辆信息">
      <van-cell title="车牌号" :value="vehicleNo" />
      <!-- <van-cell title="备注" :value="remark" /> -->
      <van-cell title="提单数量" :value="ladingBillList.length + '个'" />
    </van-cell-group>

    <!-- 捆包扫描输入 -->
    <van-field v-model="packId" label="捆包号" placeholder="请扫描或输入捆包号" @keyup.enter.native="scanPack">
    </van-field>

    <div class="load-content">
      <div v-if="packList && packList.length > 0">
        <div class="baletext2">
          已扫捆包合计:
          <span class="span-count">{{ packList.length }}</span>
          件<br />
          净重: <span class="span-count">{{ totalNetWeight }}</span>
          ，毛重: <span class="span-count">{{ totalGrossWeight }}</span>
        </div>

        <van-swipe-cell v-for="(item, index) in packList" :key="index">
          <van-cell>
            <template #title>
              <div class="load-number">{{ item.packId }}</div>
              <div class="pack-info">
                规格: {{ item.specDesc }} |
                净重: {{ item.netWeight }} |
                毛重: {{ item.grossWeight }} |
                件数: {{ item.pieceNum }}
              </div>
              <div class="pack-info">客户: {{ item.settleUserName }}</div>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" text="删除" class="delete-bale-btn" @click="deleteItem(index)" />
          </template>
        </van-swipe-cell>

        <div class="mui-input-row" style="margin: 0" @click="onOutStorage">
          <button type="button" class="mui-btn">
            确认出库
          </button>
        </div>
      </div>
      <div v-else>
        <van-empty description="暂未扫描捆包，请先扫描捆包号" />
      </div>
    </div>

    <!-- 捆包选择弹窗 -->
    <van-dialog v-model="showPackPicker" title="选择捆包" :width="340" show-cancel-button
      :before-close="beforeClosePackPicker">
      <div class="search-list" v-if="availablePackList && availablePackList.length > 0">
        <van-checkbox-group v-model="selectedPackIds">
          <van-cell-group>
            <van-cell v-for="(item, index) in availablePackList" clickable :key="index"
              @click="togglePack(item.packId)">
              <template #title>
                <div class="load-number">{{ item.packId }}</div>
                <div class="pack-info">规格: {{ item.specDesc }}</div>
                <div class="pack-info">净重: {{ item.netWeight }} | 毛重: {{ item.grossWeight }}</div>
                <div class="pack-info">客户: {{ item.settleUserName }}</div>
              </template>
              <template #right-icon>
                <van-checkbox :name="item.packId">
                  <template #icon="props">
                    <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                  </template>
                </van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>

      <div v-else>
        <div class="search-list">
          <div class="empty-des">暂未查询到可出库的捆包信息</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import { isEmpty } from "@/utils/tools.js";
import { Dialog } from "vant";

export default {
  data() {
    return {
      vehicleNo: "",
      // remark: "",
      ladingBillList: [],
      packId: "",
      packList: [],
      showPackPicker: false,
      availablePackList: [],
      selectedPackIds: [],
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
    };
  },
  computed: {
    totalNetWeight() {
      return this.packList.reduce((sum, item) => sum + parseFloat(item.netWeight || 0), 0).toFixed(2);
    },
    totalGrossWeight() {
      return this.packList.reduce((sum, item) => sum + parseFloat(item.grossWeight || 0), 0).toFixed(2);
    },
  },
  created() {
    this.initData();

    // 自动根据提单查询捆包
    this.getPacksByLadingBills();
  },
  methods: {
    // 初始化数据 - 优先从路由参数获取，如果没有则从 sessionStorage 恢复
    initData() {
      console.log(this.$route.params);

      if (this.$route.params.vehicleNo) {
        console.log('路由');

        // 从路由参数获取（正常进入页面）
        this.vehicleNo = this.$route.params.vehicleNo;
        // this.remark = this.$route.params.remark;
        this.ladingBillList = this.$route.params.ladingBillList || [];
      } else {
        console.log('sessionStorage');

        // 从 sessionStorage 恢复车辆信息
        this.restoreVehicleInfo();
        this.ladingBillList = this.$route.params.ladingBillList || [];
      }
    },

    // 从 sessionStorage 恢复车辆信息
    restoreVehicleInfo() {
      try {
        const savedState = sessionStorage.getItem('carLeaveScanLadingState');
        console.log(savedState);

        if (savedState) {
          const state = JSON.parse(savedState);
          this.vehicleNo = state.vehicleNo || "";
          // this.remark = state.remark || "";
        }
      } catch (err) {
        console.error("恢复车辆信息失败:", err);
        this.vehicleNo = "";
        // this.remark = "";
      }
    },

    onClickLeft() {
      this.$router.goBack();
    },

    // 根据提单查询捆包
    async getPacksByLadingBills() {
      if (this.ladingBillList.length === 0) return;

      const params = {
        serviceId: "S_UC_PR_230621",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        factoryArea: localStorage.getItem("factoryArea"),
        ladingBillIdList: this.ladingBillList.map(item => item.ladingBillId),
      };

      try {
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }

        if (res.data.__sys__.status == '-1') {
          this.$toast(res.data.__sys__.msg);
          return;
        }

        if (res.data.result && res.data.result.packList.length > 0) {
          this.availablePackList = res.data.result.packList.map(item => ({
            packId: item.packId,
            specDesc: item.specDesc || item.specsDesc,
            netWeight: item.netWeight,
            grossWeight: item.grossWeight,
            pieceNum: item.pieceNum,
            settleUserName: item.settleUserName,
            matInnerId: item.matInnerId,
            tradeCode: item.tradeCode,
            ladingBillId: item.ladingBillId
          }));

          // 弹出捆包选择框
          this.showPackPicker = true;
        } else {
          this.$toast("未查询到可出库的捆包信息");
        }
      } catch (err) {
        console.error("查询捆包失败:", err);
        this.$toast("查询捆包失败，请重试");
      }
    },

    // 切换捆包选中状态
    togglePack(packId) {
      const index = this.selectedPackIds.indexOf(packId);
      if (index > -1) {
        this.selectedPackIds.splice(index, 1);
      } else {
        this.selectedPackIds.push(packId);
      }
    },

    // 关闭捆包选择弹窗前的处理
    beforeClosePackPicker(action, done) {
      if (action === 'confirm') {
        // 将选中的捆包添加到列表
        const selectedPacks = this.availablePackList.filter(pack =>
          this.selectedPackIds.includes(pack.packId)
        );

        selectedPacks.forEach(pack => {
          const exists = this.packList.some(item => item.packId === pack.packId);
          if (!exists) {
            this.packList.push(pack);
          }
        });

        if (selectedPacks.length > 0) {
          this.$toast(`成功添加 ${selectedPacks.length} 个捆包`);
        }

        this.selectedPackIds = [];
        done();
      } else {
        this.selectedPackIds = [];
        done();
      }
    },

    // 扫描捆包
    async scanPack() {
      if (isEmpty(this.packId)) {
        this.$toast("请输入捆包号!");
        return;
      }

      // 检查是否已经扫描过
      const exists = this.packList.some(item => item.packId === this.packId);
      if (exists) {
        this.$toast("此捆包已扫描，请重新扫描其他捆包号");
        this.packId = "";
        return;
      }

      // 查询捆包信息
      const params = {
        serviceId: "S_UC_PR_200006", // 使用现有的捆包查询接口
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        packId: this.packId
      };

      try {
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }

        if (res.data.__sys__.status == '-1') {
          this.$toast(res.data.__sys__.msg);
          return;
        }

        if (res.data.result && res.data.result.length > 0) {
          const packData = res.data.result[0];

          const newPack = {
            packId: packData.packId,
            specDesc: packData.specDesc || packData.specsDesc,
            netWeight: packData.netWeight,
            grossWeight: packData.grossWeight,
            pieceNum: packData.pieceNum,
            settleUserName: packData.settleUserName,
            matInnerId: packData.matInnerId,
            tradeCode: packData.tradeCode,
            ladingBillId: packData.ladingBillId
          };

          this.packList.unshift(newPack);
          this.$toast("扫描成功");
        } else {
          this.$toast("没有查询到相应捆包信息！");
        }
      } catch (err) {
        console.error("捆包查询失败:", err);
        this.$toast("捆包查询失败，请重试");
      }

      this.packId = "";
    },

    deleteItem(index) {
      this.$delete(this.packList, index);
    },

    // 确认出库
    async onOutStorage() {
      if (this.packList.length === 0) {
        this.$toast("请先扫描捆包!");
        return;
      }

      Dialog.confirm({
        title: '确认',
        message: `确认出库 ${this.packList.length} 个捆包？`,
      })
        .then(async () => {
          const now = new Date();
          const productionDate = now.getFullYear().toString() +
            (now.getMonth() + 1).toString().padStart(2, '0') +
            now.getDate().toString().padStart(2, '0') +
            now.getHours().toString().padStart(2, '0') +
            now.getMinutes().toString().padStart(2, '0') +
            now.getSeconds().toString().padStart(2, '0');

          // 调用出库接口
          const params = {
            serviceId: "S_UC_EP_0155",
            userId: localStorage.getItem("userId"),
            accessToken: localStorage.getItem("accessToken"),
            segNo: localStorage.getItem("segNo"),
            warehouseCode: localStorage.getItem("warehouseCode"),
            factoryArea: localStorage.getItem("factoryArea"),
            vehicleNo: this.vehicleNo,
            packList: this.packList.map(item => ({
              ...item,
              vehicleNo: this.vehicleNo,
              productionDate,
              scanUserId: localStorage.getItem("userId"),
              scanUserName: localStorage.getItem("userName"),
            })),
          };

          try {
            let res = await baseApi.baseService(params);

            if (!res || !res.data) {
              this.$toast("调用失败");
              return;
            }

            if (res.data.__sys__.status == '-1') {
              this.$toast(res.data.__sys__.msg);
              return;
            }

            this.$toast("出库成功!");
            this.$router.replace({
              name: "carLeaveSelectVehicle",
            });
          } catch (err) {
            console.error("出库失败:", err);
            this.$toast("出库失败，请重试");
          }
        })
        .catch(() => {
          // 取消
        });
    },
  },
};
</script>

<style lang="less" scoped>
// 统一字体大小
:deep(.van-cell__title),
:deep(.van-cell__value),
:deep(.van-field__label),
:deep(.van-field__control),
:deep(.van-cell-group__title),
:deep(.van-dialog__header),
:deep(.van-checkbox),
:deep(.van-empty__description) {
  font-size: 15px !important;
}

.load-content {
  padding-bottom: 120px;
}

.nav-bar-scan {
  color: #fff;
}

.span-count {
  margin-left: 5px;
  color: #0000ff;
  font-weight: bold;
  font-size: 15px;
}

.load-number {
  font-size: 15px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}

.pack-info {
  font-size: 15px;
  color: #666;
  margin-top: 4px;
}

.search-list {
  height: 180px;
  border: 1px solid #f2f2f2;
  background-color: #f2f2f2;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}

.empty-des {
  text-align: center;
  padding-top: 30px;
  color: #BEBEBE;
  font-size: 15px;
}

.delete-bale-btn {
  height: 100%;
  font-size: 15px;
}

.mui-input-row {
  padding: 0 15px;

  .mui-btn {
    width: 100%;
    background: #007aff;
    color: #fff;
    border: none;
    padding: 15px;
    border-radius: 4px;
    font-size: 15px;
  }
}

.baletext2 {
  background-color: #fff;
  padding: 10px;
  border-bottom: 1px solid #f2f2f2;
  font-size: 15px;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: normal;
  height: 50px;
  width: 80%;
}

.activeColor {
  color: #007aff;
}
</style>