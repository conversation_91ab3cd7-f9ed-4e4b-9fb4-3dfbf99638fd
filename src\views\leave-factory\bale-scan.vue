<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">捆包出厂扫描</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuojiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="detail_row">
      <div class="" style="width: 90%">
        <hm-input
          placeholder="待扫捆包"
          type="text"
          class="search_input"
          v-model="packId"
          clearable
          @keyup.enter.native="handerSearch($event.target.value)"
        ></hm-input>
      </div>
    </div>
    <div class="in-content">
      <div class="detail_textarea">
        <div class="detail_text" style="padding-bottom: 10px">
          <div class="fourline-blue"></div>
          <div class="baletext2" style="margin-left: 0; margin-top: 14px">
            已扫捆包合计 : <span class="span-count">{{ packList.length }}</span>
          </div>
        </div>
      </div>
      <van-swipe-cell v-for="(item, index) in packList" :key="index">
        <van-cell>
          <template #title>
            <div class="load-number">{{ item }}</div>
          </template>
        </van-cell>
        <template #right>
          <van-button
            square
            type="danger"
            text="删除"
            class="delete-bale-btn"
            @click="deleteItem(index)"
          />
        </template>
      </van-swipe-cell>
      <!-- 扫描捆包列表 -->
    </div>
    <div
      class="mui-input-row"
      style="margin: 0"
      @click="onConfirm"
      v-show="isShowBottom"
    >
      <button id="block_button" type="button" class="mui-btn">确认</button>
    </div>
  </div>
</template>

<script>
import HmInput from "@/components/input.vue";
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
      packId: "",
      show: false,
      fathermsg: "",
      timer: null,
      carList: [],
      allocateVehicleNo: "",
      packList: [],
    };
  },
  mounted() {
    this.allocateVehicleNo = this.$route.query.allocateVehicleNo;
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  components: {
    HmInput,
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goInList() {
      this.$router.togo("/inlist");
    },
    handerSearch(val) {
      this.carList.push(val);
      var arr = this.carList;
      this.packList = Array.from(new Set(this.carList.map((item) => item)));
    },
    deleteItem(index) {
      //删除数组中值
      this.$delete(this.packList, index); //没效果？
    },
    onConfirm() {
      console.log(this.packList);
      this.$route.params.packList = this.packList;
      this.$router.back();
    //  this.$router.togo("/baleInfo?scanInfo=" + this.packList);
    },
  },
};
</script>

<style lang="less" scoped>
.span-count {
  margin-left: 10px;
  color: #0000ff;
}
.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  //margin-top: 8px;
}
.delete-bale-btn {
  height: 44px;
}
</style>