<template>
  <div>
    <!-- <van-nav-bar>
      <template #title>
        <div class="global-hfont">入库扫描</div>
      </template>
    </van-nav-bar> -->

    <!-- 进入页面光标停留输入框，扫描后自动跳转到捆包 -->
    <div class="">
      <van-form>
        <!-- <van-field
          v-model="bale"
          label="捆包"
          clearable
          placeholder="请输入或扫描捆包号"
          @keyup.enter.native="getBaleByPackId"
        /> -->
        <van-field name="radio" label="上传货损" class="all-font-size">
          <template #input>
            <van-radio-group v-model="radio" direction="horizontal">
              <van-radio name="00"><template #icon="props">
                  <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                </template>
                否
              </van-radio>
              <van-radio name="10"><template #icon="props">
                  <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                </template>
                是
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field label="捆包" @keyup.enter.native="getBaleByPackId" class="all-font-size">
          <template #input>
            <input type="search" class="new-field" placeholder="请输入或扫描捆包号" ref="baleref" v-model="bale" />
          </template>
        </van-field>
        <van-field label="库位编码" placeholder="请输入或扫描库位编码" ref="labelsecond" v-model="location" class="all-font-size"
          :readonly="isLocationDisabled" @keyup.enter.native="!isLocationDisabled && searchLocationName">
          <template #right-icon>
            <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE"
                  :style="{ opacity: isLocationDisabled ? 0.5 : 1, cursor: isLocationDisabled ? 'not-allowed' : 'pointer' }"
                  @click="!isLocationDisabled && searchLocation"></span>
          </template>
        </van-field>
        <van-field label="库位" placeholder="库位" v-model="locationName" class="all-font-size" readonly
                   :class="{ 'disabled-field': isLocationDisabled }" />

        <van-field label="规格" placeholder="规格" v-model="baleList.specsDesc" class="all-font-size" readonly />
        <van-field v-model="baleList.noticeMsg" error class="all-font-size" label="提示" placeholder="提示" readonly />
        <!-- <div class="detail_row">
          <div class="fourtext">重/件</div>
          <div style="width: 80%">
            <input class="weight_input" type="text" v-model="baleList.netWeight" readonly />
            <input class="weight_input" type="text" v-model="baleList.pieceNum" readonly />
          </div>
        </div> -->
        <van-field v-model="storeName" label="仓库" class="all-font-size" readonly />
      </van-form>
    </div>
    <!-- <div
      class="mui-input-row"
      style="margin: 0"
      v-show="isShowBottom"
      @click="saveBale"
    >
      <button id="block_button" type="button" class="mui-btn">
        保&nbsp; &nbsp; &nbsp;&nbsp;存
      </button>
    </div> -->
    <van-dialog v-model="showImg" title="上传图片" :beforeClose="beforeClose" show-cancel-button>
      <van-field readonly clickable name="picker" :value="cargoDamageValue" label="货损类型" placeholder="点击选择货损类型"
        @click="showPicker = true" />
      <van-field name="uploader" label="图片上传">
        <template #input>
          <van-uploader v-model="uploaderList" :upload-icon="uploadIcon" @oversize="onOversize" multiple :max-count="5"
            accept="image/*" />
        </template>
      </van-field>

    </van-dialog>
    <van-popup v-model="showPicker" position="bottom">
      <van-checkbox-group v-model="checkboxGroup">
        <van-cell-group>
          <van-cell v-for="(item, index) in cargoDamageList" clickable :key="item" :title="` ${item}`"
            @click="toggle(index)">
            <template #right-icon>
              <van-checkbox :name="item" ref="checkboxes">
                <template #icon="props">
                  <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                </template>
                <!-- </van-radio> -->
              </van-checkbox>
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
      <div>
        <van-button type="info" size="large" @click="onConfrimCheck">确认</van-button>
      </div>
    </van-popup>
    <van-dialog v-model="localtionIdShow" title="库位查询" @confirm="onConfirmItem" @cancel="onCancelItem"
      show-cancel-button>
      <div class="dialog-content">
        <div v-if="locationList && locationList.length > 0">
          <van-list v-model:loading="loading" :finished="finished" :error.sync="error" error-text="请求失败，点击重新加载"
            finished-text="没有更多了" @load="searchLocation">
            <van-radio-group v-model="radio2">
              <van-cell-group>
                <van-cell v-for="(item, index) in locationList" :key="index" clickable @click="isChecked(index, item)">
                  <template #title>
                    <div class="ware-title">{{item.locationId}}</div>
                  </template>
                  <template #label>
                    <div>
                      库位名称：{{ item.locationName }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="index"><template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </van-list>
        </div>
        <div v-else>
          <van-empty description="未查询到相应库位" class="all-font-size" />
        </div>
      </div>
    </van-dialog>
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
  </div>
</template>

<script>
  import uploadIcon from '@/assets/imgs/upload-icon.png'
  import * as baseApi from "@/api/base-api";
  import HmInput from "@/components/input.vue";
  import HmPopup from "@/components/HmPopup.vue";
  import {
    isEmpty
  } from '@/utils/tools';
  export default {
    props: ["inList", "putinType", "showInfo"],
    data() {
      return {
        error: false,
        loading: false,
        finished: false,
        offset: 0,
        bale: "",
        cargoDamageValue: "",
        showPicker: false,
        localtionIdShow: false,
        checkboxGroup: [],
        radio: "00",
        showImg: false,
        storeNumber: "",
        cleanLocationFlag:0, //0库位是否自动清除
        notAutoJoin:0, //"0" 表示自动添加到未匹配列表  "1"表示不自动添加到未匹配列表
        cargoDamageFlag:0,//"0":默认否    "1":默认是
        storeName: "",
        show: "",
        matInnerId: "",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        baleList: {},
        location: "", //库位
        locationName: "", //库位名称
        isLocationDisabled: false, // 库位是否禁用修改
        chooseList: [],
        cargoDamageList: [],
        docList: [],
        testList: [],
        uploaderList: [],
        locationList: [],
        checkLocaltion: {},
        uploadIcon: uploadIcon,
        radio2: -1,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
      };
    },
    watch: {
      showInfo(oldValue, newValue) {
        if (oldValue) {
          this.baleList = {};
          this.chooseList = [];
          this.bale = "";
          this.location = "";
          this.testList = [];
          this.locationName = "";
        }
      },
      location: {
        handler: function() {
          if (isEmpty(this.location)) {
            // 处理数据变化
            this.locationName = "";
          }
        }
      }
    },
    components: {
      HmInput,
      HmPopup,
    },
    created() {
      this.storeName = localStorage.getItem("warehouseName");
      this.searchCargoDamage();
      this.initLocationForKT();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      // KT000000特殊处理：从缓存中初始化库位信息
      initLocationForKT() {
        const segNo = localStorage.getItem("segNo");
        if (segNo === "KT000000") {
          // 从缓存中获取locationId和isChangeFlag
          const locationId = localStorage.getItem("locationId");
          const isChangeFlag = localStorage.getItem("isChangeFlag");

          if (locationId) {
            // 设置库位编码
            this.location = locationId;
            // 设置库位修改权限
            this.isLocationDisabled = isChangeFlag === "0";
            // 查询库位名称
            this.searchLocationNameForKT(locationId);
          }
        }
      },
      // KT000000专用：根据库位编码查询库位名称
      async searchLocationNameForKT(locationId) {
        const params = {
          serviceId: "S_UC_PR_230623",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          locationId: locationId
        };

        try {
          const res = await baseApi.baseService(params);
          if (res.data && res.data.__sys__.status != -1) {
            if (res.data.result && res.data.result.length > 0) {
              this.locationName = res.data.result[0].locationName;
            }
          }
        } catch (err) {
          console.log("查询库位名称失败:", err);
        }
      },
      toggle(index) {
        this.$refs.checkboxes[index].toggle();
      },
      onConfrimCheck() {
        this.showPicker = false;
        this.cargoDamageValue = this.checkboxGroup.toString();
      },
      onClickLeft() {
        this.$router.goBack();
      },
      goInList() {
        this.$router.togo("/inlist");
      },
      isChecked(index, item) {
        this.radio2 = index;
        this.checkLocaltion = item;
      },
      onConfirmItem() {
        this.location = this.checkLocaltion.locationId;
        this.locationName = this.checkLocaltion.locationName;
        this.radio2 = -1;
        this.checkLocaltion = [];
        this.locationList = [];
        this.offset = 0;
        if(this.notAutoJoin == 0){
           this.saveBale();
        }
      },
      onCancelItem() {
        this.locationList = [];
        this.offset = 0;
      },
      // afterRead(file) {
      //   file.status = 'uploading';
      //   file.message = '上传中...';

      //   setTimeout(() => {
      //     file.status = 'failed';
      //     file.message = '上传失败';
      //   }, 1000);
      // },
      async beforeClose(action, done) {
        if (action === "confirm") {
          if (this.uploaderList.length > 0) {
            let base64Img = this.uploaderList.map(f => f.content);
            let isRes = await this.uploadImage(base64Img);
            if (isRes) {
              this.saveList();
            }
            done(isRes);
          } else {
            this.saveList();
            done();
          }

        } else {

          done();
        }
      },
     async saveList() {
        if (this.chooseList && this.chooseList.length > 0) {
          if (isEmpty(this.location)) {
            this.$toast("库位不能为空!");
          } else {
            this.$nextTick(() => {
              this.$refs.baleref.focus();
            });
            //this.saveBale2();
            this.$emit(
              "scanInfo",
              this.chooseList,
              this.baleList.putinType,
              this.location,
              this.locationName,
              this.docList,
              this.checkboxGroup
            );
            if(this.cleanLocationFlag == 1){
              this.location = "";
              this.locationName = "";
            }
            this.clearList();
          }
        } else {
          if (isEmpty(this.bale)) {
            this.$toast("捆包号不能为空!");
          } else {
             let isRes = await this.getSpecialPackId();
            const chekVal2 = [{
              location: this.location,
              locationName: this.locationName,
              packId: this.bale,
              factoryOrderNum: '',
              specsDesc: '',
              specDesc: '',
              netWeight: '',
              pictureDetail: this.docList || [],
              cargoDamageType: this.checkboxGroup || []
            }, ];
            this.$nextTick(() => {
              this.$refs.baleref.focus();
            });
            this.$emit("noInPack", chekVal2);
            // this.addBaleByPackId();
            if(this.cleanLocationFlag == 1){
              this.location = "";
              this.locationName = "";
            }
            this.clearList();
          }
        }
      },
      async saveBale() {
        if (this.chooseList && this.chooseList.length > 0) {
          if (isEmpty(this.location)) {
            this.$toast("库位不能为空!");
          } else {
            //this.saveBale2();
            if (this.radio === "00") {
              this.$nextTick(() => {
                this.$refs.baleref.focus();
              });
              this.$emit(
                "scanInfo",
                this.chooseList,
                this.baleList.putinType,
                this.location,
                this.locationName,
                this.docList,
                this.checkboxGroup
              );
              if(this.cleanLocationFlag == 1){
                this.location = "";
                this.locationName = "";
              }
              this.clearList();
            } else {
              this.showImg = true;
            }

          }
        } else {
          if (isEmpty(this.locationName)) {
            this.$toast("库位或库位名称不能为空!");
            return;
          }
          if (isEmpty(this.bale)) {
            this.$toast("捆包号不能为空!");
          } else {
            let isRes = await this.getSpecialPackId();
            const chekVal2 = [{
              location: this.location,
              locationName: this.locationName,
              packId: this.bale,
              factoryOrderNum: '',
              specsDesc: '',
              specDesc: '',
              netWeight: '',
              pictureDetail: this.docList || [],
              cargoDamageType: this.checkboxGroup || []
            }];
            if (this.radio === "00") {
              this.$nextTick(() => {
                this.$refs.baleref.focus();
              });
              this.$emit("noInPack", chekVal2);
              if(this.cleanLocationFlag == 1){
                this.location = "";
                this.locationName = "";
              }
              this.clearList();
            } else {
              this.showImg = true;
            }

          }
        }

      },
      //校验上传图片大小
      onOversize(file) {
        console.log(file);
        this.$toast("文件大小不能超过5MB");
      },
      clearList() {
        this.baleList = {};
        this.chooseList = [];
        this.bale = "";
        // this.location = "";
        this.testList = [];
        // this.locationName = "";
        this.uploaderList = [];
        this.docList = [];
        this.cargoDamageValue = "";
        this.checkboxGroup = [];
        this.cleanLocationFlag = 0;
        this.notAutoJoin = 0;
      },
      clearLoction() {
        this.location = "";
        this.locationName = "";
      },
      getCheckInfo(val) {
        this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.matInnerId = this.baleList.matInnerId;
        this.bale = this.baleList.packId;
        this.location = this.baleList.locationId;
        this.locationName = this.baleList.locationName;
        if (isEmpty(this.location)) {
          this.changeFocus();
        } else {
          this.saveBale();
        }
        // this.getWarehouseLocation();
      },
      showtest() {
        console.log("点击了关闭弹窗");
        this.show = !this.show;
      },
      handerSearch() {
        this.getBaleByPackId();
      },
      changeFocus() {
        this.$nextTick(() => {
          this.$refs.labelsecond.focus();
        });
      },
      saveBaleInUnplanned() {
        const chekVal2 = [{
          location: this.location,
          locationName: this.locationName,
          packId: this.bale,
        }];
        if (this.radio === "00") {
          this.$nextTick(() => {
            this.$refs.baleref.focus();
          });

          this.$emit("noInPack", chekVal2);
          this.clearList();
        }
      },
      // 上传图片
      async uploadImage(val) {
        const segNo = localStorage.getItem("segNo");
        let currentVal = val;
        if (segNo == 'KA000000' || segNo == 'KB000000') {
          currentVal = await this.compressImages(val);
        }
        const params = {
          serviceId: "S_UC_PR_230808",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          pictureList: currentVal,
        };
        let res = await baseApi.baseService(params);
        if (!res || !res.data) {
          this.$toast('调用接口失败');
          return false;
        }
        this.$toast(res.data.__sys__.msg);
        this.docList = res.data.detail === undefined ? [] : res.data.detail;
        return res.data.__sys__.status != '-1';
      },
      async compressImages(images) {
        const compressedImages = [];
        for (const image of images) {
          const img = new Image();
          img.src = image;
          await new Promise((resolve) => {
            img.onload = () => {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0, img.width, img.height);
              canvas.toBlob((blob) => {
                const reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = () => {
                  compressedImages.push(reader.result);
                  if (compressedImages.length === images.length) {
                    resolve();
                  }
                };
              }, 'image/jpeg', 0.5); // 压缩质量为0.5
            };
          });
        }
        return compressedImages;
      },
      //获取库位推荐
      async getSpecialPackId() {
        const params = {
          serviceId: "S_UC_PR_230638",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          packId: this.bale,
        };
        let res = await baseApi.baseService(params);
        if (!res || !res.data) {
          this.$toast(res.data.__sys__.msg);
          return false;
        }
        this.bale = res.data.result.packId || this.bale;
        return this.bale;
      },
      //根据扫描捆包号获取捆包信息
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_230601",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          packId: this.bale,
          locationId: this.location,
          locationName: this.locationName
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.__sys__.status != -1) {
              this.cleanLocationFlag = res.data.cleanLocationFlag;
              this.notAutoJoin = res.data.notAutoJoin;
              if (res.data.result.length > 1) {
                this.show = !this.show;
                this.testList = res.data.result;
              } else {
                this.chooseList = res.data.result;
                this.baleList = {
                  ...res.data.result[0]
                };
                this.location = this.baleList.locationId;
                this.locationName = this.baleList.locationName;
                if (
                  this.location == "" ||
                  this.location == null ||
                  this.location == undefined
                ) {
                  this.changeFocus();
                } else {
                  this.saveBale();
                }

              }
            } else {
              this.cleanLocationFlag = res.data.cleanLocationFlag || 0;
              this.notAutoJoin = res.data.notAutoJoin || 0;
              this.chooseList = [];
              this.baleList = {};
              this.bale = res.data.packId || this.bale;
              if (
                this.location == "" ||
                this.location == null ||
                this.location == undefined
              ) {
                this.changeFocus();
              } else {
                if(this.notAutoJoin == 0){
                   this.saveBale();
                }

              }
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取库位推荐
      async getWarehouseLocation() {
        const params = {
          serviceId: "S_UC_PR_230611",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          packId: this.bale,
          matInnerId: this.matInnerId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.__sys__.status != -1) {
                this.location = dataObj.result.locationId;
                this.locationName = dataObj.result.locationName;
                if (isEmpty(this.location)) {
                  this.changeFocus();
                } else {
                  this.saveBale();
                }
              } else {
                this.location = "";
                this.locationName = "";
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //查询库位名称
      async searchLocationName() {
        const params = {
          serviceId: "S_UC_PR_230623",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          locationId: this.location,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.location = dataObj.result.locationId;
                this.locationName = dataObj.result.locationName;
                if(this.notAutoJoin == 0){
                   this.saveBale();
                }
               // this.saveBale();
              } else {
                this.location = "";
                this.locationName = "";
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      // searchLocation() {
      //   if (isEmpty(this.location)) {
      //     this.$toast("请输入库位参数查询");
      //   } else {
      //     this.searchLocationId()
      //   }
      // },
      //查询库位
      async searchLocation() {
        this.localtionIdShow = true;
        const params = {
          serviceId: "S_UA_CM_201501",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          segCname: localStorage.getItem("segName"),
          locationId: this.location,
          startSize: this.offset,
          length: 10
        };
        const data = await baseApi.baseService(params);
        if (!data || !data.data) {
          this.error = true;
          this.$toast("网络异常, 请联系管理员!");
          return;
        }
        if (data.data.__sys__.status == '-1') {
          this.$toast(data.data.__sys__.msg);
          return;
        }
        data.data.result.forEach(d => {
          this.locationList.push(d);
        });

        this.offset += 10;
        this.loading = false;
        this.finished = false;
        if (this.offset >= data.data.totalCount) {
          this.finished = true;
        }
      },

      //获取货损列表
      async searchCargoDamage() {
        const params = {
          serviceId: "S_UC_PR_230632",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.cargoDamageList = dataObj.result;
                this.cargoDamageFlag = dataObj.cargoDamageFlag;
                if( dataObj.cargoDamageFlag == 1){
                  this.radio = '10'
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //补充入库计划
      async addBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_230606",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          packId: this.bale,
          location: this.location,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                console.log("res.data", res.data.result);
                this.$toast(dataObj.__sys__.msg);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },

      saveBale2() {
        const chekVal = this.chooseList.flat().map((item) => {
          return {
            putinPlanId: item.putinPlanId,
            voucherNum: item.voucherNum,
            location: this.location,
            packId: item.packId,
            matInnerId: item.matInnerId,
            partId: item.partId,
            contractNum: item.contractNum,
            orderNum: item.orderNum,
            purOrderNum: item.purOrderNum,
            factoryOrderNum: item.factoryOrderNum,
            companyCode: item.companyCode,
            storeType: item.storeType,
            netWeight: item.netWeight,
            grossWeight: item.grossWeight,
            pieceNum: item.pieceNum,
            measureId: item.measureId,
            specsDesc: item.specsDesc,
            heatNum: item.heatNum,
            shopsign: item.shopsign,
            prodCode: item.prodCode,
            prodTypeId: item.prodTypeId,
            prodTypeDesc: item.prodTypeDesc,
            dUserNum: item.dUserNum,
            dUserName: item.dUserName,
            settleUserNum: item.settleUserNum,
            settleUserName: item.settleUserName,
            tradeCode: item.tradeCode,
            qualityGrade: item.qualityGrade,
          };
        });
        this.$emit("scanInfo", chekVal, this.baleList.putinType);
      },
    },
  };
</script>

<style lang="less" scoped>
  .store-name {
    font-size: 15px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .dialog-content {
    width: 100%;
    height: 200px;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .activeColor {
    color: #007aff;
  }

  .disabled-field {
    opacity: 0.6;
  }
</style>
