<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">吊装清单打印</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content">
      <van-field
        v-model="putinId"
        center
        clearable
        label="入库号"
        disabled
      >
      </van-field>
      <van-field
        v-model="operateUser"
        label="入库人"
        disabled
      />
      <van-field
        v-model="vehicleNo"
        label="车牌号"
        disabled
      />
      <van-field
        v-model="operateTime"
        label="时间"
        disabled
      />
      <div class="search-list" v-if="searchList && searchList.length != 0">
        <van-cell-group v-for="(item, index) in searchList" :key="index">
          <van-cell>
            <template #title>
              <span class="load-number">{{ item.packId }} </span>
            </template>
            <template #label>
              <span class="load-name">{{ item.specDesc }}</span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div v-else>
        <van-empty description="暂未查询到清单列表" />
      </div>
    </div>

    <div
      class="mui-input-row"
      style="margin: 0"
      v-show="isShowBottom"
      @click="printIt"
    >
      <button id="block_button" type="button" class="mui-btn"  v-preventReClick="3000">远程打印</button>
    </div>
  </div>
</template>

<script>
import HmInput from "@/components/input.vue";
import * as baseApi from "@/api/base-api";
export default {
  name: "print-hoisting",
  data() {
    return {
      searchVal: "",
      operateUser: "",
      operateTime: "",
      vehicleNo: "",
      putinId: "",
      searchList: [],
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  components: {
    HmInput,
  },
  created() {
    this.putinId = this.$route.query.putinId;
    //  this.vehicleId = this.$route.params.vehicleId;
    // this.factoryArea = this.$route.params.factoryArea;
    this.getSearchList();
  },
  beforeRouteLeave(to, from, next) {
    to.meta.keepAlive = true;
    next(0);
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    //查询待打印吊装清单列表
    async getSearchList() {
      const params = {
        serviceId: "S_UC_PR_230617",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putinStackingRecNum: this.putinId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.detail) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.detail) {
              this.searchList = dataObj.detail;
              this.operateUser = dataObj.operateUser;
              this.operateTime = dataObj.operateTime;
              this.vehicleNo = dataObj.vehicleNo;
              console.log(dataObj.detail);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //远程打印
    async printIt() {
      const params = {
        serviceId: "S_UC_PR_230618",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        putinStackingRecNum: this.putinId,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data) {
              this.$toast(res.data.__sys__.msg);
              // setTimeout(() => {
              //   this.$router.goBack();
              // }, 2000);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-cell {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 16px;
  overflow: hidden;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  font-size: 15px;
  font-weight: 500;
  line-height: 24px;
  background-color: #fff;
}
.font-search {
  font-size: 15px;
}
.search-list {
  width: 99%;
  height: 300px;
  margin-top: 6px;
  // margin: 8px;
  border: 1px solid #dcdcdc;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  /*滚动轴背景颜色*/
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
  }
}
.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
  margin-top: 2px;
}
</style>
