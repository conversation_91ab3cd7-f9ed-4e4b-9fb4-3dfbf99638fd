<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">排队列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON>ou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div v-if="vehicleList !== undefined && vehicleList != null && vehicleList.length > 0">
      <div class="in-content">
        <van-radio-group v-model="currentIndex">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in vehicleList"
              :key="index"
              clickable
              @click="isChecked(index, item)"
            >
              <template #title>
                <div class="load-number">{{ item.vehicleNo }}</div>
                <div class="load-name">{{ item.loadingPointName }}</div>
              </template>
              <template #label> </template>
              <template #right-icon>
                <van-radio :name="index" >
                  <template #icon="props">
                        <span
                          class="iconfont"
                          :class="props.checked ? activeIcon : inactiveIcon"
                        ></span>
                      </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
      <div class="mui-input-row" style="margin: 0">
        <button type="button" class="mui-btn" @click="onConfirm"  v-preventReClick="3000">
          叫&nbsp; &nbsp; &nbsp;&nbsp;号
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无排队列表" />
    </div>

  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import { Dialog } from "vant";
export default {
  data() {
    return {
      vehicleList: [],
      rowList: [],
      currentIndex: -1,
      value: "",
      allocateVehicleNo: "",
      uuid: "",
      vehicleNo: "",
      check: false,
       activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  created() {
    if (localStorage.getItem("factoryArea")) {
      this.getVehicleList();
    } else if (localStorage.getItem("teamName")) {
      Dialog.alert({
        title: "提示",
        message: "您还未选择班组班次",
      }).then(() => {
        // on close
        this.$router.togo("/group");
      });
    } else {
      Dialog.alert({
        title: "提示",
        message: "您还未选择厂区仓库",
      }).then(() => {
        // on close
        this.$router.togo("/factory");
      });
    }
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.currentIndex = index;
      this.check = true;
      this.uuid = item.uuid;
      this.rowList = [];
      //let arr =  this.rowList.push({uuid:item.uuid})
    },
    //排队信息查询
    async getVehicleList() {
      this.vehicleList = [];
      const params = {
        serviceId: "S_UC_PR_220101",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              //console.log("---", dataObj.result);
              this.vehicleList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async onConfirm() {
      this.rowList.push({ uuid: this.uuid });
      if (this.check) {
        const params = {
          serviceId: "S_UC_PR_220102",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          rowList: this.rowList,
        };

        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                  setTimeout(() => {
                    this.getVehicleList();
                  //  this.$router.togo("/");
                }, 1000);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        this.$toast("请选择要叫号的车辆！");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  padding-bottom: 100px;
}

.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 30px;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
}
.activeColor {
  color: #007aff;
}
</style>
