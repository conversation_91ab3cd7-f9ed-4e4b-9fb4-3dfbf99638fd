<template>
  <div class="choose-body">
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">选择装卸点</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON>ou" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div
      v-if="loadList !== undefined && loadList != null && loadList.length > 0"
    >
      <div class="in-content">
        <van-radio-group v-model="currentIndex">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in loadList"
              :key="index"
              clickable
              @click="isChecked(index, item)"
            >
              <template #title>
                 <div class=" load-number">{{ item.loadingPointName }}</div>
                <div class="load-name">{{ item.loadingPointNo }}</div>
                <div class="load-name">
                  装卸类型：
                  <span class="load-type">{{
                    item.loadingType == "10"
                      ? "装"
                      : item.loadingType == "20"
                      ? "装卸"
                      : "卸"
                  }}</span>
                </div>
              </template>
              <template #label> </template>
              <template #right-icon>
                <van-radio :name="index">
                  <template #icon="props">
                    <span
                      class="iconfont"
                      :class="props.checked ? activeIcon : inactiveIcon"
                    ></span>
                  </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
      <div class="mui-input-row" style="margin: 0">
        <button type="button" class="mui-btn" @click="onConfirm">
          确&nbsp; &nbsp; &nbsp;&nbsp;定
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂未查到相关列表" />
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      currentIndex: -1,
      loadList: [],
      check: false,
      loadingPointName: "",
      loadingPointNo: "",
      activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
    };
  },
  created() {
    this.getLoadList();
  },
  methods: {
    //查询装卸点
    async getLoadList() {
      const params = {
        serviceId: "S_UC_PR_200301",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
        warehouseCode: localStorage.getItem("warehouseCode"),
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              this.loadList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.check = true;
      this.currentIndex = index;
      this.loadingPointName = item.loadingPointName;
      this.loadingPointNo = item.loadingPointNo;
    },
    onConfirm() {
      if (this.check) {
        sessionStorage.setItem("pdaLoadName", this.loadingPointName);
        sessionStorage.setItem("pdaLoadNo", this.loadingPointNo);
        this.$router.goBack();
      } else {
        this.$toast("请选择装卸点!");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.choose-body {
  height: 100vh;
  //background-color: #fff;
}
.load-content {
  margin-bottom: 60px;
}
.load-title {
  height: 23px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  margin-left: 16px;
  margin-right: auto;
  margin-top: 20px;
}
.load-check {
  margin-left: 10px;
  height: 23px;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
  line-height: 22px;
  margin-top: 20px;
}
.load-cell {
  margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 25px;
  font-family: Noto Sans SC;
  font-weight:600;
  color: #0000ff;
}
.load-type {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 550;
  color: #0000ff;
}
.load-name {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight:550;
  margin-top: 2px;
}
.activeColor {
  color: #007aff;
}
</style>
