<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">查询列表</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="load-content">
      <button class="mui-btn" @click="goInstorage">
        <div class="search icon"></div>
        入库单查询
      </button>
      <button class="mui-btn" @click="goOutstorage">
        <div class="search icon"></div>
        出库单查询
      </button>
      <button class="mui-btn" @click="goBale">
        <div class="search icon"></div>
        捆包查询
      </button>
      <button class="mui-btn" @click="goInventory">
        <div class="search icon"></div>
        库存查询
      </button>
      <button class="mui-btn" @click="goLable">
        <div class="search icon"></div>
        标签对比
      </button>
     <button class="mui-btn" @click="goUniBoard">
        <div class="search icon"></div>
        优尼看板
      </button>
    </div>
  </div>
</template>

<script>
import { Toast,Dialog } from "vant";
export default {
  data() {
    return {
      codeValue: "",
      code: "",
      lastTime: "",
      nextTime: "",
      lastCode: "",
      nextCode: "",
      dtmainId: "",
    };
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "search-outstorage") {
      from.meta.keepAlive = false;
    }
    if (from.name === "search-instorage") {
      from.meta.keepAlive = false;
    }
        if (localStorage.getItem("factoryArea")) {
      if (localStorage.getItem("teamName") == null) {
        Dialog.alert({
          title: "提示",
          message: "您还未选择班组班次",
        }).then(() => {
          next("/group");
        });
      }
    } else {
      Dialog.alert({
        title: "提示",
        message: "您还未选择厂区仓库",
      }).then(() => {
       next("/factory");
      });
    }
    next();
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    goInstorage() {
      this.$router.togo("/searchInstorage");
    },
    goOutstorage() {
      this.$router.togo("/searchOutstorage");
    },
    goBale() {
      this.$router.togo("/searchBale");
    },
    goLable() {
      this.$router.togo("/lableContrast");
    },
    goUniBoard() {
      this.$router.togo("/uni-board");
    },
    goInventory() {
      this.$router.togo("/search-inventory");
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  margin: 20px 20px 8px 20px;
}
.input {
  height: 30px;
}
.btn {
  margin-top: 15px;
}
.btn button {
  width: 50px;
  height: 30px;
  background-color: #409eff;
  color: #fff;
  border: none;
  border-radius: 4px;
}
</style>
