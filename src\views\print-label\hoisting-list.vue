<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">入库单查询</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuoji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
      <van-search
        v-model="value"
        shape="round"
        background="#007aff"
        placeholder="入库单号"
        @search="getSearchList"
        left-icon=""
      >
        <template #right-icon>
          <div
            class="iconfont icon-sousuo"
            @click="getSearchList"
            style="color: #999999"
          ></div>
        </template>
      </van-search>
    </van-sticky>
    <div v-if="searchList && searchList.length != 0">
      <van-cell-group>
        <van-cell
          v-for="(item, index) in searchList"
          :key="index"
          @click="isChecked(index, item)"
        >
          <template #title>
            <div class="load-number">{{ item.putinId }}</div>
          </template>
          <template #label>
            <div class="load-name">{{ item.settleUserName }}</div>
          </template>
          <template #right-icon>
            <span
              class="iconfont icon-youjiantou"
              style="color: #9f9f9f"
            ></span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <div v-else>
      <van-empty description="暂未查询到入库清单列表" />
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
import { Dialog } from "vant";
export default {
  data() {
    return {
      searchList: [],
      currentIndex: -1,
      value: "",
      allocateVehicleNo: "",
      vehicleNo: "",
      check: false,
      isShowBottom: true, //显示或者隐藏footer
      documentHeight: document.documentElement.clientHeight,
    };
  },
  created() {
      this.getSearchList();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        if (this.documentHeight > document.documentElement.clientHeight) {
          this.isShowBottom = false;
        } else {
          this.isShowBottom = true;
        }
      })();
    };
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.currentIndex = index;

      //  this.$router.push({
      //       name: "transferPlan",
      //       params: {
      //         factoryArea: this.factoryArea,
      //         factoryAreaName :this.factoryName,
      //       }
      //     });
      this.$router.togo("/printHoisting?putinId=" + item.putinId);
    },
    //查询入库单列表
    async getSearchList() {
      const params = {
        serviceId: "S_UC_PR_230607",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        warehouseCode: localStorage.getItem("warehouseCode"),
        putinId: this.value,
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (res.data && res.data.result) {
              this.searchList = dataObj.result;
              if (this.searchList.length == 0) {
                this.$toast("未查询到相应数据");
              }
              //console.log(dataObj.result);
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.load-content {
  padding-bottom: 100px;
}

.load-cell {
  //margin: 8px;
  background-color: #fff;
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // border-radius: 8px;
  border-bottom: 1px solid #dcdcdc;
}
.load-number {
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 500;
  color: #0000ff;
}
.load-name {
  font-size: 14px;
  font-family: Noto Sans SC;
  font-weight: 400;
}
</style>
