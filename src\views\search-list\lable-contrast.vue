<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">标签对比</div>
      </template>
      <template #left>
        <span class="iconfont icon-zu<PERSON>jiantou" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div>
      <!-- <div class="detail_row">
        <div class="fourtext">标签一</div>
        <span class="iconfont icon-shuxian icon-line"></span>
        <hm-input
           class=""
          type="text"
          placeholder=""
          v-model="lableVal1"
          clearable
          @keyup.enter.native="changeFocus('label1')"
        ></hm-input>
      </div>
      <div class="detail_row">
        <div class="fourtext">标签二</div>
        <span class="iconfont icon-shuxian icon-line"></span>
        <hm-input
           class=""
          type="text"
          placeholder=""
          v-model="lableVal2"
            ref="labelsecond"
          clearable
          @keyup.enter.native="changeFocus('label2')"
        ></hm-input>
      </div> -->
       <van-form>
        <van-field
          v-model="lableVal1"
          label="标签一"
          class="all-font-size"
          clearable
          placeholder="标签一"
         @keyup.enter.native="changeFocus('label1')"
        />
        <van-field
          label="标签二"
          class="all-font-size"
          placeholder="标签二"
          v-model="lableVal2"
           ref="labelsecond"
           clearable
            @keyup.enter.native="changeFocus('label2')"
        />
      </van-form>
    </div>
  </div>
</template>

<script>
import { Notify } from "vant";
import HmInput from "@/components/input.vue";
export default {
  data() {
    return {
      lableVal1: "",
      lableVal2: "",
      tipMessage: "",
      show: false,
      themeStyle: "round-button",
      msgFlag: false,
      buttonColor: "#007AFF",
    };
  },
  components: {
    HmInput,
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    changeFocus(obj) {
      if (obj === "label1") {
        this.$nextTick(() => {
          this.$refs.labelsecond.focus();
        });
      } else {
        if (this.lableVal1 == this.lableVal2) {
          Notify({
            type: "success",
            message: "标签一致，通过",
            duration: 2000,
          });
        } else {
          Notify({ type: "danger", message: "标签不一致", duration: 2000 });
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.success-text {
  margin: 10px;
  padding: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  background-color: #00cd00;
  font-family: Noto Sans SC;
  border-radius: 4px;
}
.failure-text {
  margin: 10px;
  padding: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  background-color: #ff0000;
  font-family: Noto Sans SC;
  border-radius: 4px;
}
</style>