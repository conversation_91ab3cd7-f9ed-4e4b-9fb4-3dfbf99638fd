// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import Vant from 'vant';
import 'vant/lib/index.css';
import 'vant/lib/icon/local.css';
import '@/assets/css/admin.css';
import '@/assets/less/navbar.less';
import '../static/js/flexible.js';
import Back from '../static/js/back.js';
import Calendar from 'vue-mobile-calendar';
import { Toast } from 'vant';
// 防止多次点击
import preventReClick from '@/utils/click.js'
import 'babel-polyfill'
import es6promise from 'es6-promise';
es6promise.polyfill()
Vue.use(preventReClick);
Toast.setDefaultOptions({ duration: 2000 });  // duration延迟时间
Vue.use(Toast);
Vue.use(Calendar);
Vue.use(Vant);
// import BaiduMap from 'vue-baidu-map';
// Vue.use(BaiduMap,{
//     ak:'aa3wdMuDXIBlTG4OweRXX13qAfyKsgvp'
// });
//import vConsole from '@/utils/vconsole.js';
Vue.prototype.$plusExtends = fn => {
  if (window.plus) {
    setTimeout(fn, 0)
  } else {
    document.addEventListener('plusready', fn, false)
  }
}
Vue.config.productionTip = false
// 解决点击事件延迟
document.addEventListener('DOMContentLoaded', function () {
  typeof FastClick === 'function' && FastClick.attach(document.body);
}, false);
let offline = null
window.addEventListener('online', () => {
    //offline.close()
    offline = null
    Toast('网络连接恢复');
})
window.addEventListener('offline', () => {
    offline = Toast('网络连接失败');
})
/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  Back,
 // vConsole,
  components: { App },
  template: '<App/>'
})
