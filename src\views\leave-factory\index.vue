<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">车辆出厂信息</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div v-if="carList !== undefined && carList != null && carList.length > 0">
      <div class="in-content">
        <van-radio-group v-model="check">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in carList"
              :key="index"
              clickable
              @click="isChecked(index, item)"
            >
              <template #title>
                <div class="ware-title">{{ item }}</div>
              </template>
              <template #label> </template>
              <template #right-icon>
                <van-radio :name="index" >
                  <template #icon="props">
                        <span
                          class="iconfont"
                          :class="props.checked ? activeIcon : inactiveIcon"
                        ></span>
                      </template>
                </van-radio>
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>

      <div class="mui-input-row" style="margin: 0">
        <button type="button" class="mui-btn" @click="updateOutFactory"  v-preventReClick="3000">
          出&nbsp; &nbsp; &nbsp;&nbsp;厂
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无待出厂车辆" />
    </div>
  </div>
</template>

<script>
import * as baseApi from "@/api/base-api";
export default {
  data() {
    return {
      check: -1,
      carList: [],
      vehicleNo: "",
       activeIcon: "icon-31xuanzhong activeColor",
      inactiveIcon: "icon-weixuanzhong",
    };
  },
  created() {
    this.getOutVehicleList();
  },
  methods: {
    onClickLeft() {
      this.$router.goBack();
    },
    isChecked(index, item) {
      this.check = index;
      this.vehicleNo = item;
    },
    async getOutVehicleList() {
      const params = {
        serviceId: "S_UC_PR_210103",
        userId: localStorage.getItem("userId"),
        accessToken: localStorage.getItem("accessToken"),
        segNo: localStorage.getItem("segNo"),
        segCname: localStorage.getItem("segName"),
        factoryArea: localStorage.getItem("factoryArea"),
        factoryAreaName: localStorage.getItem("factoryName"),
      };
      await baseApi.baseService(params)
        .then((res) => {
          if (res.data && res.data.result) {
            let dataObj = null;
            try {
              dataObj = res.data;
            } catch (e) {
              dataObj = {
                __sys__: {
                  msg: "调用失败",
                  status: -1,
                },
              };
            }
            if (dataObj && dataObj.__sys__.status != -1) {
              this.carList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async updateOutFactory() {
      if (this.vehicleNo.length == 0) {
        this.$toast("请选择车牌号!");
      } else {
        const params = {
          serviceId: "S_UC_PR_230102",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          vehicleNo: this.vehicleNo,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(dataObj.__sys__.msg);
                this.check = -1;
                setTimeout(() => {
                  this.getOutVehicleList();
                }, 1000);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ware-title {
  color: #007aff;
  font-size: 16px;
  font-family: Noto Sans SC;
  font-weight: 550;
}
.activeColor {
  color: #007aff;
}
</style>
