<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">捆包列表</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON>ou" @click="onClickLeft"></span>
        </template>
       <!-- <template #right>
          <div class="global-hfont" @click="checkAll">{{isAllSelected ? '取消全选' :'全选'}}</div>
        </template> -->
      </van-nav-bar>
      <van-field name="radio" label="状态" class="all-font-size">
        <template #input>
          <van-radio-group v-model="isAllSelected" direction="horizontal">
            <van-radio name="true" @click="selectAll"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              全选
            </van-radio>
            <van-radio name="false" @click="unselectAll"><template #icon="props">
                <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
              </template>
              取消全选
            </van-radio>
          </van-radio-group>
        </template>
      </van-field>
    </van-sticky>

    <div v-if="noScanPackList !== undefined && noScanPackList != null && noScanPackList.length > 0" class="home">
      <van-cell-group v-for="(item,index) in noScanPackList" :key="index" class="cell-group">
        <van-cell>
          <template #title>
            <div class="title-packId">
              {{item.packId}}
            </div>
            <div class="div-display">
              <div class="">
                状态
              </div>
              <div class="">
                <van-switch :value="item.selectStatus" @input="onInput(item.selectStatus,index)" size="24px" />
              </div>
            </div>
            <div class="div-display">
              <div class="">
                原始捆包号:{{item.originalPackId}}
              </div>
              <div class="">
                并包号:{{item.unitedPackId}}
              </div>
            </div>


            <div class="div-display">
              <div>
                <van-tag type="warning" size="medium" style="margin-right: 4px;"><span
                    class="tag-title">规</span></van-tag>{{item.specDesc}}
              </div>
              <div>
                <van-tag type="success" size="medium" style="margin-right: 4px;"><span
                    class="tag-title">重</span></van-tag>{{item.netWeight}}
              </div>
              <div>
                <van-tag type="primary" size="medium" style="margin-right: 4px;"><span
                    class="tag-title">件</span></van-tag>{{item.pieceNum}}
              </div>

            </div>
            <div class="div-display" @click="changeLocation(item,index)">
              <div>
                库位编码/名称:
              </div>
              <div style="font-size: 16px;">
                {{item.locationId}}/{{item.locationName}} <span class="iconfont icon-youjiantou"
                  style="color: #9f9f9f"></span>
              </div>
            </div>
          </template>

        </van-cell>

      </van-cell-group>
      <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" @click="onSave">
        <button type="button" class="mui-btn" v-preventReClick="3000">
          保&nbsp; &nbsp; &nbsp;&nbsp; 存
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无捆包列表" />
    </div>

    <van-dialog v-model="locationShow" title="库位变更" :beforeClose="beforeClose" show-cancel-button>
      <van-cell-group>
        <van-field label="库位编码" :required="true" class="all-font-size" @keyup.enter.native="searchLocationName">
          <template #input>
            <input type="search" class="new-field" placeholder="库位编码" ref="locationIdRef" v-model="locationId" />
          </template>
          <template #right-icon>
            <span class="iconfont icon-sousuo" style="width: 10%; color: #BEBEBE" @click="searchLocation"></span>
          </template>
        </van-field>
        <!--  <van-field v-model="locationId" label="库位" placeholder="库位编码" /> -->
        <van-field v-model="locationName" class="all-font-size" label="库位名称" placeholder="库位名称" />
      </van-cell-group>
    </van-dialog>
    <van-dialog v-model="localtionIdShow" title="库位查询" @confirm="onConfirmItem" @cancel="onCancelItem"
      show-cancel-button>
      <div class="dialog-content">
        <div v-if="locationList && locationList.length > 0">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="searchLocation">
            <van-radio-group v-model="radio2">
              <van-cell-group>
                <van-cell v-for="(item, index) in locationList" :key="index" clickable @click="isChecked(index, item)">
                  <template #title>
                    <div class="ware-title">{{item.locationId}}</div>
                  </template>
                  <template #label>
                    <div>
                      库位名称：{{ item.locationName }}</div>
                  </template>
                  <template #right-icon>
                    <van-radio :name="index"><template #icon="props">
                        <span class="iconfont" :class="props.checked ? activeIcon : inactiveIcon"></span>
                      </template>
                    </van-radio>
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
          </van-list>
        </div>
        <div v-else>
          <van-empty description="未查询到相应库位" class="all-font-size" />
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
  import * as baseApi from "@/api/base-api";
  import {
    Dialog
  } from "vant";
  export default {
    data() {
      return {
        offset: 0,
        matInnerId: '',
        locationList: [],
        backList: [],
        vehicleList: [],
        activeNames: [],
        checkLocaltion: {},
        oldLocationId: "",
        oldLocationName: "",
        locationId: "",
        locationName: "",
        ladingBillIdList: [],
        noScanPackList: [],
        rowList: [],
        currentIndex: -1,
        radio2: -1,
        value: "",
        allocateVehicleNo: "",
        uuid: "",
        vehicleNo: "",
        check: false,
        loading: false,
        finished: false,
        localtionIdShow: false,
        locationShow: false,
        isAllSelected: false,
        activeIcon: "icon-31xuanzhong activeColor",
        inactiveIcon: "icon-weixuanzhong",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    created() {
      this.ladingBillIdList = this.$route.params.ladingBillIdList;
      this.getWeightPieceNum();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      changeLocation(item, index) {
        this.currentIndex = index;
        this.locationShow = true;
        this.locationId = item.locationId;
        this.locationName = item.locationName;
      },
      checkAll() {
        this.isAllSelected = !this.isAllSelected;
        // 遍历 items 数组并设置每个元素的 selected 属性
        this.noScanPackList.forEach(item => {
          item.selectStatus = this.isAllSelected;
        });
      },
      //全选
      selectAll() {
        this.noScanPackList.forEach(item => {
          item.selectStatus = true;
        });
      },
      //取消全选
      unselectAll() {
        this.noScanPackList.forEach(item => {
          item.selectStatus = false;
        });
      },
      onInput(val, index) {
        console.log(index);
        this.noScanPackList[index].selectStatus = !this.noScanPackList[index].selectStatus;
        // Dialog.confirm({
        //   title: '提醒',
        //   message: '是否切换开关？',
        // }).then(() => {
        //   this.checked = checked;
        // });
      },
      async beforeClose(action, done) {
        if (action === "confirm") {
          this.noScanPackList[this.currentIndex].locationId = this.locationId;
          this.noScanPackList[this.currentIndex].locationName = this.locationName;
          done();
        } else {
          this.currentIndex = -1;
          done();
        }
      },
      isChecked(index, item) {
        this.radio2 = index;
        this.checkLocaltion = item;
      },
      onCancelItem() {
        this.locationList = [];
        this.offset = 0;
      },
      onConfirmItem() {
        this.locationId = this.checkLocaltion.locationId;
        this.locationName = this.checkLocaltion.locationName;
        this.radio2 = -1;
        this.checkLocaltion = [];
        this.locationList = [];
        this.offset = 0;
        //  this.saveBale();
      },
      async searchLocation() {
        this.localtionIdShow = true;
        const params = {
          serviceId: "S_UA_CM_201501",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          segCname: localStorage.getItem("segName"),
          locationId: this.newLocationId,
          startSize: this.offset,
          length: 10
        };
        const data = await baseApi.baseService(params);
        if (!data || !data.data) {
          this.$toast("网络异常, 请联系管理员!");
          return;
        }
        if (data.data.__sys__.status == '-1') {
          this.$toast(data.data.__sys__.msg);
          return;
        }
        data.data.result.forEach(d => {
          this.locationList.push(d);
        });

        this.offset += 10;
        this.loading = false;
        this.finished = false;
        if (this.offset >= data.data.totalCount) {
          this.finished = true;
        }

      },
      //查询库位名称
      async searchLocationName() {
        const params = {
          serviceId: "S_UC_PR_230623",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          locationId: this.locationId,
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (res.data && res.data.__sys__.status != -1) {
                console.log(dataObj.result);
                this.locationId = dataObj.result.locationId;
                this.locationName = dataObj.result.locationName;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      async getWeightPieceNum() {
        const params = {
          serviceId: "S_UC_PR_230621",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          ladingBillIdList: this.ladingBillIdList
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                // this.noScanPackList = dataObj.result.packList.map(item => ({
                //   ...item,
                //   selectStatus: item.selectStatus == 1 ? true : false, // 将selectStatus转换为布尔值
                // }));
                this.noScanPackList = dataObj.result.packList;
                // this.$toast(dataObj.__sys__.msg);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      onSave() {
        let that = this;
        if (window.plus) {
          // 在这里调用h5+ API
          plus.nativeUI.confirm(`是否保存捆包列表?`, function(e) {
            if (e.index == 0) {
              that.onSaveList();
            } else {
              console.log("You clicked Cancel!");
            }
          }, "提示", ["确认", "取消"]);
        } else {
          Dialog.confirm({
              title: '提示',
              message: `是否保存捆包列表?`,
            })
            .then(() => {
              // on confirm
              that.onSaveList();
            })
            .catch(() => {});
        }



        // let a = this.noScanPackList.map(item => ({
        //   ...item,
        //   selectStatus: item.selectStatus ? 1 : 0, // 将selectStatus转换为布尔值
        // }));
        // console.log("noScanPackList", a);
      },
      async onSaveList() {
        const params = {
          serviceId: "S_UC_PR_230649",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          rowList: this.noScanPackList,
          ladingBillIdList: this.ladingBillIdList
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(dataObj.__sys__.msg);
                let that = this;

                if (window.plus) {
                  plus.nativeUI.confirm(`${dataObj.__sys__.msg} `, function(e) {
                    if (e.index == 0) {
                      that.getWeightPieceNum();
                      //  this.$router.goBack();
                    }
                  }, "提示", ["确认"]);
                } else {
                  Dialog.alert({
                    title: '提示',
                    message: `${dataObj.__sys__.msg} `,
                  }).then(() => {
                    that.getWeightPieceNum();
                    //  this.$router.goBack();
                    // on close
                  });
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },

    },
  };
</script>

<style lang="less" scoped>
  .home {
    padding-bottom: 100px;
    background-color: rgba(243, 243, 243, 1);
  }

  .load-content {
    padding-bottom: 100px;
  }

  .activeColor {
    color: #007aff;
  }

  .div-display {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tag-title {
    padding: 3px;
  }

  .list {
    background-color: #fff;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 25px;
    // border-radius: 8px;
    border: 1px solid #dcdcdc;
  }

  .list-left {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
    margin-left: 5px;
    margin-top: 10px;
  }

  .list-right {}

  .btn {
    margin-top: 5px;
  }

  .number {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
    // margin-left: 5px;
  }

  .title-packId {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 500;
    color: #0000ff;
  }

  .more-des {
    font-size: 15px;
  }

  .cell-group {
    margin-bottom: 8px;
  }

  .dialog-content {
    width: 100%;
    height: 200px;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 2px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }
</style>
