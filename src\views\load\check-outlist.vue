<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">出库单扫描校验</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div>

    </div>
    <van-form>
      <van-field v-model="packId" label="捆包" placeholder="请扫描或输入捆包号" @keyup.enter.native="getBaleByPackId"
        :rules="[{ required: true, message: '请填写用户名' }]" />
    </van-form>
    <div v-if="noScanPackList && noScanPackList.length > 0">
      <div class="check-list" style="margin-left: 10px; margin-top: 14px">
        已扫捆包合计 :
        <span class="span-count">{{ scanCount }}</span>
      </div>
      <div class="check-list" style="margin-left: 10px; margin-top: 14px">
        捆包合计 :
        <span class="span-count">{{ noScanPackList.length }}</span>
      </div>
      <div class="content-cell">

        <van-cell-group v-for="(item, index) in noScanPackList" :key="index">
          <van-cell size="large">
            <template #title>
              <div class="packId-title">{{ item.packId }}</div>
              <div class="load-name">
                重/件：{{item.netWeight }}/{{ item.pieceNum }}
              </div>
            </template>
            <template #label>
              <div>
                规：{{item.specsDesc}}
              </div>
            </template>
            <template #default>
              <!--   <van-tag type="success"  class="tag-class"></van-tag> -->
              <span v-if="item.scanStatus"></span>
              <van-tag type="danger" v-else class="tag-class">未扫描</van-tag>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
        <button type="button" class="mui-btn" v-preventReClick="3000" @click="onOutConfirm">
          出厂确认
        </button>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无出厂捆包清单" />
    </div>

    <van-dialog v-model="showDialog" title="提示" show-cancel-button>
      <van-field v-model="message" rows="1" autosize label="异常信息" type="textarea" placeholder="请输入异常信息" />
    </van-dialog>

    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>

  </div>
</template>

<script>
  import {
    Toast
  } from "vant";
  import HmPopup from "@/components/HmPopup.vue";
  import * as baseApi from "@/api/base-api";
  export default {
    data() {
      return {
        confirmColor: "#0000ff",
        packList: [],
        testList: [],
        putoutIdList: [],
        packId: "",
        labelId:"",
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        allPackQty: '',
        active: 2,
        noScanPackList: [],
        documentHeight: document.documentElement.clientHeight,
        show: false,
        showDialog: false,
        carList: [],
        vehicleNo: "",
        message: "",
      };
    },
    created() {
      this.putoutIdList = this.$route.params.putoutIdList;
      this.getOutList();
    },
    components: {
      HmPopup,
    },
    computed: {
      scanCount: function() {
        let resList = this.noScanPackList.filter((item) => {
          return item.scanStatus;
        });
        return resList.length;
      }
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
    },
    methods: {
      onClickLeft() {
        this.$router.goBack();
      },
      getCheckInfo(val) {
        this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        //this.storeNumber = this.baleList.packId;
        let packIdValue = this.baleList.packId;
        this.changeStatus(packIdValue);
      },
      showtest() {
        this.show = !this.show;
      },
      async onOutConfirm() {
        let resList = this.noScanPackList.filter((item) => {
          return item.scanStatus;
        });
        const params = {
          serviceId: "S_UC_PR_230631",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          putoutIdList: this.putoutIdList,
          rowList: resList
        };
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }
        if (res.data.__sys__.status == '-1') {
          this.$toast(res.data.__sys__.msg);
          return;
        }
        Toast({
          message: res.data.__sys__.msg,
          duration: 3000,
        });
        this.packList = [];
        this.noScanPackList = [];
      },
      onConfirm() {
        //弹出框 是否填异常信息
        this.showDialog = true;
      },
      getScanInfo(val1, val) {
        // this.packList = val;
        let flag = this.packList.some((item) => {
          return item.matInnerId === val1[0].matInnerId && item.tradeCode === val1[0].tradeCode;
        });
        if (flag) {
          this.$toast("此捆包号已扫描，请重新扫描其他捆包号");
          this.packId = "";
        } else {
          this.packList = [...val1, ...this.packList];
          this.noScanPackList[this.noScanPackList.findIndex(n => n.packId == val)].scanStatus = true;
          this.packId = "";

        }
      },
      getBaleByPackId2(val) {
        let flag = this.noScanPackList.some((item) => {
          return item.packId === val;
        });
        if (flag) {
          let status = this.noScanPackList[this.noScanPackList.findIndex(n => n.packId == val)].scanStatus;
          if (status) {
            this.$toast("此捆包号已扫描，请重新扫描其他捆包号");
          } else {
            this.noScanPackList[this.noScanPackList.findIndex(n => n.packId == val)].scanStatus = true;
          }
          this.packId = "";
        } else {
          this.$toast("此捆包号未在出库捆包列表中");
          this.packId = "";
        }
      },
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_230629",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          packId: this.packId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                  let packIdValue = dataObj.result[0].packId;
                  this.changeStatus(packIdValue);
                 // this.getScanInfo(dataObj.result, this.packId)
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      changeStatus(val) {
        this.noScanPackList[this.noScanPackList.findIndex(n => n.packId == val)].scanStatus = true;
        this.packId = "";
      },
      async getOutList() {
        const params = {
          serviceId: "S_UC_PR_230630",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          putoutIdList: this.putoutIdList,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                dataObj.result.forEach(item => {
                  item.scanStatus = false;
                })
                this.noScanPackList = dataObj.result;
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },

    },
  };
</script>

<style lang="less" scoped>
  .inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  .tag-class {
    font-size: 15px;
    padding: 3px;
  }

  .title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
  }

  .packId-title {
    font-size: 18px;
    color: #0000ff;
  }

  .content-cell {
    margin-bottom: 100px;
  }

  .check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
  }

  .check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .div-flex {
    display: flex;
    justify-content: space-between;
  }

  .check-list {
    font-size: 16px;
  }

  .swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete {
    width: 56px;
    height: 97px;
    background: #d33017;
    opacity: 1;
  }
</style>
