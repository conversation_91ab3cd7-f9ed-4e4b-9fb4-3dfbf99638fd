<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">出库捆包清单</div>
        </template>
        <template #left>
          <span class="iconfont icon-zuoji<PERSON>ou" @click="onClickLeft"></span>
        </template>
        <!-- <template #right>
          <span
            v-if="packList && packList.length > 0"
            class="title-add"
            @click="nextLoad"
            >下一步</span
          >
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <out-scan ref="child" :showInfo="show" @scanInfo="getScanInfo" @closeDialog="closeDialog" :carNumber="carNumber"
      :remark="remark" :allNetWeight="formatNumber(residueNetWeight)" :baleListNetWeight="formatNumber(totalNetWeight)"
      :baleListPieceNum="totalPieceNum" :allPieceNum="residuePieceNum" :packScanList="packScanList" :ladingBillIdList="ladingBillIdList" :packIdListString="packIdListString"
      :allPackQty="allPackQty" :scanned="ScannedLength" :notScanned="notScannedLength"></out-scan>
    <van-tabs v-model="active" color="#007aff" style="width: 100%" line-width="60px" offset-top="44px"
      title-active-color="#007aff" sticky>
      <van-tab title="已扫捆包">
        <div v-if="packList && packList.length > 0">
          <div class="inlist-content">
            <div class="detail_textarea">
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  已扫捆包合计 :
                  <span class="span-count">{{ packList.length }}</span>
                </div>
              </div>
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  总件数 :
                  <span class="span-count">{{ totalPieceNum }}</span>
                  总吨数 :
                  <span class="span-count">{{
                    formatNumber(totalNetWeight)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="content-cell">
              <van-swipe-cell v-for="(item, index) in packList" :key="index">
                <div style="text-align: left">
                  <div class="detail_textarea">
                    <div class="detail_text">
                      <div class="fourtext3">{{ item.packId }}</div>
                    </div>
                    <div class="border_top">
                      <div>
                        <div v-if="item.locationId">
                          <span class="check-spec">库位：</span>
                          <span class="check-val">{{ item.locationId }}/{{ item.locationName }}</span>
                        </div>
                        <div>
                          <span class="check-spec">标签：</span>
                          <span class="check-val">{{ item.labelId }}</span>
                        </div>
                        <div>
                          <span class="check-spec">规：</span>
                          <span class="check-val">{{ item.specsDesc }}</span>
                        </div>
                        <div>
                          <span class="check-spec">重/件：</span>
                          <span class="check-val">{{ item.netWeight }}/{{ item.pieceNum }}</span>
                        </div>
                        <div>
                          <span class="check-spec">客：</span>
                          <span class="check-val">{{
                            item.settleUserName
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <template #right>
                  <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
                    <span class="swiper-text">删除</span>
                  </button>
                </template>
              </van-swipe-cell>
            </div>
          </div>
          <div></div>
          <div class="mui-input-row3" v-show="isShowBottom">
            <button id="block_button" type="button" class="mui-btn3" @click="onConfirm">
              确&nbsp; &nbsp; &nbsp;&nbsp; 认
            </button>
            <button id="block_button" type="button" class="mui-btn3" @click="outList" v-preventReClick="3000">
              出&nbsp; &nbsp; &nbsp;&nbsp; 库
            </button>
          </div>
        </div>
        <div v-else>
          <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
            <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfirm">
              确&nbsp; &nbsp; &nbsp;&nbsp; 认
            </button>
          </div>
          <van-empty description="暂无捆包清单，请添加扫描" />
        </div>
      </van-tab>

      <van-tab title="未扫捆包">
        <van-swipe-cell v-for="(item, index) in noScanPackList" :key="index">
          <div style="text-align: left">
            <div class="detail_textarea">
              <div class="detail_text">
                <div class="fourtext3">{{ item.packId }}</div>
              </div>
              <div class="border_top">
                <div>
                  <div v-if="item.locationId">
                    <span class="check-spec">库位：</span>
                    <span class="check-val">{{ item.locationId }}/{{ item.locationName }}</span>
                  </div>
                  <div>
                    <span class="check-spec">标签：</span>
                    <span class="check-val">{{ item.labelId }}</span>
                  </div>
                  <div>
                    <span class="check-spec">规：</span>
                    <span class="check-val">{{ item.specsDesc }}</span>
                  </div>
                  <div>
                    <span class="check-spec">重/件：</span>
                    <span class="check-val">{{ item.netWeight }}/{{ item.pieceNum }}</span>
                  </div>
                  <div>
                    <span class="check-spec">客：</span>
                    <span class="check-val">{{ item.settleUserName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-swipe-cell>
      </van-tab>
    </van-tabs>
    <van-popup :style="{ height: '100%' }" v-model="signShow" get-container="body" position="bottom">
      <signCanvas @close="closeDialog1" :d_h="h" :d_w="w" :title="title2"></signCanvas>
      <!--  <div class="canvans-tip">提示模块</div> -->
    </van-popup>
    <van-popup :style="{ height: '100%' }" v-model="signShow2" get-container="body" position="bottom">
      <signCanvas @close="closeDialog2" :d_h="h" :d_w="w" :title="title1"></signCanvas>
      <!--  <div class="canvans-tip">提示模块</div> -->
    </van-popup>

  </div>
</template>

<script>
  import {
    ImagePreview,
    Dialog,
    Toast
  } from "vant";
  import outScan from "./index.vue";
  import * as baseApi from "@/api/base-api";
  import signCanvas from '@/components/signature.vue'
  import {
    isEmpty
  } from "@/utils/tools";
  export default {
    data() {
      return {
        title1: '司机签名',
        title2: '业务签名',
        signShow: false,
        signShow2: false,
        resultImg: "",
        resultImg2: "",
        putoutIdList: [],
        show: false,
        confirmColor: "#0000ff",
        picWordList: [],
        ladingBillIdList: [],
        packList: [],
        allNetWeight: "",
        allPieceNum: "",
        putoutType: "",
        carNumber: "",
        w: document.documentElement.clientWidth, //实时屏幕宽度
        h: document.documentElement.clientHeight, //实时屏幕高度
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
        allPackQty: "",
        active: 2,
        noScanPackList: [],
        remark: "",
        isAutoPrint: "", //是否自动打印
        shippingDirection:"" //发货方向
      };
    },
    components: {
      outScan,
      signCanvas
    },
    created() {
      this.carNumber = this.$route.params.carNumber;
      this.ladingBillIdList = this.$route.params.ladingBillIdList;
      this.remark = this.$route.params.remark;
      this.shippingDirection = this.$route.params.shippingDirection || "";
      this.getWeightPieceNum();
    },

    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };
      if (window.history && window.history.pushState) {
        // 往历史记录里面添加一条新的当前页面的url
        history.pushState(null, null, document.URL);
        // 给 popstate 绑定一个方法 监听页面刷新
        window.addEventListener("popstate", this.backChange, false); //false阻止默认事件
      }
    },
    beforeRouteLeave(to, from, next) { // 仅适用于本页面不作为H5着陆页的情况
      console.log("to", to);
      next()
    },
    computed: {
      totalPieceNum() {
        let allPiece = this.packList.reduce(
          (sum, e) => sum + Number(e.pieceNum || 0),
          0
        );
        return allPiece;
      },
      totalNetWeight() {
        let allWeight = this.packList.reduce(
          (sum, e) => sum + Number(e.netWeight || 0),
          0
        );
        return allWeight;
      },
      residueNetWeight() {
        return Number(this.allNetWeight) - Number(this.totalNetWeight || 0);
      },
      residuePieceNum() {
        return Number(this.allPieceNum) - Number(this.totalPieceNum || 0);
      },
      ScannedLength() {
        return this.packList.length || 0;
      },
      ScannedLength() {
        return this.packList.length || 0;
      },
      notScannedLength() {
        return this.noScanPackList.length;
      },
      packIdListString(){
         return this.packList.filter(item => item.planParticle == '20' || item.planParticle == '30').map(pack => pack.packId).join(',') ||  '';
      },
      packScanList(){
        return this.packList.map(item => ({
          ladingBillId: item.ladingBillId,
          packId: item.packId
        }));
      },
    },
    methods: {
      onClickLeft() {
        if (this.packList.length > 0 || this.noScanPackList.length > 0) {
          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => {
                window.history.back();
                window.removeEventListener("popstate", this.backChange);
                this.$router.goBack()
              }
              // this.$router.push("/lodingBill?carNumber=" + this.carNumber)
            )
            .catch(() => {});
          return;
        }
        this.$router.goBack();
      },
      backChange() {
        console.log("调用了");
        // 手机自带返回
        if (this.packList.length > 0 || this.noScanPackList.length > 0) {
          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => {
              this.$router.goBack();
            })
            .catch(() => {
              if (window.history && window.history.pushState) {
                // 手机点击了物理返回 然后又选择了取消 这时需要在添加一条记录
                history.pushState(null, null, document.URL);
              }
            });
          return;
        }
        this.$router.goBack();
      },
      onClickRight() {
        this.show = true;
      },

      formatNumber(val) {
        return Math.round(val * 1000) / 1000; // 保留3位小数
      },
      updateListItem() {
        this.$router.goBack();
      },
      nextLoad() {
        Dialog.confirm({
            title: "提示",
            message: "清单里有数据未提交确认进入下一步？"
          })
          .then(() => {
            this.$router.toReplace("/nextLoad?carNumber=" + this.carNumber);
          })
          .catch(() => {

          });

        // this.$router.togo("/nextLoad");
      },
      onConfirm() {
        this.$refs.child.onSave();
      },
      beforeClose(action, done) {
        if (action === "confirm") {
          //进行判断是否添加到入库列表
          this.$refs.child.onSave();
          //没有库位只有捆包号调用另一个接口
          // setTimeout(done, 1000);
          done();
        } else {
          done();
        }
      },
      getScanInfo(val, val2) {
        // this.packList = val;
        this.putoutType = val2;
        let flag = this.packList.some(item => {
          return (
            item.matInnerId === val[0].matInnerId &&
            item.tradeCode === val[0].tradeCode
          );
        });
        if (flag) {
          this.$toast("此捆包号已添加，请重新扫描其他捆包号");
        } else {
          this.packList = [...val, ...this.packList];
          this.noScanPackList.splice(
            this.noScanPackList.findIndex(n => n.packId == val[0].packId),
            1
          );
        }
      },
      closeDialog() {
        this.show = false;
      },

      closeDialog1(e) {
        // 签名赋值 这里的e就是base64了，直接使用
        // 关闭签名框,这里要调用上传图片接口不要直接确认

        if (!isEmpty(e)) {
          this.resultImg = e;
          //  this.uploadImage([e]);
          //这里调用是否有客户签名
          this.getWarningMsg();
          //this.signShow2 = true;
          // this.isCustomerSign();
          //   this.toConfirm();
        }
        this.signShow = false;

      },
      closeDialog2(e) {
        // 签名赋值 这里的e就是base64了，直接使用
        // 关闭签名框,这里要调用上传图片接口不要直接确认

        if (!isEmpty(e)) {
          this.resultImg2 = e;
          //直接调用出库完成
          this.outFinish();
          //this.toConfirm();
        }
        this.signShow2 = false;

      },
      outList() {
        let that = this
        if (window.plus) {
          // 在这里调用h5+ API
          plus.nativeUI.confirm("下列捆包列表确认要出库？", function(e) {
            if (e.index == 0) {
              that.outList2();
            } else {
              console.log("You clicked Cancel!");
            }
          }, "提示", ["确认", "取消"]);
        } else {
          Dialog.confirm({
              title: '提示',
              message: '下列捆包列表确认要出库？',
            })
            .then(() => {
              // on confirm
              that.outList2();
            })
            .catch(() => {
              // on cancel
            });
        }

      },
      outList2() {

        let that = this
        if (that.noScanPackList.length > 0) {
          let packIdList = that.noScanPackList.map(m => m.packId);
          let packIdLists = packIdList.join(',');
          if (packIdList.length > 2) {
            let bb = packIdList.slice(0, 2);
            packIdLists = bb.join(',') + '...';

          }
          if (window.plus) {
            // 在这里调用h5+ API
            plus.nativeUI.confirm(`还有捆包${packIdLists}未扫描，是否继续出库?`, function(e) {
              if (e.index == 0) {
                // if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
                //   that.isPrint();
                // } else {
                //   that.outList3();
                // }
                that.isPrint();
              } else {
                console.log("You clicked Cancel!");
              }
            }, "提示", ["确认", "取消"]);
          } else {
            Dialog.confirm({
                title: '提示',
                message: `还有捆包${packIdLists}未扫描，是否继续出库?`,
              })
              .then(() => {
                // on confirm
                that.isPrint();
                // if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
                //   that.isPrint(); //换掉出库前判断弹出是否打印框
                // } else {
                //   that.outList3();
                // }
              })
              .catch(() => {
                // on cancel
              });
          }
        } else {
          that.isPrint();
          // if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
          //   that.isPrint();
          // } else {
          //   that.outList3();
          // }
        }
      },
      outList1() {
        let that = this;
        if (window.plus) {
          // 在这里调用h5+ API
          plus.nativeUI.confirm("是否确认自动打印？", function(e) {
            if (e.index == 0) {
              that.isAutoPrint = 1;
              that.outList3();
            } else {
              that.isAutoPrint = 0;
              that.outList3();
            }
          }, "提示", ["是", "否"]);
        } else {
          Dialog.confirm({
              title: '提示',
              message: '是否确认自动打印？',
            })
            .then(() => {
              // on confirm
              that.isAutoPrint = 1;
              that.outList3();
            })
            .catch(() => {
              that.isAutoPrint = 0;
              that.outList3();
              // on cancel
            });
        }
      },

      //出库前判断弹出是否打印框
      async isPrint() {
        const params = {
          serviceId: "S_UC_PR_230644",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (dataObj.result == 1) {
                  //弹出打印判断 出库添加参数printFlag = 1
                  this.outList1();
                } else {
                  //直接上传
                  this.outList3();
                }
                this.$toast(res.data.__sys__.msg); //看返回结果
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //普通出库判断是否需要签字
      async isSign() {
        const params = {
          serviceId: "S_UC_PR_230645",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                if (dataObj.result == 1) {
                  //再调用提示方法
                  this.isCustomerSign();
                  // this.getWarningMsg();
                  //弹出需要签名版
                }
                if (dataObj.result == 0) {
                  this.$router.replace('/selectOption');
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //判断是否要业务客户签字
      async isCustomerSign() {
        const params = {
          serviceId: "S_UC_PR_230646",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
                if (dataObj.result == 1) {
                  //再调用提示方法
                  this.signShow = true;
                } else {
                  this.getWarningMsg();
                  //   this.signShow2 = true;
                  //调用完成方法
                  // this.outFinish();
                }
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //普通出库完成
      async outFinish() {
        const params = {
          serviceId: "S_UC_PR_230648",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          putoutIdList: this.putoutIdList,
          picture: this.resultImg2,
          picture2: this.resultImg

        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.$toast(res.data.__sys__.msg);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getWarningMsg() {
        let that = this;
        const params = {
          serviceId: "S_UC_PR_230510",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          vehicleNo: that.carNumber,
          warehouseCode: localStorage.getItem("warehouseCode"),
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                //console.log("---", dataObj.result);
                //    that.warningMsg = dataObj.result.warningMsg;
                if (!isEmpty(dataObj.result.warningMsg)) {
                  if (window.plus) {
                    plus.nativeUI.confirm(dataObj.result.warningMsg, function(e) {
                      if (e.index == 0) {
                        //  that.isCustomerSign();
                        that.signShow2 = true;
                      } else {
                        console.log("You clicked Cancel!");
                      }
                    }, "提示", ["确认", "取消"]);
                  } else {
                    Dialog.confirm({
                      title: '提示',
                      message: dataObj.result.warningMsg,
                    }).then(() => {
                      //that.isCustomerSign();
                      that.signShow2 = true;
                      // on close
                    }).catch(() => {
                      // on cancel
                    });
                  }
                } else {
                  that.signShow2 = true;
                }
              } else {
                that.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async outList3() {

        let that = this
        const params = {
          serviceId: "S_UC_PR_230637",
          iplat_transactionType: 3,
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          putoutType: that.putoutType,
          vehicleNo: that.carNumber,
          shippingDirection: that.shippingDirection,
          remark: that.remark,
          rowList: that.packList,
          printFlag: this.isAutoPrint, //新增字段
          teamId: localStorage.getItem("groupVal"),
          workingShift: localStorage.getItem("classVal"),
          loadingPointNo: sessionStorage.getItem("pdaLoadNo") || "",
          loadingPointName: sessionStorage.getItem("pdaLoadName") || "",
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                if (!isEmpty(sessionStorage.getItem("pdaLoadName"))) {
                  if (window.plus) {
                    plus.nativeUI.confirm(`${dataObj.__sys__.msg},是否进入下一步操作?`, function(e) {
                      if (e.index == 0) {
                        that.$router.togo({
                          name: "endLoad",
                          params: {
                            putoutIdList: dataObj.putoutIdList,
                            carNumber: that.carNumber
                          },
                        });
                        console.log("You clicked OK!");
                      } else {
                        console.log("You clicked Cancel!");
                      }
                    }, "提示", ["确认", "取消"]);
                  } else {
                    Dialog.confirm({
                        title: '提示',
                        message: `${dataObj.__sys__.msg},是否进入下一步操作?`,
                      })
                      .then(() => {
                        // on confirm
                        //this.$router.togo("/endLoad?carNumber=" + this.carNumber);
                        that.$router.togo({
                          name: "endLoad",
                          params: {
                            putoutIdList: dataObj.putoutIdList,
                            carNumber: that.carNumber
                          },
                        });
                      })
                      .catch(() => {
                        // on cancel
                      });
                  }
                } else {
                  that.isSign();
                }
                that.putoutIdList = dataObj.putoutIdList;
                //这里判断接口调用是否需要签字
                that.$toast(dataObj.__sys__.msg);
                that.packList = [];
              } else {
                that.$toast(res.data.__sys__.msg);
                // this.$router.goBack();
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      async getWeightPieceNum() {
        const params = {
          serviceId: "S_UC_PR_230621",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          ladingBillIdList: this.ladingBillIdList
        };
        await baseApi
          .baseService(params)
          .then(res => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1
                  }
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                this.allNetWeight = dataObj.result.totalNetWeight;
                this.allPieceNum = dataObj.result.totalPieceNum;
                this.allPackQty = dataObj.result.totalNum;
                this.noScanPackList = dataObj.result.packList;
                // this.$toast(dataObj.__sys__.msg);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch(err => {
            console.log(err);
          });
      },
      Scrollbottom() {
        let scrollTop =
          document.documentElement.scrollTop || document.body.scrollTop;
        let clientHeight = document.documentElement.clientHeight;
        let scrollHeight = document.documentElement.scrollHeight;
        if (scrollTop + clientHeight >= scrollHeight) {
          console.log("滚动到底部了");
          // this.pageNo++;
          // console.log(this.pageNo);
          // this.fetchData();
        }
      },
      deleteListItem(index, item) {
        //删除数组中值
        this.$delete(this.packList, index);
        this.noScanPackList.unshift(item);
      }
    },
    beforeDestroy() {
      window.removeEventListener("popstate", this.backChange);
    },
  };
</script>

<style lang="less" scoped>
  .inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  /deep/ .van-dialog {
    position: fixed;
    top: 60%;
    left: 50%;
    width: 320px;
    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
  }

  .title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
  }

  .content-cell {
    margin-bottom: 100px;
  }

  .check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
  }

  .check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .div-flex {
    display: flex;
    justify-content: space-between;
  }

  .swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
  }
</style>
