<template>
  <div>
    <van-nav-bar>
      <template #title>
        <div class="global-hfont">盘库</div>
      </template>
      <template #left>
        <span class="iconfont icon-zuo<PERSON><PERSON><PERSON>" @click="onClickLeft"></span>
      </template>
    </van-nav-bar>
    <div class="content">
      <van-form>
        <van-field
          v-model="formData.content"
          label="内容"
          type="textarea"
          placeholder="请输入内容"
          rows="1"
          autosize
        >
          <template #button v-if="radio === '0'">
            <van-button size="small" type="primary" @click="takePhoto" :loading="takingPhoto">
              {{ takingPhoto ? '拍照中...' : '拍照' }}
            </van-button>
          </template>
        </van-field>
      </van-form>

      <!-- 照片预览 -->
      <div v-if="imageBase64" class="photo-preview">
        <h4>拍摄的照片：</h4>
        <img :src="imageBase64" alt="拍摄的照片" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CheckMapDemo',
  data() {
    return {
      imageBase64: null, // 存储拍照的base64数据
      takingPhoto: false, // 拍照状态
      radio: '0', // 默认值
      formData: {
        content: ''
      },
      uploaderList: []
    }
  },
  created() {
    // 监听plusready事件
    document.addEventListener('plusready', this.onPlusReady, false);
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },

    // plus环境就绪
    onPlusReady() {
      console.log('plus environment is ready');
    },

    // 拍照方法
    takePhoto() {
      if (!window.plus) {
        this.$toast('请在APP环境下使用此功能');
        return;
      }

      this.takingPhoto = true;
      try {
        const camera = plus.camera.getCamera();
        camera.captureImage(
          (path) => {
            // 成功回调
            console.log('拍照成功，路径：', path);
            this.convertToBase64(path);
          },
          (error) => {
            // 失败回调
            console.error('拍照失败：', error);
            this.$toast('拍照失败：' + (error.message || '未知错误'));
            this.takingPhoto = false;
          },
          {
            filename: '_doc/camera/', // 保存路径
            index: 1, // 后置摄像头
            format: 'jpg', // 图片格式
            quality: 100 // 图片质量
          }
        );
      } catch (e) {
        console.error('调用相机失败：', e);
        this.$toast('调用相机失败：' + (e.message || '未知错误'));
        this.takingPhoto = false;
      }
    },

    // 将图片转换为base64
    convertToBase64(path) {
      plus.io.resolveLocalFileSystemURL(path, (entry) => {
        entry.file((file) => {
          const reader = new plus.io.FileReader();
          reader.onloadend = (e) => {
            this.imageBase64 = e.target.result;
            // 将base64赋值给uploaderList中content对应的值
            this.formData.content = this.imageBase64;
            // 更新uploaderList
            this.uploaderList = [{
              content: this.imageBase64
            }];
            this.takingPhoto = false;
            console.log('图片转换为base64成功');
          };
          reader.onerror = (e) => {
            console.error('读取文件失败：', e);
            this.$toast('读取图片失败');
            this.takingPhoto = false;
          };
          reader.readAsDataURL(file);
        });
      }, (error) => {
        console.error('解析文件路径失败：', error);
        this.$toast('解析图片失败');
        this.takingPhoto = false;
      });
    }
  },
  beforeDestroy() {
    // 移除plusready事件监听
    document.removeEventListener('plusready', this.onPlusReady);
  }
}
</script>

<style lang="less" scoped>
.content {
  padding: 15px;
  min-height: calc(100vh - 46px);
  background: #f7f8fa;
}

.photo-preview {
  margin-top: 20px;
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  h4 {
    margin: 0 0 10px;
    font-size: 15px;
    color: #323233;
  }

  img {
    width: 100%;
    max-width: 300px;
    height: auto;
    border-radius: 4px;
    display: block;
    margin: 0 auto;
  }
}
</style>
