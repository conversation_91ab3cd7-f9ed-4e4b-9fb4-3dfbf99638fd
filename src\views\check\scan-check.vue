<template>
  <div>
    <van-sticky>
      <van-nav-bar>
        <template #title>
          <div class="global-hfont">盘点扫描</div>
        </template>
        <template #left>
          <span class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>" @click="onClickLeft"></span>
        </template>
      </van-nav-bar>
    </van-sticky>
    <div class="in-content">
      <van-field v-model="stockCheckNum" label="盘库单" class="all-font-size" readonly />
      <van-field label="库位" @keyup.enter.native="searchLocationName" :rules="[{ required: true, message: '请输入或扫描库位' }]"
        class="all-font-size">
        <template #input>
          <input type="search" class="new-field" :disabled="isItDisable" placeholder="请输入或扫描库位" v-model="locationId" />
        </template>
        <template #button>
          <van-button size="small" :type="isItDisable ? dangerType : infoType" @click="lockIt">
            {{ isItDisable ? '已锁定':'未锁定'}}
          </van-button>
        </template>
      </van-field>
      <van-field label="库位名称" class="all-font-size" placeholder="库位名称" v-model="locationName" readonly />
      <van-field v-model="packId" class="all-font-size" label="捆包" ref="labelsecond" clearable placeholder="请输入或扫描捆包号"
        :rules="[{ required: true, message: '请输入或扫描捆包号' }]" @keyup.enter.native="getBaleByPackId" />

      <div class="detail_text" style="padding-bottom: 10px">
        <div class="fourline-blue"></div>
        <div class="baletext2" style="margin-left: 0; margin-top: 14px">
          盘库单总数 :
          <span class="span-count">{{ totalCount }}</span>
        </div>
        <div class="baletext2" style="margin-left: 0; margin-top: 14px">
          未扫捆包总数 :
          <span class="span-count">{{ sweepCount }}</span>
        </div>
      </div>
      <van-tabs v-model="active" color="#007aff" style="width: 100%" line-width="60px" offset-top="44px"
        title-active-color="#007aff" sticky>
        <van-tab title="已匹配" :title-style="fontSize">
          <div v-if="stockCheckList && stockCheckList.length > 0">
            <div class="">
              <div class="detail_textarea">
                <div class="detail_text" style="padding-bottom: 10px">
                  <div class="fourline-blue"></div>
                  <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                    已扫捆包合计 :
                    <span class="span-count">{{ stockCheckList.length }}</span>
                  </div>
                </div>

              </div>
              <div class="">
                <van-swipe-cell v-for="(item, index) in stockCheckList" :key="index">
                  <div style="text-align: left">
                    <div class="detail_textarea">
                      <div class="detail_text">
                        <div class="fourtext3">{{ item.packId }}</div>
                      </div>
                      <div class="border_top">
                        <div class="">
                          <div class="check-spec" style="color:#007aff ;">{{ item.labelId }}</div>
                        </div>
                        <div>
                          <div v-if="item.locationId">
                            <span class="check-spec">库位：</span>
                            <span class="check-val">{{ item.locationId }}/{{item.locationName}}</span>
                          </div>
                          <div>
                            <span class="check-spec">规：</span>
                            <span class="check-val">{{ item.specDesc }}</span>
                          </div>
                          <div>
                            <span class="check-spec">重/件：</span>
                            <span class="check-val">{{ item.netWeight }}/{{item.pieceNum}}</span>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                  <template #right>
                    <!--   <button class="swiper-btn-update" @click="updateListItem(item,index)">
                      <span class="swiper-text">修改</span>
                    </button> -->
                    <button class="swiper-btn-delete" @click="deleteListItem(index, item)">
                      <span class="swiper-text">删除</span>
                    </button>
                  </template>
                </van-swipe-cell>
              </div>
            </div>
            <div>

            </div>
            <div class="mui-input-row3" v-show="isShowBottom">
              <button id="block_button" type="button" class="mui-btn3" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
              <button id="block_button" type="button" class="mui-btn3" @click="uploadOn" v-preventReClick="3000">
                上&nbsp; &nbsp; &nbsp;&nbsp; 传
              </button>
            </div>
          </div>
          <div v-else>
            <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
              <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
            </div>
            <van-empty description="暂无捆包清单，请添加扫描" />
          </div>
        </van-tab>
        <van-tab title="未匹配" :title-style="fontSize">
          <div v-if="noScanPackList && noScanPackList.length > 0">
            <div class="detail_textarea">
              <div class="detail_text" style="padding-bottom: 10px">
                <div class="fourline-blue"></div>
                <div class="baletext2" style="margin-left: 0; margin-top: 14px">
                  未扫捆包合计 :
                  <span class="span-count">{{ noScanPackList.length }}</span>
                </div>
              </div>

            </div>
            <div class="inlist-content">
              <div class="content-cell">
                <van-swipe-cell v-for="(item, index) in noScanPackList" :key="index">
                  <div style="text-align: left">
                    <div class="detail_textarea">
                      <div class="detail_text">
                        <div class="fourtext3">{{ item.packId }}</div>
                      </div>
                      <div class="border_top">
                        <!-- <div>
                            <span class="check-spec">库位：</span>
                            <span class="check-val">{{ item.location }}11111</span>
                          </div> -->
                        <div class="content-spec">
                          <div>
                            <span class="check-spec">库位</span>
                            <span class="check-val">{{ item.locationId }}/{{item.locationName}}</span>
                          </div>
                          <div>
                            <span class="check-spec" style="color: #ff0000">捆包未匹配盘库单</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <template #right>
                    <!-- <button class="swiper-btn-update2" @click="updateListItem">
                        <span class="swiper-text">修改</span>
                      </button> -->
                    <button class="swiper-btn-delete" @click="deleteListItem2(index)">
                      <span class="swiper-text">删除</span>
                    </button>
                  </template>
                </van-swipe-cell>
              </div>
            </div>
            <div class="mui-input-row3" v-show="isShowBottom">
              <button id="block_button" type="button" class="mui-btn3" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
              <button id="block_button" type="button" class="mui-btn3" @click="uploadOn" v-preventReClick="3000">
                上&nbsp; &nbsp; &nbsp;&nbsp; 传
              </button>
            </div>
          </div>
          <div v-else>
            <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
              <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
            </div>
            <van-empty description="暂无捆包清单，请添加扫描" />
          </div>
        </van-tab>
        <van-tab title="未上传" :title-style="fontSize">
          <div v-if="stockPackList && stockPackList.length > 0">
            <div class="">
              <div class="content-cell">
                <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getCheckPackList">
                  <van-swipe-cell v-for="(item, index) in stockPackList" :key="index">
                    <div style="text-align: left">
                      <div class="detail_textarea">
                        <div class="detail_text">
                          <div class="fourtext3">{{ item.packId }}</div>
                        </div>
                        <div class="border_top">
                          <!-- <div>
                           <span class="check-spec">库位：</span>
                           <span class="check-val">{{ item.location }}11111</span>
                         </div> -->
                          <div class="content-spec">
                            <div>
                              <span class="check-spec">库位</span>
                              <span class="check-val">{{ item.locationId }}/{{item.locationName}}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <template #right>
                      <!-- <button class="swiper-btn-update2" @click="updateListItem">
                       <span class="swiper-text">修改</span>
                     </button> -->
                      <button class="swiper-btn-delete" @click="deleteListItem2(index)">
                        <span class="swiper-text">删除</span>
                      </button>
                    </template>
                  </van-swipe-cell>
                </van-list>
              </div>
            </div>
           <div v-if="stockCheckList && stockCheckList.length > 0">
           <div class="mui-input-row3" v-show="isShowBottom">
             <button id="block_button" type="button" class="mui-btn3" @click="onConfrim">
               确&nbsp; &nbsp; &nbsp;&nbsp; 认
             </button>
             <button id="block_button"  type="button" class="mui-btn3" @click="uploadOn" v-preventReClick="3000">
               上&nbsp; &nbsp; &nbsp;&nbsp; 传
             </button>
           </div>
           
           </div>
           <div  v-else>
             <div class="mui-input-row" style="margin: 0" v-show="isShowBottom" >
               <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                 确&nbsp; &nbsp; &nbsp;&nbsp; 认
               </button>
             </div>
           </div>
          </div>
          <div v-else>
            <div class="mui-input-row" style="margin: 0" v-show="isShowBottom">
              <button type="button" class="mui-btn" v-preventReClick="3000" @click="onConfrim">
                确&nbsp; &nbsp; &nbsp;&nbsp; 认
              </button>
            </div>
            <van-empty description="暂无捆包清单" />
          </div>
        </van-tab>
      </van-tabs>


    </div>
    <HmPopup v-if="show" @closetip="showtest()" @checkInfo="getCheckInfo" :arr="testList"></HmPopup>
    <!--  <div class="mui-input-row" v-show="isShowBottom" @click="uploadOn">
      <button id="block_button" type="button" class="mui-btn">
        上&nbsp; &nbsp; &nbsp;&nbsp; 传
      </button>
    </div> -->
  </div>
</template>

<script>
  import HmPopup from "@/components/HmPopup.vue";
  import {
    Dialog
  } from "vant";
  import * as baseApi from "@/api/base-api";
  import {
    isEmpty
  } from '@/utils/tools';
  export default {
    data() {
      return {
        loading: false,
        finished: false,
        page: 1, // 当前页码
        pageSize: 10, // 每页数量
        hasMore: true,
        show: "",
        active: 0,
        fontSize: " font-size: 16px;",
        totalCount: 0,
        sweepCount: 0,
        testList: [],
        noScanPackList: [],
        baleList: {},
        chooseList: [],
        packList: [],
        stockCheckList: [],
        stockPackList: [],
        storeNumber: '',
        stockCheckNum: '',
        packId: '',
        matInnerId: '',
        locationId: '',
        locationName: '',
        infoType: "info",
        dangerType: "danger",
        isItDisable: false,
        isInitialized: false,
        isShowBottom: true, //显示或者隐藏footer
        documentHeight: document.documentElement.clientHeight,
      };
    },
    components: {
      HmPopup
    },
    created() {
      this.stockCheckNum = this.$route.query.stockCheckNum;
      this.getStorageCache();
      this.getStorageCache2();
      this.getCheckList();
      this.getCheckPackList();
    },
    mounted() {
      window.onresize = () => {
        return (() => {
          if (this.documentHeight > document.documentElement.clientHeight) {
            this.isShowBottom = false;
          } else {
            this.isShowBottom = true;
          }
        })();
      };

      if (window.history && window.history.pushState) {
        // 往历史记录里面添加一条新的当前页面的url
        history.pushState(null, null, document.URL);
        // 给 popstate 绑定一个方法 监听页面刷新
        window.addEventListener("popstate", this.backChange, false); //false阻止默认事件
      }
    },
    watch: {
      locationId(newVal, oldVal) {
        if (this.isInitialized) {
          // 处理数据变化
          this.locationName = "";
        } else {
          this.isInitialized = true;
        }
      },
      // locationId: {
      //   handler: function() {
      //     this.locationName = "";
      //   }
      // }
    },
    methods: {
      onClickLeft() {
        if (this.stockCheckList.length > 0 || this.noScanPackList.length > 0) {
          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => this.$router.push('/checkList'))
            .catch(() => {});
          return;
        }
        this.$router.goBack();

      },
      backChange() {
        // 手机自带返回
        if (this.stockCheckList.length > 0 || this.noScanPackList.length > 0) {

          Dialog.confirm({
              title: "提示",
              message: "清单里有数据未提交确认退出？"
            })
            .then(() => {
              this.$router.goBack();
            })
            .catch(() => {
              if (window.history && window.history.pushState) {
                // 手机点击了物理返回 然后又选择了取消 这时需要在添加一条记录
                history.pushState(null, null, document.URL);
              }
            });
          return;
        }
        this.$router.goBack();

      },
      lockIt() {
        this.isItDisable = !this.isItDisable;
      },
      getCheckInfo(val) {
        console.log("接收到了传过来的值", val);
        this.show = !this.show;
        this.chooseList = val;
        this.baleList = {
          ...val[0]
        };
        this.packId = this.baleList.packId;
        this.locationName = this.baleList.locationName;
        this.locationId = this.baleList.locationId;
        this.matInnerId = this.baleList.matInnerId;
      },
      showtest() {
        console.log("点击了关闭弹窗");
        this.show = !this.show;
      },

      //查询库位名称
      async searchLocationName() {
        const params = {
          serviceId: "S_UC_PR_230623",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          factoryArea: localStorage.getItem("factoryArea"),
          type: "inventory",
          locationId: this.locationId,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data && res.data.result) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (res.data && res.data.result) {
                this.locationId = dataObj.result.locationId;
                this.locationName = dataObj.result.locationName;
                this.$nextTick(() => {
                  this.$refs.labelsecond.focus();
                });
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            } else {
              this.$toast(res.data.__sys__.msg);
              this.locationId = "";
              this.locationName = "";
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取盘库列表
      async getCheckList() {
        const params = {
          serviceId: "S_UC_PR_230633",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.stockCheckNum,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data.__sys__.status != -1) {
              this.sweepCount = res.data.sweepCount;
              this.totalCount = res.data.totalCount;
              // this.noScanPackList = dataObj.result;
            } else {
              this.$toast(res.data.__sys__.msg);
            }

          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取未扫描捆包列表
      async getCheckPackList() {
        const params = {
          serviceId: "S_UC_PR_230664",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.stockCheckNum,
          pageIndex: this.page,
          pageSize: this.pageSize,
        };
        const data = await baseApi.baseService(params);
        if (!data || !data.data) {
          this.$toast("网络异常, 请联系管理员!");
          return;
        }
        data.data.result.forEach(d => {
          this.stockPackList.push(d);
        });

        this.page++;
        this.loading = false;
        this.finished = false;
        if (data.data.result.length == 0) {
          this.finished = true;
        }
        this.$toast(data.data.__sys__.msg);
      },
      async uploadOn() {
        const params = {
          serviceId: "S_UC_PR_230615",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          warehouseName: localStorage.getItem("warehouseName"),
          factoryArea: localStorage.getItem("factoryArea"),
          factoryAreaName: localStorage.getItem("factoryName"),
          // iplat_transactionType: 3,
          stockCheckNum: this.stockCheckNum,
          list: this.stockCheckList,
          list2: this.noScanPackList,
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && dataObj.__sys__.status != -1) {
                // this.$toast(res.data.__sys__.msg);
                this.packId = "";
                this.stockCheckList = [];
                this.noScanPackList = [];
                this.page = 1;
                let _this = this
                Dialog.alert({
                  title: '提示',
                  message: res.data.__sys__.msg,
                  beforeClose: this.beforeClose,
                });
                setTimeout(function() {
                  _this.deleteStorageCache();
                  _this.deleteStorageCache2();
                  _this.getReloadCheckPackList();
                  // 需要执行的代码
                }, 1000);
              } else {
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      //获取未扫描捆包列表
      async getReloadCheckPackList() {
        const params = {
          serviceId: "S_UC_PR_230664",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          warehouseName: localStorage.getItem("warehouseName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.stockCheckNum,
          pageIndex: 1,
          pageSize: 10,
        };
        const data = await baseApi.baseService(params);
        if (!data || !data.data) {
          this.isLoading = false;
          this.$toast("网络异常, 请联系管理员!");
          return;
        }
        this.stockPackList = data.data.result;
        this.page++;
        this.isLoading = false;
      },
      beforeClose(action, done) {
        let _this = this;
        if (action === 'confirm') {
          setTimeout(function() {
            _this.getCheckList();
            // 需要执行的代码
          }, 3000);
          setTimeout(done, 3000);
        } else {
          done();
        }
      },
      onConfrim() {
        if (this.isItDisable) {
          //   console.log("suod");
          if (isEmpty(this.packId)) {
            this.$toast("捆包号不能为空!");
          } else {
            if (Object.keys(this.baleList).length == 0) {
              //this.$toast("已添加到未匹配到盘库单!");
              if (localStorage.getItem("segNo") == 'GA000000') {
                let result = this.packId.split("$")[0]; //使用""作为分隔符分割字符串，并取第一个元素
                this.addNoPackInList(result);
              } else {
                this.addNoPackInList(this.packId);
              }
              // this.addNoPackInList(this.packId);
            } else {
              this.handleVal(this.packId);
            }

          }
        } else {
          if (isEmpty(this.locationName)) {
            this.$toast("库位或库位名称不能为空!");
          } else {
            if (isEmpty(this.packId)) {
              this.$toast("捆包号不能为空!");
            } else {
              if (Object.keys(this.baleList).length == 0) {
               // this.$toast("已添加到未匹配到盘库单!");
                if (localStorage.getItem("segNo") == 'GA000000') {
                  let result = this.packId.split("$")[0]; //使用""作为分隔符分割字符串，并取第一个元素
                  this.addNoPackInList(result);
                } else {
                  this.addNoPackInList(this.packId);
                }
                // this.addNoPackInList(this.packId);
              } else {
                this.handleVal(this.packId);
              }

            }
          }
        }
      },
      handleVal(val) {
        if (isEmpty(val)) {
          return;
        }
        let status = this.stockCheckList.some((item) => {
          return item.packId === val || item.labelId === val;
        });
        if (status) {
          this.$toast("此捆包号已扫描，请重新扫描其他捆包号");
          this.packId = "";
          this.baleList = {};
        } else {
          const scanVal = [{
            packId: Object.keys(this.baleList).length != 0 ? this.baleList.packId : this.packId,
            matInnerId: Object.keys(this.baleList).length != 0 ? this.baleList.matInnerId : "",
            labelId: Object.keys(this.baleList).length != 0 ? this.baleList.labelId : "",
            locationId: this.isItDisable ? this.baleList.locationId : this.locationId,
            locationName: this.isItDisable ? this.baleList.locationName : this.locationName,
            specDesc: Object.keys(this.baleList).length != 0 ? this.baleList.specDesc : "",
            netWeight: Object.keys(this.baleList).length != 0 ? this.baleList.netWeight : "",
            pieceNum: Object.keys(this.baleList).length != 0 ? this.baleList.pieceNum : "",
            tradeCode: Object.keys(this.baleList).length != 0 ? this.baleList.tradeCode : "",
            settleUserNum: Object.keys(this.baleList).length != 0 ? this.baleList.settleUserNum : "",
            settleUserName: Object.keys(this.baleList).length != 0 ? this.baleList.settleUserName : "",
          }];
          this.stockCheckList = [...scanVal, ...this.stockCheckList];
          console.log(this.stockCheckList);
          this.packId = "";
          this.baleList = {};
        }
      },
      deleteListItem(index, item) {
        //删除数组中值
        this.$delete(this.stockCheckList, index);
      },

      deleteListItem2(index) {
        this.$delete(this.noScanPackList, index);
      },
      async addStorageCache() {
        const params = {
          serviceId: "S_UC_PR_270101",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "02",
          billNo: this.stockCheckNum,
          rowList: this.stockCheckList,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data.__sys__.status == '-1') {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getStorageCache() {
        const params = {
          serviceId: "S_UC_PR_270102",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "02",
          billNo: this.stockCheckNum,
        };
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }

        if (res.data.__sys__.status == '-1') {
          this.$toast(res.data.__sys__.msg);
          this.stockCheckList = [];
          return;
        }

        if (res.data.result.length == 0) { // 本次没有数据
          this.stockCheckList = [];
        } else { // 本次有数据
          this.stockCheckList = res.data.result;
        }
      },
      async deleteStorageCache() {
        const params = {
          serviceId: "S_UC_PR_270103",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "02",
          billNo: this.stockCheckNum,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data.__sys__.status == '-1') {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async addStorageCache2() {
        const params = {
          serviceId: "S_UC_PR_270101",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "03",
          billNo: this.stockCheckNum,
          rowList: this.noScanPackList,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data.__sys__.status == '-1') {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getStorageCache2() {
        const params = {
          serviceId: "S_UC_PR_270102",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "03",
          billNo: this.stockCheckNum,
        };
        let res = await baseApi.baseService(params);

        if (!res || !res.data) {
          this.$toast("调用失败");
          return;
        }

        if (res.data.__sys__.status == '-1') {
          this.$toast(res.data.__sys__.msg);
          this.noScanPackList = [];
          return;
        }

        if (res.data.result.length == 0) { // 本次没有数据
          this.noScanPackList = [];
        } else { // 本次有数据
          this.noScanPackList = res.data.result;
        }
      },
      async deleteStorageCache2() {
        const params = {
          serviceId: "S_UC_PR_270103",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          factoryArea: localStorage.getItem("factoryArea"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          type: "03",
          billNo: this.stockCheckNum,
        };
        await baseApi.baseService(params)
          .then((res) => {
            if (res.data.__sys__.status == '-1') {
              this.$toast(res.data.__sys__.msg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      async getBaleByPackId() {
        const params = {
          serviceId: "S_UC_PR_230634",
          userId: localStorage.getItem("userId"),
          accessToken: localStorage.getItem("accessToken"),
          segNo: localStorage.getItem("segNo"),
          segCname: localStorage.getItem("segName"),
          warehouseCode: localStorage.getItem("warehouseCode"),
          stockCheckNum: this.stockCheckNum,
          packId: this.packId,
        };
        await baseApi
          .baseService(params)
          .then((res) => {
            if (res.data) {
              let dataObj = null;
              try {
                dataObj = res.data;
              } catch (e) {
                dataObj = {
                  __sys__: {
                    msg: "调用失败",
                    status: -1,
                  },
                };
              }
              if (dataObj && res.data.__sys__.status != -1) {
                 this.packId = dataObj.packId;
                this.baleList = dataObj.result;
                if (typeof this.baleList === 'undefined' || this.baleList === null) {
                  this.$toast("已添加到未匹配到盘库单!");
                  this.baleList = {};
                  if (isEmpty(this.locationName)) {
                    this.$toast("库位或库位名称不能为空!");
                  } else {
                    if (isEmpty(this.packId)) {
                      this.$toast("捆包号不能为空!");
                    } else {
                      if (localStorage.getItem("segNo") == 'GA000000') {
                        let result = this.packId.split("$")[0]; //使用""作为分隔符分割字符串，并取第一个元素
                        this.addNoPackInList(result);
                      } else {
                        this.addNoPackInList(this.packId);
                      }

                    }
                  }
                } else {
                  if (this.isItDisable) {
                    if (isEmpty(this.baleList.locationName)) {
                      this.$toast("库位或库位名称不能为空!");
                    } else {
                      this.handleVal(this.packId);
                    }
                  } else {
                    if (isEmpty(this.locationName)) {
                      this.$toast("库位或库位名称不能为空!");
                    } else {
                      this.handleVal(this.packId);
                    }

                  }
                }
                //   this.onConfrim();
              } else {
                 this.packId = dataObj.packId;
                this.$toast(res.data.__sys__.msg);
              }
            }
          })
          .catch((err) => {
            console.log(err);
          });
      },
      addNoPackInList(val) {
        let status = this.noScanPackList.some((item) => {
          return item.packId === val;
        });
        if (isEmpty(this.locationName)) {
          this.$toast("库位或库位名称不能为空!");
          return;
        }
        if (status) {
          this.$toast("此捆包号已扫描，请重新扫描其他捆包号");
          this.packId = "";
        } else {
          const chekVal = [{
            locationId: this.locationId,
            locationName: this.locationName,
            packId: val,
          }];
          this.$toast("已添加到未匹配列表!");
          this.noScanPackList = [...chekVal, ...this.noScanPackList];
          console.log(this.noScanPackList);
          this.packId = "";
        }

      }
    },
    beforeDestroy() {
      window.removeEventListener("popstate", this.backChange);
      if (this.stockCheckList.length > 0) {
        this.addStorageCache();
      }
      if (this.noScanPackList.length > 0) {
        this.addStorageCache2();

      }
    },
  };
</script>

<style lang="less" scoped>
  .bottom-btn {
    display: flex;
    justify-content: space-between;
    margin: 20px 16px 0 16px;
  }

  .btn {
    width: 146px;
    height: 48px;
    background: #007aff;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
  }

  .span-btn {
    width: 32px;
    height: 23px;
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    line-height: 22px;
  }

  .inlist-content {
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    /*滚动轴背景颜色*/
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
    }
  }

  /deep/ .van-dialog {
    position: fixed;
    top: 60%;
    left: 50%;
    width: 320px;
    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
  }

  .title-add {
    font-size: 16px;
    font-family: Noto Sans SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 25px;
  }

  .content-cell {
    margin-bottom: 100px;
  }

  .check-spec {
    font-size: 15px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 21px;
  }

  .check-val {
    font-size: 16px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }

  .content-spec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .div-flex {
    display: flex;
    justify-content: space-between;
  }

  .swiper-text {
    letter-spacing: 2px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 20px;
  }

  .swiper-btn-update {
    width: 56px;
    height: 97px;
    background: #0000ff;
    opacity: 1;
  }

  .swiper-btn-delete {
    width: 56px;
    height: 100%;
    background: #d33017;
    opacity: 1;
  }
</style>
